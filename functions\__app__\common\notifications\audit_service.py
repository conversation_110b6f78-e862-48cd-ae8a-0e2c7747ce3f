"""
Notification Audit and Observability Service.
Provides comprehensive audit trail and metrics for notification system.
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

from ..models import (
    NotificationAudit,
    NotificationTrigger,
    StakeholderRoute,
    NotificationStatus,
    NotificationMetrics,
    TriggerType,
    RecipientType,
    DeliveryMethod
)
from ..utils.config import Config
from ..utils.logging import log_structured, log_performance_metric, log_business_event

logger = logging.getLogger(__name__)


class NotificationAuditService:
    """
    Comprehensive audit and observability service for notifications.
    
    Features:
    - Complete audit trail for all notification actions
    - Performance metrics and SLA monitoring
    - Business event tracking
    - Idempotent operation tracking
    - Application Insights integration
    """
    
    def __init__(self, config: Config):
        self.config = config
        self._audit_records: Dict[str, NotificationAudit] = {}
        self._metrics_cache: Dict[str, Any] = {}
        
    def create_audit_record(
        self,
        trigger: NotificationTrigger,
        route: StakeholderRoute,
        notification_id: Optional[str] = None
    ) -> str:
        """
        Create a new audit record for a notification.
        
        Args:
            trigger: Notification trigger
            route: Stakeholder route
            notification_id: Optional notification ID (generated if not provided)
        
        Returns:
            Notification ID
        """
        if not notification_id:
            notification_id = str(uuid.uuid4())
        
        audit_record = NotificationAudit(
            notification_id=notification_id,
            work_item_id=trigger.work_item_id,
            trigger_type=trigger.trigger_type,
            recipient_type=route.recipient_type,
            recipient_id=route.recipient_id,
            delivery_method=route.delivery_method,
            status=NotificationStatus.PENDING,
            created_at=datetime.utcnow()
        )
        
        self._audit_records[notification_id] = audit_record
        
        log_structured(
            logger,
            "info",
            "Created notification audit record",
            extra={
                "notification_id": notification_id,
                "work_item_id": trigger.work_item_id,
                "trigger_type": trigger.trigger_type,
                "recipient_type": route.recipient_type,
                "recipient_id": route.recipient_id
            }
        )
        
        # Log business event
        log_business_event(
            logger,
            "notification_created",
            {
                "notification_id": notification_id,
                "work_item_id": trigger.work_item_id,
                "trigger_type": trigger.trigger_type,
                "recipient_type": route.recipient_type,
                "escalation_level": trigger.escalation_level.name,
                "severity_score": trigger.severity_score
            }
        )
        
        return notification_id
    
    def update_notification_status(
        self,
        notification_id: str,
        status: NotificationStatus,
        error_message: Optional[str] = None,
        response_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update notification status in audit record.
        
        Args:
            notification_id: Notification ID
            status: New status
            error_message: Error message if failed
            response_data: Response data from delivery
        
        Returns:
            True if updated successfully
        """
        try:
            if notification_id not in self._audit_records:
                logger.warning(f"Audit record not found for notification {notification_id}")
                return False
            
            audit_record = self._audit_records[notification_id]
            audit_record.status = status
            
            current_time = datetime.utcnow()
            
            if status == NotificationStatus.SENT:
                audit_record.sent_at = current_time
            elif status == NotificationStatus.DELIVERED:
                audit_record.delivered_at = current_time
            
            if error_message:
                audit_record.error_message = error_message
            
            if response_data:
                audit_record.response_data = response_data
            
            # Calculate processing time if sent
            if status == NotificationStatus.SENT and audit_record.created_at:
                processing_time = (current_time - audit_record.created_at).total_seconds() * 1000
                audit_record.processing_time_ms = processing_time
                
                # Log performance metric
                log_performance_metric(
                    logger,
                    "notification_processing_time",
                    processing_time,
                    "ms",
                    {
                        "trigger_type": audit_record.trigger_type,
                        "recipient_type": audit_record.recipient_type,
                        "delivery_method": audit_record.delivery_method
                    }
                )
            
            log_structured(
                logger,
                "info",
                f"Updated notification status to {status}",
                extra={
                    "notification_id": notification_id,
                    "status": status,
                    "processing_time_ms": audit_record.processing_time_ms,
                    "error_message": error_message
                }
            )
            
            # Log business event for status changes
            log_business_event(
                logger,
                "notification_status_changed",
                {
                    "notification_id": notification_id,
                    "work_item_id": audit_record.work_item_id,
                    "old_status": "pending",  # Could track previous status
                    "new_status": status,
                    "processing_time_ms": audit_record.processing_time_ms,
                    "error_message": error_message
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error updating notification status: {e}",
                extra={"notification_id": notification_id},
                exc_info=True
            )
            return False
    
    def record_user_action(
        self,
        notification_id: str,
        action_type: str,
        action_data: Dict[str, Any],
        user_id: Optional[str] = None
    ) -> bool:
        """
        Record user action taken from notification.
        
        Args:
            notification_id: Notification ID
            action_type: Type of action taken
            action_data: Action data
            user_id: User who took the action
        
        Returns:
            True if recorded successfully
        """
        try:
            log_structured(
                logger,
                "info",
                f"User action recorded: {action_type}",
                extra={
                    "notification_id": notification_id,
                    "action_type": action_type,
                    "user_id": user_id,
                    "action_data": action_data
                }
            )
            
            # Log business event
            log_business_event(
                logger,
                "notification_user_action",
                {
                    "notification_id": notification_id,
                    "action_type": action_type,
                    "user_id": user_id,
                    "action_data": action_data,
                    "timestamp": datetime.utcnow().isoformat()
                },
                user_id=user_id
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error recording user action: {e}",
                extra={"notification_id": notification_id},
                exc_info=True
            )
            return False
    
    def get_audit_record(self, notification_id: str) -> Optional[NotificationAudit]:
        """Get audit record by notification ID."""
        return self._audit_records.get(notification_id)
    
    def get_audit_records_for_work_item(self, work_item_id: int) -> List[NotificationAudit]:
        """Get all audit records for a work item."""
        return [
            record for record in self._audit_records.values()
            if record.work_item_id == work_item_id
        ]
    
    def calculate_metrics(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> NotificationMetrics:
        """
        Calculate notification metrics for a time period.
        
        Args:
            start_time: Start of time period
            end_time: End of time period
        
        Returns:
            Notification metrics
        """
        try:
            # Filter records by time period
            period_records = [
                record for record in self._audit_records.values()
                if start_time <= record.created_at <= end_time
            ]
            
            if not period_records:
                return NotificationMetrics(
                    period_start=start_time,
                    period_end=end_time
                )
            
            # Calculate basic metrics
            total_notifications = len(period_records)
            successful_deliveries = len([r for r in period_records if r.status == NotificationStatus.DELIVERED])
            failed_deliveries = len([r for r in period_records if r.status == NotificationStatus.FAILED])
            rate_limited = len([r for r in period_records if r.status == NotificationStatus.RATE_LIMITED])
            deduplicated = len([r for r in period_records if r.status == NotificationStatus.DEDUPLICATED])
            
            # Calculate processing times
            processing_times = [
                r.processing_time_ms for r in period_records
                if r.processing_time_ms is not None
            ]
            
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0
            
            # Calculate percentiles
            if processing_times:
                sorted_times = sorted(processing_times)
                p50_index = int(len(sorted_times) * 0.5)
                p95_index = int(len(sorted_times) * 0.95)
                p50_processing_time = sorted_times[p50_index] if p50_index < len(sorted_times) else 0.0
                p95_processing_time = sorted_times[p95_index] if p95_index < len(sorted_times) else 0.0
            else:
                p50_processing_time = 0.0
                p95_processing_time = 0.0
            
            # Calculate error rates
            error_rate = failed_deliveries / total_notifications if total_notifications > 0 else 0.0
            
            metrics = NotificationMetrics(
                total_notifications_sent=total_notifications,
                avg_processing_time_ms=avg_processing_time,
                p50_processing_time_ms=p50_processing_time,
                p95_processing_time_ms=p95_processing_time,
                successful_deliveries=successful_deliveries,
                failed_deliveries=failed_deliveries,
                rate_limited_notifications=rate_limited,
                deduplicated_notifications=deduplicated,
                error_rate=error_rate,
                period_start=start_time,
                period_end=end_time
            )
            
            # Log performance metrics
            log_performance_metric(
                logger,
                "notification_p50_processing_time",
                p50_processing_time,
                "ms",
                {"period": f"{start_time.isoformat()}_to_{end_time.isoformat()}"}
            )
            
            log_performance_metric(
                logger,
                "notification_error_rate",
                error_rate * 100,
                "percent",
                {"period": f"{start_time.isoformat()}_to_{end_time.isoformat()}"}
            )
            
            # Log business event
            log_business_event(
                logger,
                "notification_metrics_calculated",
                {
                    "period_start": start_time.isoformat(),
                    "period_end": end_time.isoformat(),
                    "total_notifications": total_notifications,
                    "successful_deliveries": successful_deliveries,
                    "error_rate": error_rate,
                    "p50_processing_time_ms": p50_processing_time,
                    "p95_processing_time_ms": p95_processing_time
                }
            )
            
            return metrics
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error calculating metrics: {e}",
                extra={
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat()
                },
                exc_info=True
            )
            return NotificationMetrics(
                period_start=start_time,
                period_end=end_time
            )
    
    def check_sla_compliance(self, metrics: NotificationMetrics) -> Dict[str, Any]:
        """
        Check SLA compliance based on metrics.
        
        Args:
            metrics: Notification metrics
        
        Returns:
            SLA compliance report
        """
        sla_report = {
            "p50_latency_sla": {
                "threshold_ms": 5000,  # 5 seconds
                "actual_ms": metrics.p50_processing_time_ms,
                "compliant": metrics.p50_processing_time_ms <= 5000,
                "violation_percentage": max(0, (metrics.p50_processing_time_ms - 5000) / 5000 * 100)
            },
            "error_rate_sla": {
                "threshold_percent": 1.0,  # 1%
                "actual_percent": metrics.error_rate * 100,
                "compliant": metrics.error_rate <= 0.01,
                "violation_percentage": max(0, (metrics.error_rate - 0.01) / 0.01 * 100)
            },
            "overall_compliant": (
                metrics.p50_processing_time_ms <= 5000 and
                metrics.error_rate <= 0.01
            )
        }
        
        # Log SLA compliance
        log_business_event(
            logger,
            "sla_compliance_check",
            sla_report
        )
        
        return sla_report
    
    def cleanup_old_records(self, retention_days: int = 30) -> int:
        """
        Clean up old audit records.
        
        Args:
            retention_days: Number of days to retain records
        
        Returns:
            Number of records cleaned up
        """
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
        
        old_records = [
            notification_id for notification_id, record in self._audit_records.items()
            if record.created_at < cutoff_date
        ]
        
        for notification_id in old_records:
            del self._audit_records[notification_id]
        
        log_structured(
            logger,
            "info",
            f"Cleaned up {len(old_records)} old audit records",
            extra={
                "retention_days": retention_days,
                "cutoff_date": cutoff_date.isoformat(),
                "records_cleaned": len(old_records)
            }
        )
        
        return len(old_records)
