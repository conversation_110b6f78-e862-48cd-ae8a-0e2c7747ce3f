"""
Azure AI Search Client
Handles indexing, searching, and vector operations for work items.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import numpy as np

from azure.search.documents import SearchClient as AzureSearchClient
from azure.search.documents.indexes import SearchIndexClient
from azure.search.documents.models import VectorizedQuery
from azure.core.credentials import AzureKeyCredential

from ..models.schemas import WorkItem, SearchResult
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class SearchClient:
    """Client for Azure AI Search operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.service_name = config.AZURE_SEARCH_SERVICE_NAME
        self.admin_key = config.AZURE_SEARCH_ADMIN_KEY
        self.index_name = getattr(config, 'SEARCH_INDEX_NAME', 'workitems')
        
        # Initialize Azure Search clients
        endpoint = f"https://{self.service_name}.search.windows.net"
        credential = AzureKeyCredential(self.admin_key)
        
        self.search_client = AzureSearchClient(
            endpoint=endpoint,
            index_name=self.index_name,
            credential=credential
        )
        
        self.index_client = SearchIndexClient(
            endpoint=endpoint,
            credential=credential
        )
    
    async def ensure_index_exists(self) -> None:
        """
        Ensure the search index exists with the correct schema.
        """
        try:
            from azure.search.documents.indexes.models import (
                SearchIndex,
                SearchField,
                SearchFieldDataType,
                SimpleField,
                SearchableField,
                VectorSearch,
                HnswAlgorithmConfiguration,
                VectorSearchProfile,
                SemanticConfiguration,
                SemanticSearch,
                SemanticField,
                SemanticPrioritizedFields
            )
            
            # Define the index schema
            fields = [
                SimpleField(name="id", type=SearchFieldDataType.String, key=True),
                SearchableField(name="title", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="description", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SimpleField(name="work_item_type", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="state", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="area_path", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="assigned_to", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="created_by", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="created_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="changed_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="priority", type=SearchFieldDataType.Int32, filterable=True, sortable=True),
                SearchableField(name="tags", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="repro_steps", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="system_info", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchField(
                    name="title_vector",
                    type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True,
                    vector_search_dimensions=384,  # Adjust based on your embedding model
                    vector_search_profile_name="default-vector-profile"
                ),
                SearchField(
                    name="content_vector",
                    type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True,
                    vector_search_dimensions=384,
                    vector_search_profile_name="default-vector-profile"
                )
            ]
            
            # Configure vector search
            vector_search = VectorSearch(
                algorithms=[
                    HnswAlgorithmConfiguration(name="default-hnsw")
                ],
                profiles=[
                    VectorSearchProfile(
                        name="default-vector-profile",
                        algorithm_configuration_name="default-hnsw"
                    )
                ]
            )
            
            # Configure semantic search
            semantic_config = SemanticConfiguration(
                name="default-semantic-config",
                prioritized_fields=SemanticPrioritizedFields(
                    title_field=SemanticField(field_name="title"),
                    content_fields=[
                        SemanticField(field_name="description"),
                        SemanticField(field_name="repro_steps")
                    ],
                    keywords_fields=[
                        SemanticField(field_name="tags"),
                        SemanticField(field_name="area_path")
                    ]
                )
            )
            
            semantic_search = SemanticSearch(
                configurations=[semantic_config]
            )
            
            # Create the index
            index = SearchIndex(
                name=self.index_name,
                fields=fields,
                vector_search=vector_search,
                semantic_search=semantic_search
            )
            
            # Create or update the index
            result = self.index_client.create_or_update_index(index)
            
            log_structured(
                logger,
                "info",
                "Search index ensured",
                extra={
                    "index_name": self.index_name,
                    "field_count": len(fields)
                }
            )
            
        except Exception as e:
            logger.error(f"Error ensuring search index exists: {e}")
            raise
    
    async def upsert_work_item(self, work_item: WorkItem, embedding: Optional[List[float]] = None) -> None:
        """
        Upsert a work item to the search index.
        
        Args:
            work_item: The work item to index
            embedding: Optional pre-computed embedding
        """
        try:
            # Convert work item to search document
            document = {
                "id": str(work_item.id),
                "title": work_item.title or "",
                "description": work_item.description or "",
                "work_item_type": work_item.work_item_type or "",
                "state": work_item.state or "",
                "area_path": work_item.area_path or "",
                "assigned_to": work_item.assigned_to or "",
                "created_by": work_item.created_by or "",
                "created_date": work_item.created_date,
                "changed_date": work_item.changed_date,
                "priority": work_item.priority or 2,
                "tags": work_item.tags or "",
                "repro_steps": work_item.repro_steps or "",
                "system_info": work_item.system_info or "",
            }
            
            # Add embeddings if provided
            if embedding:
                document["title_vector"] = embedding
                document["content_vector"] = embedding
            
            # Upload the document
            result = self.search_client.upload_documents([document])
            
            log_structured(
                logger,
                "debug",
                "Upserted work item to search index",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title or "") > 50 else work_item.title,
                    "has_embedding": embedding is not None
                }
            )
            
        except Exception as e:
            logger.error(f"Error upserting work item {work_item.id} to search index: {e}")
            raise
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: List[float]
    ) -> None:
        """
        Upsert a work item with its embedding to the search index.
        """
        await self.upsert_work_item(work_item, embedding)
    
    async def hybrid_search(
        self,
        query_text: str,
        query_vector: Optional[List[float]] = None,
        filters: Optional[str] = None,
        top: int = 10,
        include_total_count: bool = False
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining text and vector search.
        
        Args:
            query_text: Text query
            query_vector: Query vector for semantic search
            filters: OData filter expression
            top: Number of results to return
            include_total_count: Whether to include total count
        
        Returns:
            List of search results
        """
        try:
            search_params = {
                "search_text": query_text,
                "top": top,
                "include_total_count": include_total_count,
                "query_type": "semantic",
                "semantic_configuration_name": "default-semantic-config"
            }
            
            if filters:
                search_params["filter"] = filters
            
            # Add vector query if provided
            if query_vector:
                vector_query = VectorizedQuery(
                    vector=query_vector,
                    k_nearest_neighbors=top,
                    fields="content_vector"
                )
                search_params["vector_queries"] = [vector_query]
            
            # Execute search
            results = self.search_client.search(**search_params)
            
            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    work_item_id=int(result["id"]),
                    title=result.get("title", ""),
                    description=result.get("description", ""),
                    work_item_type=result.get("work_item_type", ""),
                    area_path=result.get("area_path", ""),
                    score=result.get("@search.score", 0.0),
                    reranker_score=result.get("@search.reranker_score"),
                    highlights=result.get("@search.highlights", {})
                )
                search_results.append(search_result)
            
            log_structured(
                logger,
                "info",
                "Executed hybrid search",
                extra={
                    "query_text": query_text[:100] + "..." if len(query_text) > 100 else query_text,
                    "has_vector": query_vector is not None,
                    "result_count": len(search_results),
                    "top": top
                }
            )
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error executing hybrid search: {e}")
            raise
    
    async def find_similar_work_items(
        self,
        work_item: WorkItem,
        embedding: List[float],
        similarity_threshold: float = 0.8,
        max_results: int = 5
    ) -> List[SearchResult]:
        """
        Find work items similar to the given work item.
        
        Args:
            work_item: The reference work item
            embedding: Embedding of the work item
            similarity_threshold: Minimum similarity score
            max_results: Maximum number of results
        
        Returns:
            List of similar work items
        """
        try:
            # Build search query combining title and description
            query_text = f"{work_item.title} {work_item.description}"
            
            # Add filters to exclude the same work item and closed items
            filters = f"id ne '{work_item.id}' and state ne 'Closed' and state ne 'Resolved'"
            
            # Perform hybrid search
            results = await self.hybrid_search(
                query_text=query_text,
                query_vector=embedding,
                filters=filters,
                top=max_results * 2  # Get more results to filter by threshold
            )
            
            # Filter by similarity threshold
            similar_items = [
                result for result in results
                if result.score >= similarity_threshold
            ][:max_results]
            
            log_structured(
                logger,
                "info",
                "Found similar work items",
                extra={
                    "reference_work_item_id": work_item.id,
                    "similar_count": len(similar_items),
                    "threshold": similarity_threshold
                }
            )
            
            return similar_items

        except Exception as e:
            logger.error(f"Error finding similar work items: {e}")
            raise

    async def index_work_item(self, work_item: WorkItem) -> None:
        """
        Index a work item for future similarity searches.
        This is an alias for upsert_work_item for backward compatibility.
        """
        await self.upsert_work_item(work_item)
