"""
Response Processing Pipeline
Orchestrates the complete workflow for processing user responses and team feedback.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..models.schemas import WorkItem, TriageResult
from ..adapters.ado_client import AdoClient
from ..adapters.teams_client import TeamsClient
from ..adapters.search_client import Search<PERSON>lient
from .defect_feedback_service import DefectFeedbackService, FeedbackType, FeedbackSentiment
from .user_action_audit_service import UserActionAuditService, UserActionType, ActionSource, ActionOutcome
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class ResponseSource(Enum):
    """Source of the response."""
    TEAMS_ADAPTIVE_CARD = "teams_adaptive_card"
    TEAMS_QUICK_ACTION = "teams_quick_action"
    LOGIC_APP = "logic_app"
    EMAIL_REPLY = "email_reply"
    WEBHOOK = "webhook"
    MANUAL = "manual"


class ProcessingStage(Enum):
    """Stages in the response processing pipeline."""
    RECEIVED = "received"
    VALIDATED = "validated"
    ANALYZED = "analyzed"
    PROCESSED = "processed"
    UPDATED = "updated"
    NOTIFIED = "notified"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class ResponseContext:
    """Context for processing a response."""
    work_item_id: int
    response_data: Dict[str, Any]
    source: ResponseSource
    user_email: str
    user_name: str
    timestamp: datetime
    stage: ProcessingStage = ProcessingStage.RECEIVED
    processing_notes: List[str] = None
    
    def __post_init__(self):
        if self.processing_notes is None:
            self.processing_notes = []


class ResponseProcessingPipeline:
    """Pipeline for processing user responses and team feedback."""
    
    def __init__(self, config: Config):
        self.config = config
        self.ado_client = AdoClient(config)
        self.teams_client = TeamsClient(config)
        self.search_client = SearchClient(config)
        self.feedback_service = DefectFeedbackService(config)
        self.user_audit_service = UserActionAuditService(config)
    
    async def process_response(
        self, 
        work_item_id: int, 
        response_data: Dict[str, Any],
        source: str = "unknown"
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Process a user response through the complete pipeline.
        
        Args:
            work_item_id: The work item ID
            response_data: Raw response data
            source: Source of the response
            
        Returns:
            Tuple of (success, processing_result)
        """
        # Create response context
        context = ResponseContext(
            work_item_id=work_item_id,
            response_data=response_data,
            source=ResponseSource(source) if source in [s.value for s in ResponseSource] else ResponseSource.MANUAL,
            user_email=response_data.get("user_email", ""),
            user_name=response_data.get("user_name", "Unknown User"),
            timestamp=datetime.utcnow()
        )
        
        processing_result = {
            "work_item_id": work_item_id,
            "processing_stages": [],
            "success": False,
            "error_message": None,
            "updates_applied": [],
            "notifications_sent": [],
            "action_id": None
        }

        # Record the user action at the start
        action_type = self._determine_action_type(context.response_data)
        action_source = self._map_source_to_action_source(context.source)

        action_id = self.user_audit_service.record_user_action(
            work_item_id=context.work_item_id,
            user_email=context.user_email,
            user_name=context.user_name,
            action_type=action_type,
            action_source=action_source,
            action_data=context.response_data,
            session_id=context.response_data.get("session_id"),
            related_notification_id=context.response_data.get("notification_id"),
            ip_address=context.response_data.get("ip_address"),
            user_agent=context.response_data.get("user_agent")
        )

        processing_result["action_id"] = action_id
        context.response_data["action_id"] = action_id
        
        try:
            log_structured(
                logger,
                "info",
                f"Starting response processing pipeline for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "source": context.source.value,
                    "user_name": context.user_name
                }
            )
            
            # Stage 1: Validate response
            if not await self._validate_response(context, processing_result):
                return False, processing_result
            
            # Stage 2: Analyze response content
            if not await self._analyze_response(context, processing_result):
                return False, processing_result
            
            # Stage 3: Process feedback
            if not await self._process_feedback(context, processing_result):
                return False, processing_result
            
            # Stage 4: Update work item
            if not await self._update_work_item(context, processing_result):
                return False, processing_result
            
            # Stage 5: Send notifications
            if not await self._send_notifications(context, processing_result):
                return False, processing_result
            
            # Stage 6: Complete processing
            await self._complete_processing(context, processing_result)
            
            processing_result["success"] = True

            # Update action outcome as successful
            processing_duration = (datetime.utcnow() - context.timestamp).total_seconds() * 1000
            self.user_audit_service.update_action_outcome(
                action_id,
                ActionOutcome.SUCCESS,
                processing_duration_ms=processing_duration,
                response_data=processing_result
            )

            log_structured(
                logger,
                "info",
                f"Successfully completed response processing pipeline for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "action_id": action_id,
                    "stages_completed": len(processing_result["processing_stages"]),
                    "updates_applied": len(processing_result["updates_applied"]),
                    "processing_duration_ms": processing_duration
                }
            )

            return True, processing_result
            
        except Exception as e:
            context.stage = ProcessingStage.FAILED
            processing_result["error_message"] = str(e)

            # Update action outcome as failed
            if action_id:
                processing_duration = (datetime.utcnow() - context.timestamp).total_seconds() * 1000
                self.user_audit_service.update_action_outcome(
                    action_id,
                    ActionOutcome.FAILED,
                    processing_duration_ms=processing_duration,
                    error_message=str(e),
                    response_data=processing_result
                )

            log_structured(
                logger,
                "error",
                f"Response processing pipeline failed for work item {work_item_id}: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "action_id": action_id,
                    "stage": context.stage.value,
                    "user_name": context.user_name
                },
                exc_info=True
            )

            return False, processing_result
    
    async def _validate_response(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Validate the response data."""
        context.stage = ProcessingStage.VALIDATED
        result["processing_stages"].append("validation")
        
        # Check required fields
        if not context.work_item_id:
            context.processing_notes.append("Missing work item ID")
            return False
        
        if not context.response_data:
            context.processing_notes.append("Empty response data")
            return False
        
        # Verify work item exists
        try:
            work_item_data = await self.ado_client.get_work_item(context.work_item_id)
            if not work_item_data:
                context.processing_notes.append("Work item not found")
                return False
        except Exception as e:
            context.processing_notes.append(f"Error accessing work item: {e}")
            return False
        
        context.processing_notes.append("Response validation successful")
        return True
    
    async def _analyze_response(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Analyze the response content."""
        context.stage = ProcessingStage.ANALYZED
        result["processing_stages"].append("analysis")
        
        reply_text = context.response_data.get("replyText", "")
        priority = context.response_data.get("priority")
        
        # Analyze sentiment and intent
        analysis = {
            "has_text_feedback": bool(reply_text),
            "has_priority_change": priority is not None,
            "sentiment": "neutral",
            "intent": "general_feedback",
            "keywords": []
        }
        
        if reply_text:
            reply_lower = reply_text.lower()
            
            # Determine sentiment
            if any(word in reply_lower for word in ["accept", "agree", "correct", "good", "yes"]):
                analysis["sentiment"] = "positive"
                analysis["intent"] = "acceptance"
            elif any(word in reply_lower for word in ["reject", "disagree", "wrong", "no", "incorrect"]):
                analysis["sentiment"] = "negative"
                analysis["intent"] = "rejection"
            elif any(word in reply_lower for word in ["reassign", "different", "change"]):
                analysis["intent"] = "reassignment_request"
            elif any(word in reply_lower for word in ["duplicate", "already", "exists"]):
                analysis["intent"] = "duplicate_report"
            
            # Extract keywords
            keywords = []
            if "urgent" in reply_lower or "critical" in reply_lower:
                keywords.append("urgent")
            if "low priority" in reply_lower or "not urgent" in reply_lower:
                keywords.append("low_priority")
            if "security" in reply_lower:
                keywords.append("security")
            
            analysis["keywords"] = keywords
        
        context.response_data["analysis"] = analysis
        context.processing_notes.append(f"Response analysis: {analysis['intent']} with {analysis['sentiment']} sentiment")
        
        return True
    
    async def _process_feedback(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Process the feedback through the feedback service."""
        context.stage = ProcessingStage.PROCESSED
        result["processing_stages"].append("feedback_processing")
        
        # Use the feedback service to process the response
        success = await self.feedback_service.process_teams_adaptive_card_response(
            context.work_item_id,
            context.response_data
        )
        
        if success:
            context.processing_notes.append("Feedback processed successfully")
            result["updates_applied"].append("feedback_processed")
        else:
            context.processing_notes.append("Feedback processing failed")
            return False
        
        return True
    
    async def _update_work_item(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Update the work item with additional processing information."""
        context.stage = ProcessingStage.UPDATED
        result["processing_stages"].append("work_item_update")

        # Skip adding processing metadata to keep work item clean
        # The feedback service already handles the actual user response
        context.processing_notes.append("Work item update stage completed (no metadata added)")

        return True

    async def _send_notifications(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Send notifications about the processed response."""
        context.stage = ProcessingStage.NOTIFIED
        result["processing_stages"].append("notifications")

        try:
            # Send confirmation to the user who provided feedback
            confirmation_sent = await self.teams_client._send_simple_message(
                context.user_name,
                f"Response Processed - Work Item {context.work_item_id}",
                f"""✅ **Your response has been processed successfully**

**Work Item:** #{context.work_item_id}
**Processed at:** {context.timestamp.strftime('%Y-%m-%d %H:%M:%S UTC')}
**Processing stages:** {' → '.join(result['processing_stages'])}

Thank you for your feedback! The work item has been updated based on your input.
                """.strip()
            )

            if confirmation_sent:
                result["notifications_sent"].append("user_confirmation")
                context.processing_notes.append("User confirmation sent")

            # Send notification to stakeholders if significant changes were made
            analysis = context.response_data.get("analysis", {})
            if analysis.get("intent") in ["rejection", "reassignment_request", "duplicate_report"]:
                stakeholder_notification_sent = await self._send_stakeholder_notification(context, result)
                if stakeholder_notification_sent:
                    result["notifications_sent"].append("stakeholder_notification")

            return True

        except Exception as e:
            context.processing_notes.append(f"Notification sending failed: {e}")
            log_structured(
                logger,
                "warning",
                f"Failed to send notifications for work item {context.work_item_id}: {e}",
                extra={"work_item_id": context.work_item_id}
            )
            # Don't fail the pipeline for notification issues
            return True

    async def _send_stakeholder_notification(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Send notification to stakeholders about significant feedback."""
        try:
            analysis = context.response_data.get("analysis", {})
            intent = analysis.get("intent", "general_feedback")

            stakeholder_message = f"""🚨 **Significant User Feedback Received**

**Work Item:** #{context.work_item_id}
**User:** {context.user_name}
**Feedback Type:** {intent.replace('_', ' ').title()}
**Sentiment:** {analysis.get('sentiment', 'neutral').title()}

**User Response:** {context.response_data.get('replyText', 'No text provided')}

**Action Required:** Please review the work item and user feedback to determine next steps.

**Azure DevOps Link:** https://dev.azure.com/organization/project/_workitems/edit/{context.work_item_id}
            """.strip()

            await self.teams_client._send_simple_message(
                "Stakeholders",
                f"User Feedback Alert - Work Item {context.work_item_id}",
                stakeholder_message
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send stakeholder notification: {e}",
                extra={"work_item_id": context.work_item_id}
            )
            return False

    async def _complete_processing(self, context: ResponseContext, result: Dict[str, Any]) -> bool:
        """Complete the processing pipeline."""
        context.stage = ProcessingStage.COMPLETED
        result["processing_stages"].append("completion")

        # Log completion metrics
        processing_duration = (datetime.utcnow() - context.timestamp).total_seconds()

        log_structured(
            logger,
            "info",
            f"Response processing pipeline completed for work item {context.work_item_id}",
            extra={
                "work_item_id": context.work_item_id,
                "user_name": context.user_name,
                "source": context.source.value,
                "processing_duration_seconds": processing_duration,
                "stages_completed": len(result["processing_stages"]),
                "updates_applied": len(result["updates_applied"]),
                "notifications_sent": len(result["notifications_sent"])
            }
        )

        context.processing_notes.append(f"Pipeline completed in {processing_duration:.2f} seconds")

        return True

    async def get_processing_statistics(self, days_back: int = 7) -> Dict[str, Any]:
        """Get statistics about response processing over the specified period."""
        try:
            # This would typically query a database or logging system
            # For now, return a placeholder structure
            stats = {
                "period_days": days_back,
                "total_responses_processed": 0,
                "success_rate": 0.0,
                "average_processing_time_seconds": 0.0,
                "responses_by_source": {},
                "responses_by_intent": {},
                "most_active_users": [],
                "common_feedback_themes": []
            }

            log_structured(
                logger,
                "info",
                f"Generated processing statistics for {days_back} days",
                extra={"days_back": days_back, "stats": stats}
            )

            return stats

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to generate processing statistics: {e}",
                exc_info=True
            )
            return {}

    async def reprocess_failed_responses(self, hours_back: int = 24) -> Dict[str, Any]:
        """Reprocess responses that failed in the last specified hours."""
        try:
            # This would typically query failed responses from a database
            # For now, return a placeholder structure
            reprocess_result = {
                "period_hours": hours_back,
                "failed_responses_found": 0,
                "reprocessing_attempts": 0,
                "successful_reprocessing": 0,
                "still_failing": 0,
                "reprocessed_work_items": []
            }

            log_structured(
                logger,
                "info",
                f"Completed reprocessing of failed responses from last {hours_back} hours",
                extra={"hours_back": hours_back, "result": reprocess_result}
            )

            return reprocess_result

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to reprocess failed responses: {e}",
                exc_info=True
            )
            return {}

    def _determine_action_type(self, response_data: Dict[str, Any]) -> UserActionType:
        """Determine the action type based on response data."""
        action = response_data.get("action", "")
        reply_text = response_data.get("replyText", "").lower()

        # Check explicit action first
        if action == "submit_feedback":
            return UserActionType.FEEDBACK_SUBMITTED
        elif action == "quick_accept":
            return UserActionType.QUICK_ACCEPT
        elif action == "request_reassignment":
            return UserActionType.REASSIGNMENT_REQUESTED
        elif action == "mark_duplicate":
            return UserActionType.DUPLICATE_CONFIRMED
        elif action == "assign_to_me":
            return UserActionType.ASSIGNMENT_ACCEPTED
        elif action == "escalate_security":
            return UserActionType.ESCALATION_REQUESTED

        # Analyze reply text for implicit actions
        if "priority" in response_data and response_data["priority"] is not None:
            return UserActionType.PRIORITY_CHANGED
        elif any(word in reply_text for word in ["accept", "agree", "correct"]):
            return UserActionType.ASSIGNMENT_ACCEPTED
        elif any(word in reply_text for word in ["reject", "disagree", "wrong"]):
            return UserActionType.ASSIGNMENT_REJECTED
        elif any(word in reply_text for word in ["reassign", "different"]):
            return UserActionType.REASSIGNMENT_REQUESTED
        elif any(word in reply_text for word in ["duplicate", "already exists"]):
            return UserActionType.DUPLICATE_CONFIRMED
        else:
            return UserActionType.RESPONSE_PROVIDED

    def _map_source_to_action_source(self, source: ResponseSource) -> ActionSource:
        """Map ResponseSource to ActionSource."""
        mapping = {
            ResponseSource.TEAMS_ADAPTIVE_CARD: ActionSource.TEAMS_ADAPTIVE_CARD,
            ResponseSource.TEAMS_QUICK_ACTION: ActionSource.TEAMS_QUICK_ACTION,
            ResponseSource.LOGIC_APP: ActionSource.LOGIC_APP,
            ResponseSource.EMAIL_REPLY: ActionSource.EMAIL_REPLY,
            ResponseSource.WEBHOOK: ActionSource.WEBHOOK,
            ResponseSource.MANUAL: ActionSource.API
        }
        return mapping.get(source, ActionSource.API)
