"""
SQL Database Vector Client
Handles storing and retrieving vectorized work item data in SQL database.
"""

import json
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import numpy as np
import asyncpg
import pyodbc
from dataclasses import dataclass

from ..models.schemas import WorkItem, SearchResult
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


@dataclass
class VectorSearchResult:
    """Result from vector similarity search."""
    work_item_id: int
    similarity_score: float
    work_item_data: Dict[str, Any]


class SqlVectorClient:
    """Client for SQL database vector operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.connection_string = config.get('SQL_CONNECTION_STRING')
        self.database_type = config.get('SQL_DATABASE_TYPE', 'azure_sql')  # azure_sql, postgresql
        self.vector_dimension = config.get('EMBEDDING_DIMENSION', 384)
        
    async def ensure_tables_exist(self) -> None:
        """Ensure the required tables exist with proper schema."""
        if self.database_type == 'postgresql':
            await self._ensure_postgresql_tables()
        else:
            await self._ensure_azure_sql_tables()
    
    async def _ensure_postgresql_tables(self) -> None:
        """Create tables for PostgreSQL with pgvector extension."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            # Enable pgvector extension
            await conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # Create work items table with vector column
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS work_items_vectors (
                    id SERIAL PRIMARY KEY,
                    work_item_id INTEGER UNIQUE NOT NULL,
                    title TEXT,
                    description TEXT,
                    work_item_type VARCHAR(50),
                    state VARCHAR(50),
                    area_path TEXT,
                    assigned_to VARCHAR(255),
                    created_by VARCHAR(255),
                    created_date TIMESTAMP,
                    changed_date TIMESTAMP,
                    priority INTEGER,
                    tags TEXT,
                    repro_steps TEXT,
                    system_info TEXT,
                    title_vector vector({self.vector_dimension}),
                    content_vector vector({self.vector_dimension}),
                    indexed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_work_item_id (work_item_id),
                    INDEX idx_state (state),
                    INDEX idx_assigned_to (assigned_to)
                );
            """)
            
            # Create index for vector similarity search
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_content_vector_cosine 
                ON work_items_vectors 
                USING ivfflat (content_vector vector_cosine_ops)
                WITH (lists = 100);
            """)
            
        finally:
            await conn.close()
    
    async def _ensure_azure_sql_tables(self) -> None:
        """Create tables for Azure SQL Database."""
        # Note: Azure SQL Database vector support is in preview
        # This would use JSON storage for vectors until native support is GA
        conn = pyodbc.connect(self.connection_string)
        cursor = conn.cursor()
        
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='work_items_vectors' AND xtype='U')
                CREATE TABLE work_items_vectors (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    work_item_id INT UNIQUE NOT NULL,
                    title NVARCHAR(MAX),
                    description NVARCHAR(MAX),
                    work_item_type NVARCHAR(50),
                    state NVARCHAR(50),
                    area_path NVARCHAR(MAX),
                    assigned_to NVARCHAR(255),
                    created_by NVARCHAR(255),
                    created_date DATETIME2,
                    changed_date DATETIME2,
                    priority INT,
                    tags NVARCHAR(MAX),
                    repro_steps NVARCHAR(MAX),
                    system_info NVARCHAR(MAX),
                    title_vector NVARCHAR(MAX), -- JSON array of floats
                    content_vector NVARCHAR(MAX), -- JSON array of floats
                    indexed_date DATETIME2 DEFAULT GETDATE()
                );
            """)
            
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_work_item_id')
                CREATE INDEX idx_work_item_id ON work_items_vectors(work_item_id);
            """)
            
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name='idx_state')
                CREATE INDEX idx_state ON work_items_vectors(state);
            """)
            
            conn.commit()
            
        finally:
            cursor.close()
            conn.close()
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: List[float]
    ) -> None:
        """
        Upsert a work item with its embedding to the database.
        """
        try:
            if self.database_type == 'postgresql':
                await self._upsert_postgresql(work_item, embedding)
            else:
                await self._upsert_azure_sql(work_item, embedding)
                
            log_structured(
                logger,
                "debug",
                "Upserted work item to SQL vector database",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title or "") > 50 else work_item.title,
                    "embedding_dimension": len(embedding)
                }
            )
            
        except Exception as e:
            logger.error(f"Error upserting work item {work_item.id} to SQL vector database: {e}")
            raise
    
    async def _upsert_postgresql(self, work_item: WorkItem, embedding: List[float]) -> None:
        """Upsert to PostgreSQL with pgvector."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            await conn.execute("""
                INSERT INTO work_items_vectors (
                    work_item_id, title, description, work_item_type, state,
                    area_path, assigned_to, created_by, created_date, changed_date,
                    priority, tags, repro_steps, system_info, title_vector, content_vector
                ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                ON CONFLICT (work_item_id) DO UPDATE SET
                    title = EXCLUDED.title,
                    description = EXCLUDED.description,
                    work_item_type = EXCLUDED.work_item_type,
                    state = EXCLUDED.state,
                    area_path = EXCLUDED.area_path,
                    assigned_to = EXCLUDED.assigned_to,
                    created_by = EXCLUDED.created_by,
                    created_date = EXCLUDED.created_date,
                    changed_date = EXCLUDED.changed_date,
                    priority = EXCLUDED.priority,
                    tags = EXCLUDED.tags,
                    repro_steps = EXCLUDED.repro_steps,
                    system_info = EXCLUDED.system_info,
                    title_vector = EXCLUDED.title_vector,
                    content_vector = EXCLUDED.content_vector,
                    indexed_date = CURRENT_TIMESTAMP;
            """, 
                work_item.id, work_item.title, work_item.description,
                work_item.work_item_type, work_item.state, work_item.area_path,
                work_item.assigned_to, work_item.created_by, work_item.created_date,
                work_item.changed_date, work_item.priority, work_item.tags,
                work_item.repro_steps, work_item.system_info, embedding, embedding
            )
        finally:
            await conn.close()
    
    async def _upsert_azure_sql(self, work_item: WorkItem, embedding: List[float]) -> None:
        """Upsert to Azure SQL Database."""
        conn = pyodbc.connect(self.connection_string)
        cursor = conn.cursor()
        
        try:
            # Convert embedding to JSON string
            embedding_json = json.dumps(embedding)
            
            cursor.execute("""
                MERGE work_items_vectors AS target
                USING (VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)) AS source
                (work_item_id, title, description, work_item_type, state, area_path,
                 assigned_to, created_by, created_date, changed_date, priority, tags,
                 repro_steps, system_info, title_vector, content_vector)
                ON target.work_item_id = source.work_item_id
                WHEN MATCHED THEN
                    UPDATE SET
                        title = source.title,
                        description = source.description,
                        work_item_type = source.work_item_type,
                        state = source.state,
                        area_path = source.area_path,
                        assigned_to = source.assigned_to,
                        created_by = source.created_by,
                        created_date = source.created_date,
                        changed_date = source.changed_date,
                        priority = source.priority,
                        tags = source.tags,
                        repro_steps = source.repro_steps,
                        system_info = source.system_info,
                        title_vector = source.title_vector,
                        content_vector = source.content_vector,
                        indexed_date = GETDATE()
                WHEN NOT MATCHED THEN
                    INSERT (work_item_id, title, description, work_item_type, state,
                           area_path, assigned_to, created_by, created_date, changed_date,
                           priority, tags, repro_steps, system_info, title_vector,
                           content_vector)
                    VALUES (source.work_item_id, source.title, source.description,
                           source.work_item_type, source.state, source.area_path,
                           source.assigned_to, source.created_by, source.created_date,
                           source.changed_date, source.priority, source.tags,
                           source.repro_steps, source.system_info, source.title_vector,
                           source.content_vector);
            """, 
                work_item.id, work_item.title, work_item.description,
                work_item.work_item_type, work_item.state, work_item.area_path,
                work_item.assigned_to, work_item.created_by, work_item.created_date,
                work_item.changed_date, work_item.priority, work_item.tags,
                work_item.repro_steps, work_item.system_info, embedding_json, embedding_json
            )
            
            conn.commit()
            
        finally:
            cursor.close()
            conn.close()
    
    async def vector_similarity_search(
        self,
        query_vector: List[float],
        filters: Optional[str] = None,
        top: int = 10
    ) -> List[VectorSearchResult]:
        """
        Perform vector similarity search.
        """
        if self.database_type == 'postgresql':
            return await self._vector_search_postgresql(query_vector, filters, top)
        else:
            return await self._vector_search_azure_sql(query_vector, filters, top)

    async def _vector_search_postgresql(
        self,
        query_vector: List[float],
        filters: Optional[str] = None,
        top: int = 10
    ) -> List[VectorSearchResult]:
        """Vector search using PostgreSQL with pgvector."""
        conn = await asyncpg.connect(self.connection_string)
        try:
            # Build the query
            base_query = """
                SELECT work_item_id, title, description, work_item_type, state,
                       area_path, assigned_to, created_by, created_date, changed_date,
                       priority, tags, repro_steps, system_info,
                       1 - (content_vector <=> $1) AS similarity_score
                FROM work_items_vectors
            """

            where_clause = ""
            if filters:
                # Convert simple filters to SQL WHERE clause
                # This is a simplified implementation - you'd want more robust filter parsing
                where_clause = f" WHERE {filters}"

            query = f"{base_query}{where_clause} ORDER BY content_vector <=> $1 LIMIT $2"

            rows = await conn.fetch(query, query_vector, top)

            results = []
            for row in rows:
                work_item_data = {
                    'id': row['work_item_id'],
                    'title': row['title'],
                    'description': row['description'],
                    'work_item_type': row['work_item_type'],
                    'state': row['state'],
                    'area_path': row['area_path'],
                    'assigned_to': row['assigned_to'],
                    'created_by': row['created_by'],
                    'created_date': row['created_date'],
                    'changed_date': row['changed_date'],
                    'priority': row['priority'],
                    'tags': row['tags'],
                    'repro_steps': row['repro_steps'],
                    'system_info': row['system_info']
                }

                results.append(VectorSearchResult(
                    work_item_id=row['work_item_id'],
                    similarity_score=row['similarity_score'],
                    work_item_data=work_item_data
                ))

            return results

        finally:
            await conn.close()

    async def _vector_search_azure_sql(
        self,
        query_vector: List[float],
        filters: Optional[str] = None,
        top: int = 10
    ) -> List[VectorSearchResult]:
        """Vector search using Azure SQL Database (with JSON vectors)."""
        conn = pyodbc.connect(self.connection_string)
        cursor = conn.cursor()

        try:
            # For Azure SQL, we'll need to implement cosine similarity in SQL
            # This is less efficient than native vector support but works
            query_vector_json = json.dumps(query_vector)

            base_query = """
                SELECT TOP (?) work_item_id, title, description, work_item_type, state,
                       area_path, assigned_to, created_by, created_date, changed_date,
                       priority, tags, repro_steps, system_info, content_vector,
                       dbo.cosine_similarity(content_vector, ?) AS similarity_score
                FROM work_items_vectors
            """

            where_clause = ""
            if filters:
                where_clause = f" WHERE {filters}"

            query = f"{base_query}{where_clause} ORDER BY similarity_score DESC"

            cursor.execute(query, top, query_vector_json)
            rows = cursor.fetchall()

            results = []
            for row in rows:
                work_item_data = {
                    'id': row.work_item_id,
                    'title': row.title,
                    'description': row.description,
                    'work_item_type': row.work_item_type,
                    'state': row.state,
                    'area_path': row.area_path,
                    'assigned_to': row.assigned_to,
                    'created_by': row.created_by,
                    'created_date': row.created_date,
                    'changed_date': row.changed_date,
                    'priority': row.priority,
                    'tags': row.tags,
                    'repro_steps': row.repro_steps,
                    'system_info': row.system_info
                }

                results.append(VectorSearchResult(
                    work_item_id=row.work_item_id,
                    similarity_score=row.similarity_score,
                    work_item_data=work_item_data
                ))

            return results

        finally:
            cursor.close()
            conn.close()

    async def get_work_item_by_id(self, work_item_id: int) -> Optional[Dict[str, Any]]:
        """Get a work item by ID."""
        if self.database_type == 'postgresql':
            conn = await asyncpg.connect(self.connection_string)
            try:
                row = await conn.fetchrow(
                    "SELECT * FROM work_items_vectors WHERE work_item_id = $1",
                    work_item_id
                )
                return dict(row) if row else None
            finally:
                await conn.close()
        else:
            conn = pyodbc.connect(self.connection_string)
            cursor = conn.cursor()
            try:
                cursor.execute(
                    "SELECT * FROM work_items_vectors WHERE work_item_id = ?",
                    work_item_id
                )
                row = cursor.fetchone()
                if row:
                    columns = [column[0] for column in cursor.description]
                    return dict(zip(columns, row))
                return None
            finally:
                cursor.close()
                conn.close()

    async def delete_work_item(self, work_item_id: int) -> None:
        """Delete a work item from the vector database."""
        if self.database_type == 'postgresql':
            conn = await asyncpg.connect(self.connection_string)
            try:
                await conn.execute(
                    "DELETE FROM work_items_vectors WHERE work_item_id = $1",
                    work_item_id
                )
            finally:
                await conn.close()
        else:
            conn = pyodbc.connect(self.connection_string)
            cursor = conn.cursor()
            try:
                cursor.execute(
                    "DELETE FROM work_items_vectors WHERE work_item_id = ?",
                    work_item_id
                )
                conn.commit()
            finally:
                cursor.close()
                conn.close()
