"""
Teams Response Handler
Handles responses from Teams adaptive cards including replyText and priority updates.
"""

import json
import logging
import azure.functions as func
from typing import Dict, Any, Optional

from .common.services.defect_feedback_service import DefectFeedbackService
from .common.services.response_processing_pipeline import ResponseProcessingPipeline
from .common.utils.config import get_config
from .common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


async def teams_response_webhook(req: func.HttpRequest) -> func.HttpResponse:
    """
    Handle Teams adaptive card responses with replyText and priority.
    
    Expected payload format:
    {
        "work_item_id": 12345,
        "replyText": "User response text",
        "priority": 2,
        "user_email": "<EMAIL>",
        "user_name": "User Name"
    }
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing Teams adaptive card response"
        )
        
        # Parse request body
        try:
            response_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger,
                "error",
                f"Invalid JSON in Teams response: {e}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        if not response_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract work item ID
        work_item_id = response_data.get("work_item_id")
        if not work_item_id:
            log_structured(
                logger,
                "error",
                "Missing work_item_id in Teams response"
            )
            return func.HttpResponse(
                json.dumps({"error": "Missing work_item_id"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Validate work item ID
        try:
            work_item_id = int(work_item_id)
        except (ValueError, TypeError):
            log_structured(
                logger,
                "error",
                f"Invalid work_item_id format: {work_item_id}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid work_item_id format"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract response data
        reply_text = response_data.get("replyText", "")
        priority = response_data.get("priority")
        user_email = response_data.get("user_email", "")
        user_name = response_data.get("user_name", "Unknown User")
        
        # Log the received response
        log_structured(
            logger,
            "info",
            f"Received Teams response for work item {work_item_id}",
            extra={
                "work_item_id": work_item_id,
                "user_name": user_name,
                "user_email": user_email,
                "has_reply_text": bool(reply_text),
                "priority": priority
            }
        )
        
        # Initialize response processing pipeline
        config = get_config()
        pipeline = ResponseProcessingPipeline(config)

        # Process the response through the complete pipeline
        success, processing_result = await pipeline.process_response(
            work_item_id,
            response_data,
            "teams_adaptive_card"
        )
        
        if success:
            response_message = {
                "status": "success",
                "message": f"Response processed successfully for work item {work_item_id}",
                "work_item_id": work_item_id,
                "processed_at": func.datetime.utcnow().isoformat(),
                "processing_result": processing_result
            }
            
            log_structured(
                logger,
                "info",
                f"Successfully processed Teams response for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name
                }
            )
            
            return func.HttpResponse(
                json.dumps(response_message),
                status_code=200,
                mimetype="application/json"
            )
        else:
            error_message = {
                "status": "error",
                "message": f"Failed to process response for work item {work_item_id}",
                "work_item_id": work_item_id,
                "processing_result": processing_result
            }
            
            return func.HttpResponse(
                json.dumps(error_message),
                status_code=500,
                mimetype="application/json"
            )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing Teams response webhook: {e}",
            exc_info=True
        )
        
        error_response = {
            "status": "error",
            "message": f"Internal server error: {str(e)}"
        }
        
        return func.HttpResponse(
            json.dumps(error_response),
            status_code=500,
            mimetype="application/json"
        )


async def process_logic_app_response(req: func.HttpRequest) -> func.HttpResponse:
    """
    Handle responses from Logic App with the specific format:
    {
        "replyText": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['replyText']}",
        "priority": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['priority']}"
    }
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing Logic App response"
        )
        
        # Parse request body
        try:
            response_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger,
                "error",
                f"Invalid JSON in Logic App response: {e}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        if not response_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract the response data
        reply_text = response_data.get("replyText", "")
        priority = response_data.get("priority")
        
        # Try to extract work item ID from query parameters or headers
        work_item_id = req.params.get("work_item_id")
        if not work_item_id:
            # Try to extract from headers
            work_item_id = req.headers.get("X-Work-Item-ID")
        
        if not work_item_id:
            log_structured(
                logger,
                "error",
                "Missing work_item_id in Logic App response"
            )
            return func.HttpResponse(
                json.dumps({"error": "Missing work_item_id parameter"}),
                status_code=400,
                mimetype="application/json"
            )
        
        try:
            work_item_id = int(work_item_id)
        except (ValueError, TypeError):
            return func.HttpResponse(
                json.dumps({"error": "Invalid work_item_id format"}),
                status_code=400,
                mimetype="application/json"
            )
        
        # Create enhanced response data
        enhanced_response_data = {
            "work_item_id": work_item_id,
            "replyText": reply_text,
            "priority": priority,
            "user_email": req.headers.get("X-User-Email", ""),
            "user_name": req.headers.get("X-User-Name", "Logic App User"),
            "source": "logic_app"
        }
        
        # Log the received response with full details
        log_structured(
            logger,
            "info",
            f"🔍 TEAMS LOGIC APP RESPONSE RECEIVED for work item {work_item_id}",
            extra={
                "work_item_id": work_item_id,
                "reply_text": reply_text,
                "reply_text_length": len(reply_text) if reply_text else 0,
                "priority": priority,
                "priority_type": type(priority).__name__,
                "user_email": req.headers.get("X-User-Email", ""),
                "user_name": req.headers.get("X-User-Name", "Logic App User"),
                "raw_request_body": response_data,
                "query_params": dict(req.params),
                "headers": dict(req.headers)
            }
        )

        # Additional debug logging
        print(f"🔍 DEBUG: Teams Logic App Response for Work Item {work_item_id}")
        print(f"   📝 Reply Text: '{reply_text}'")
        print(f"   🎯 Priority: {priority} (type: {type(priority).__name__})")
        print(f"   📋 Raw Body: {json.dumps(response_data, indent=2)}")
        print(f"   🔗 Query Params: {dict(req.params)}")
        print(f"   📨 Headers: {dict(req.headers)}")
        
        # Initialize response processing pipeline
        config = get_config()
        pipeline = ResponseProcessingPipeline(config)

        # Process the response through the complete pipeline
        success, processing_result = await pipeline.process_response(
            work_item_id,
            enhanced_response_data,
            "logic_app"
        )
        
        if success:
            response_message = {
                "status": "success",
                "message": f"Logic App response processed successfully for work item {work_item_id}",
                "work_item_id": work_item_id,
                "processed_at": func.datetime.utcnow().isoformat(),
                "processing_result": processing_result
            }
            
            return func.HttpResponse(
                json.dumps(response_message),
                status_code=200,
                mimetype="application/json"
            )
        else:
            error_message = {
                "status": "error",
                "message": f"Failed to process Logic App response for work item {work_item_id}",
                "work_item_id": work_item_id
            }
            
            return func.HttpResponse(
                json.dumps(error_message),
                status_code=500,
                mimetype="application/json"
            )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing Logic App response: {e}",
            exc_info=True
        )
        
        error_response = {
            "status": "error",
            "message": f"Internal server error: {str(e)}"
        }
        
        return func.HttpResponse(
            json.dumps(error_response),
            status_code=500,
            mimetype="application/json"
        )
