"""
Vector Storage Factory
Creates the appropriate vector storage client based on configuration.
"""

import logging
from typing import Union, Protocol
from abc import ABC, abstractmethod

from .search_client import SearchClient
from .sql_vector_client import SqlVectorClient
from .local_vector_client import LocalVectorClient
from ..utils.config import Config
from ..models.schemas import WorkItem

logger = logging.getLogger(__name__)


class VectorStorageProtocol(Protocol):
    """Protocol defining the interface for vector storage clients."""
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: list[float]
    ) -> None:
        """Upsert a work item with its embedding."""
        ...
    
    async def vector_similarity_search(
        self,
        query_vector: list[float],
        filters: str | None = None,
        top: int = 10
    ) -> list:
        """Perform vector similarity search."""
        ...


class VectorStorageAdapter:
    """Adapter to make SearchClient compatible with VectorStorageProtocol."""
    
    def __init__(self, search_client: SearchClient):
        self.search_client = search_client
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: list[float]
    ) -> None:
        """Upsert a work item with its embedding."""
        await self.search_client.upsert_work_item_with_embedding(work_item, embedding)
    
    async def vector_similarity_search(
        self,
        query_vector: list[float],
        filters: str | None = None,
        top: int = 10
    ) -> list:
        """Perform vector similarity search using hybrid search."""
        # Convert to SearchClient's hybrid_search format
        results = await self.search_client.hybrid_search(
            query_text="",  # Empty text for pure vector search
            query_vector=query_vector,
            filters=filters,
            top=top
        )
        return results
    
    async def ensure_index_exists(self) -> None:
        """Ensure the search index exists."""
        await self.search_client.ensure_index_exists()


class VectorStorageFactory:
    """Factory for creating vector storage clients."""
    
    @staticmethod
    def create_vector_storage(config: Config) -> VectorStorageProtocol:
        """
        Create the appropriate vector storage client based on configuration.
        
        Args:
            config: Application configuration
            
        Returns:
            Vector storage client implementing VectorStorageProtocol
        """
        storage_type = config.get('VECTOR_STORAGE_TYPE', 'azure_search').lower()
        
        if storage_type == 'sql':
            logger.info("Creating SQL vector storage client")
            return SqlVectorClient(config)
        elif storage_type == 'local':
            logger.info("Creating local file vector storage client")
            return LocalVectorClient(config)
        elif storage_type == 'azure_search':
            logger.info("Creating Azure Search vector storage client")
            search_client = SearchClient(config)
            return VectorStorageAdapter(search_client)
        else:
            raise ValueError(f"Unsupported vector storage type: {storage_type}")
    
    @staticmethod
    async def initialize_storage(storage_client: VectorStorageProtocol) -> None:
        """
        Initialize the storage backend (create tables/indexes as needed).
        
        Args:
            storage_client: The vector storage client to initialize
        """
        try:
            if hasattr(storage_client, 'ensure_tables_exist'):
                # SQL storage
                await storage_client.ensure_tables_exist()
                logger.info("SQL vector storage tables initialized")
            elif hasattr(storage_client, 'ensure_index_exists'):
                # Azure Search storage (via adapter)
                await storage_client.ensure_index_exists()
                logger.info("Azure Search vector index initialized")
            else:
                logger.warning("Storage client does not support initialization")
                
        except Exception as e:
            logger.error(f"Failed to initialize vector storage: {e}")
            raise


# Example usage in your application:
"""
# In your main application or handler:
config = get_config()
vector_storage = VectorStorageFactory.create_vector_storage(config)
await VectorStorageFactory.initialize_storage(vector_storage)

# Use the storage client
embedding = await embedding_service.embed_work_item(work_item)
await vector_storage.upsert_work_item_with_embedding(work_item, embedding)

# Search for similar items
similar_items = await vector_storage.vector_similarity_search(
    query_vector=embedding,
    filters="state ne 'Closed'",
    top=10
)
"""


class HybridVectorStorage:
    """
    Hybrid storage that can use both Azure Search and SQL for different purposes.
    For example: Azure Search for fast retrieval, SQL for detailed analytics.
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.primary_storage = VectorStorageFactory.create_vector_storage(config)
        
        # Optional secondary storage
        secondary_type = config.get('SECONDARY_VECTOR_STORAGE_TYPE')
        if secondary_type and secondary_type != config.get('VECTOR_STORAGE_TYPE'):
            # Create a temporary config for secondary storage
            secondary_config = Config()
            secondary_config.VECTOR_STORAGE_TYPE = secondary_type
            # Copy other relevant settings...
            self.secondary_storage = VectorStorageFactory.create_vector_storage(secondary_config)
        else:
            self.secondary_storage = None
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: list[float]
    ) -> None:
        """Upsert to both primary and secondary storage."""
        # Always upsert to primary storage
        await self.primary_storage.upsert_work_item_with_embedding(work_item, embedding)
        
        # Optionally upsert to secondary storage
        if self.secondary_storage:
            try:
                await self.secondary_storage.upsert_work_item_with_embedding(work_item, embedding)
            except Exception as e:
                logger.warning(f"Failed to upsert to secondary storage: {e}")
    
    async def vector_similarity_search(
        self,
        query_vector: list[float],
        filters: str | None = None,
        top: int = 10,
        use_secondary: bool = False
    ) -> list:
        """Search using primary or secondary storage."""
        storage = self.secondary_storage if use_secondary and self.secondary_storage else self.primary_storage
        return await storage.vector_similarity_search(query_vector, filters, top)
    
    async def initialize_all_storage(self) -> None:
        """Initialize both primary and secondary storage."""
        await VectorStorageFactory.initialize_storage(self.primary_storage)
        if self.secondary_storage:
            await VectorStorageFactory.initialize_storage(self.secondary_storage)
