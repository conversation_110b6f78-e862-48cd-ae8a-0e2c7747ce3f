{"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "1.0.0.0", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}}, "triggers": {"Recurrence": {"recurrence": {"frequency": "Day", "interval": 1, "schedule": {"hours": ["9"], "minutes": [0]}, "timeZone": "GMT Standard Time"}, "type": "Recurrence"}}, "actions": {"Initialize_Date_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "StartDate", "type": "string", "value": "@{formatDateTime(addDays(utcNow(), -2), 'yyyy-MM-dd')}"}, {"name": "EndDate", "type": "string", "value": "@{formatDateTime(utcNow(), 'yyyy-MM-dd')}"}]}, "runAfter": {}}, "Query_Work_Items": {"type": "Http", "inputs": {"method": "POST", "uri": "https://dev.azure.com/@{parameters('ado_organization')}/@{encodeUriComponent(parameters('ado_project'))}/_apis/wit/wiql?api-version=7.0", "headers": {"Authorization": "Basic @{base64(concat(':', parameters('ado_pat_token')))}", "Content-Type": "application/json"}, "body": {"query": "SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State], [System.AssignedTo], [System.CreatedDate], [System.CreatedBy], [Microsoft.VSTS.Common.Priority], [Microsoft.VSTS.Common.Severity], [System.AreaPath] FROM WorkItems WHERE [System.WorkItemType] IN ('Bug', 'Defect', 'User Story', 'Task') AND [System.CreatedDate] >= '@{variables('StartDate')}' AND [System.CreatedDate] < '@{variables('EndDate')}' ORDER BY [System.CreatedDate] DESC"}}, "runAfter": {"Initialize_Date_Variables": ["Succeeded"]}}, "Parse_Work_Items_Response": {"type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@body('Query_Work_Items')", "schema": {"type": "object", "properties": {"workItems": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "url": {"type": "string"}}}}}}}, "runAfter": {"Query_Work_Items": ["Succeeded"]}}, "Condition_Check_Work_Items_Found": {"type": "If", "expression": {"and": [{"greater": ["@length(body('Parse_Work_Items_Response')?['workItems'])", 0]}]}, "actions": {"Get_Work_Item_Details": {"type": "Http", "inputs": {"method": "GET", "uri": "https://dev.azure.com/@{parameters('ado_organization')}/@{encodeUriComponent(parameters('ado_project'))}/_apis/wit/workitems?ids=@{join(select(body('Parse_Work_Items_Response')?['workItems'], item('id')), ',')}&$expand=all&api-version=7.0", "headers": {"Authorization": "Basic @{base64(concat(':', parameters('ado_pat_token')))}", "Content-Type": "application/json"}}}, "Parse_Work_Item_Details": {"type": "<PERSON><PERSON><PERSON><PERSON>", "inputs": {"content": "@body('Get_Work_Item_Details')", "schema": {"type": "object", "properties": {"value": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "fields": {"type": "object"}}}}}}}, "runAfter": {"Get_Work_Item_Details": ["Succeeded"]}}, "Compose_Email_Content": {"type": "Compose", "inputs": {"emailSubject": "📊 Daily Work Items Report - @{length(body('Parse_Work_Item_Details')?['value'])} items created in last 48 hours", "emailBody": "@{variables('EmailBody')}"}, "runAfter": {"Create_Email_Body": ["Succeeded"]}}, "Create_Email_Body": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "EmailBody", "type": "string", "value": "@{concat('<html><body style=\"font-family: Segoe <PERSON>, Arial, sans-serif; line-height: 1.6; color: #333;\"><div style=\"max-width: 800px; margin: 0 auto; padding: 20px;\"><div style=\"background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;\"><h1 style=\"margin: 0; font-size: 24px;\">📊 Daily Work Items Report</h1><p style=\"margin: 10px 0 0 0; opacity: 0.9;\">Virgin Atlantic - Air4 Channels Testing</p></div><div style=\"background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;\"><div style=\"background: white; padding: 15px; border-radius: 6px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);\"><h2 style=\"color: #495057; margin-top: 0;\">📈 Summary</h2><p><strong>Report Period:</strong> ', formatDateTime(addDays(utcNow(), -2), 'MMM dd, yyyy'), ' - ', formatDateTime(utcNow(), 'MMM dd, yyyy'), '</p><p><strong>Total Items:</strong> ', length(body('Parse_Work_Item_Details')?['value']), '</p><p><strong>Generated:</strong> ', formatDateTime(utcNow(), 'MMM dd, yyyy HH:mm'), ' UTC</p></div>')}"}]}, "runAfter": {"Parse_Work_Item_Details": ["Succeeded"]}}, "For_Each_Work_Item": {"type": "Foreach", "foreach": "@body('Parse_Work_Item_Details')?['value']", "actions": {"Append_Work_Item_To_Email": {"type": "AppendToStringVariable", "inputs": {"name": "EmailBody", "value": "@{concat('<div style=\"background: white; padding: 15px; border-radius: 6px; margin-bottom: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid ', if(equals(items('For_Each_Work_Item')?['fields']?['System.WorkItemType'], 'Bug'), '#dc3545', if(equals(items('For_Each_Work_Item')?['fields']?['System.WorkItemType'], 'Defect'), '#fd7e14', '#28a745')), ';\"><div style=\"display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;\"><h3 style=\"margin: 0; color: #495057; font-size: 16px;\">🔗 <a href=\"https://dev.azure.com/', parameters('ado_organization'), '/', encodeUriComponent(parameters('ado_project')), '/_workitems/edit/', items('For_Each_Work_Item')?['id'], '\" style=\"color: #007bff; text-decoration: none;\">', items('For_Each_Work_Item')?['fields']?['System.Title'], '</a></h3><span style=\"background: ', if(equals(items('For_Each_Work_Item')?['fields']?['System.WorkItemType'], 'Bug'), '#dc3545', if(equals(items('For_Each_Work_Item')?['fields']?['System.WorkItemType'], 'Defect'), '#fd7e14', '#28a745')), '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: bold;\">', items('For_Each_Work_Item')?['fields']?['System.WorkItemType'], '</span></div><div style=\"display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;\"><div><strong>ID:</strong> ', items('For_Each_Work_Item')?['id'], '</div><div><strong>State:</strong> <span style=\"color: ', if(equals(items('For_Each_Work_Item')?['fields']?['System.State'], 'New'), '#28a745', if(equals(items('For_Each_Work_Item')?['fields']?['System.State'], 'Active'), '#ffc107', '#6c757d')), ';\">', coalesce(items('For_Each_Work_Item')?['fields']?['System.State'], 'N/A'), '</span></div><div><strong>Priority:</strong> ', coalesce(string(items('For_Each_Work_Item')?['fields']?['Microsoft.VSTS.Common.Priority']), 'N/A'), '</div><div><strong>Severity:</strong> ', coalesce(items('For_Each_Work_Item')?['fields']?['Microsoft.VSTS.Common.Severity'], 'N/A'), '</div><div><strong>Assigned To:</strong> ', coalesce(items('For_Each_Work_Item')?['fields']?['System.AssignedTo']?['displayName'], 'Unassigned'), '</div><div><strong>Created By:</strong> ', coalesce(items('For_Each_Work_Item')?['fields']?['System.CreatedBy']?['displayName'], 'N/A'), '</div></div><div style=\"margin-top: 10px; font-size: 12px; color: #6c757d;\"><strong>Area:</strong> ', coalesce(items('For_Each_Work_Item')?['fields']?['System.AreaPath'], 'N/A'), '<br><strong>Created:</strong> ', formatDateTime(items('For_Each_Work_Item')?['fields']?['System.CreatedDate'], 'MMM dd, yyyy HH:mm'), '</div></div>')}"}}}, "runAfter": {"Create_Email_Body": ["Succeeded"]}}, "Finalize_Email_Body": {"type": "AppendToStringVariable", "inputs": {"name": "EmailBody", "value": "</div><div style=\"background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; border-radius: 0 0 8px 8px;\">This is an automated report from the Auto Defect Triage System<br>Generated on @{formatDateTime(utcNow(), 'MMM dd, yyyy HH:mm')} UTC</div></div></body></html>"}, "runAfter": {"For_Each_Work_Item": ["Succeeded"]}}, "Send_Email": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@parameters('email_recipients')", "Subject": "@outputs('Compose_Email_Content')?['emailSubject']", "Body": "@variables('EmailBody')", "IsHtml": true, "From": "@parameters('email_sender')"}}, "runAfter": {"Finalize_Email_Body": ["Succeeded"]}}}, "else": {"actions": {"Send_No_Items_Email": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@parameters('email_recipients')", "Subject": "✅ Daily Work Items Report - No new items in last 48 hours", "Body": "<html><body style=\"font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;\"><div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\"><div style=\"background: #28a745; color: white; padding: 20px; border-radius: 8px; text-align: center;\"><h1>✅ All Clear!</h1><p>No new Bugs, Defects, or Stories were created in the last 48 hours.</p><p><strong>Report Period:</strong> @{formatDateTime(addDays(utcNow(), -2), 'MMM dd, yyyy')} - @{formatDateTime(utcNow(), 'MMM dd, yyyy')}</p><p style=\"font-size: 12px; opacity: 0.9;\">Generated on @{formatDateTime(utcNow(), 'MMM dd, yyyy HH:mm')} UTC</p></div></div></body></html>", "IsHtml": true, "From": "@parameters('email_sender')"}}}}}, "runAfter": {"Parse_Work_Items_Response": ["Succeeded"]}}}}