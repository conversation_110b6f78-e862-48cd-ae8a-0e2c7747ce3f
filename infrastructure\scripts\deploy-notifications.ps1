#!/usr/bin/env pwsh

<#
.SYNOPSIS
    Deploy the Intelligent Notifications & Reminders service infrastructure

.DESCRIPTION
    This script deploys the notification service infrastructure using Bicep templates.
    It supports multiple environments and handles secure parameter input.

.PARAMETER Environment
    Target environment (dev, staging, prod)

.PARAMETER ResourceGroupName
    Name of the resource group to deploy to

.PARAMETER Location
    Azure region for deployment

.PARAMETER SubscriptionId
    Azure subscription ID

.PARAMETER PagerDutyApiKey
    PagerDuty API key (optional, will prompt if not provided and PagerDuty is enabled)

.PARAMETER TeamsWebhookUrl
    Teams webhook URL (optional, will prompt if not provided)

.PARAMETER AdoPat
    Azure DevOps Personal Access Token (optional, will prompt if not provided)

.PARAMETER OpenAiApiKey
    OpenAI API key (optional, will prompt if not provided)

.PARAMETER WhatIf
    Run deployment in what-if mode to preview changes

.EXAMPLE
    ./deploy-notifications.ps1 -Environment dev -ResourceGroupName "rg-qa-ai-triage-dev" -Location "East US 2"

.EXAMPLE
    ./deploy-notifications.ps1 -Environment prod -ResourceGroupName "rg-qa-ai-triage-prod" -Location "East US 2" -WhatIf
#>

param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("dev", "staging", "prod")]
    [string]$Environment,

    [Parameter(Mandatory = $true)]
    [string]$ResourceGroupName,

    [Parameter(Mandatory = $true)]
    [string]$Location,

    [Parameter(Mandatory = $false)]
    [string]$SubscriptionId,

    [Parameter(Mandatory = $false)]
    [SecureString]$PagerDutyApiKey,

    [Parameter(Mandatory = $false)]
    [SecureString]$TeamsWebhookUrl,

    [Parameter(Mandatory = $false)]
    [SecureString]$AdoPat,

    [Parameter(Mandatory = $false)]
    [SecureString]$OpenAiApiKey,

    [Parameter(Mandatory = $false)]
    [switch]$WhatIf
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Script variables
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$BicepDir = Split-Path -Parent $ScriptDir
$TemplateFile = Join-Path $BicepDir "main-notification-deployment.bicep"
$ParametersFile = Join-Path $BicepDir "parameters" "$Environment.bicepparam"

Write-Host "🚀 Starting deployment of Notification Service for environment: $Environment" -ForegroundColor Green

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Host "✅ Azure CLI version: $($azVersion.'azure-cli')" -ForegroundColor Green
}
catch {
    Write-Error "❌ Azure CLI is not installed or not in PATH. Please install Azure CLI first."
    exit 1
}

# Check if Bicep is available
try {
    az bicep version
    Write-Host "✅ Bicep is available" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ Installing Bicep..." -ForegroundColor Yellow
    az bicep install
}

# Set subscription if provided
if ($SubscriptionId) {
    Write-Host "🔧 Setting subscription to: $SubscriptionId" -ForegroundColor Blue
    az account set --subscription $SubscriptionId
}

# Get current subscription info
$currentSub = az account show --output json | ConvertFrom-Json
Write-Host "📋 Current subscription: $($currentSub.name) ($($currentSub.id))" -ForegroundColor Blue

# Check if resource group exists
$rgExists = az group exists --name $ResourceGroupName --output tsv
if ($rgExists -eq "false") {
    Write-Host "📦 Creating resource group: $ResourceGroupName" -ForegroundColor Yellow
    az group create --name $ResourceGroupName --location $Location --output none
    if ($LASTEXITCODE -ne 0) {
        Write-Error "❌ Failed to create resource group"
        exit 1
    }
    Write-Host "✅ Resource group created successfully" -ForegroundColor Green
}
else {
    Write-Host "✅ Resource group already exists: $ResourceGroupName" -ForegroundColor Green
}

# Prepare deployment parameters
$deploymentParams = @()

# Handle secure parameters
if (-not $TeamsWebhookUrl) {
    $TeamsWebhookUrl = Read-Host "Enter Teams webhook URL" -AsSecureString
}

if (-not $AdoPat) {
    $AdoPat = Read-Host "Enter Azure DevOps PAT" -AsSecureString
}

if (-not $OpenAiApiKey) {
    $OpenAiApiKey = Read-Host "Enter OpenAI API key" -AsSecureString
}

# Check if PagerDuty is enabled for this environment
$enablePagerDuty = $false
if ($Environment -eq "prod") {
    $enablePagerDuty = $true
    if (-not $PagerDutyApiKey) {
        $PagerDutyApiKey = Read-Host "Enter PagerDuty API key (required for production)" -AsSecureString
    }
}

# Convert secure strings to plain text for Azure CLI (Azure CLI handles the security)
$teamsWebhookPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($TeamsWebhookUrl))
$adoPatPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($AdoPat))
$openAiKeyPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($OpenAiApiKey))

$deploymentParams += "teamsWebhookUrl=$teamsWebhookPlain"
$deploymentParams += "adoPat=$adoPatPlain"
$deploymentParams += "openAiApiKey=$openAiKeyPlain"

if ($enablePagerDuty -and $PagerDutyApiKey) {
    $pagerDutyKeyPlain = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($PagerDutyApiKey))
    $deploymentParams += "pagerDutyApiKey=$pagerDutyKeyPlain"
}

# Generate deployment name
$deploymentName = "notification-service-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

Write-Host "🔧 Deployment configuration:" -ForegroundColor Blue
Write-Host "  Environment: $Environment" -ForegroundColor Gray
Write-Host "  Resource Group: $ResourceGroupName" -ForegroundColor Gray
Write-Host "  Location: $Location" -ForegroundColor Gray
Write-Host "  Template: $TemplateFile" -ForegroundColor Gray
Write-Host "  Parameters: $ParametersFile" -ForegroundColor Gray
Write-Host "  Deployment Name: $deploymentName" -ForegroundColor Gray
Write-Host "  PagerDuty Enabled: $enablePagerDuty" -ForegroundColor Gray
Write-Host "  What-If Mode: $WhatIf" -ForegroundColor Gray

# Validate template
Write-Host "🔍 Validating Bicep template..." -ForegroundColor Blue
$validateCmd = @(
    "az", "deployment", "group", "validate"
    "--resource-group", $ResourceGroupName
    "--template-file", $TemplateFile
    "--parameters", $ParametersFile
)

foreach ($param in $deploymentParams) {
    $validateCmd += "--parameters"
    $validateCmd += $param
}

$validateResult = & $validateCmd[0] $validateCmd[1..($validateCmd.Length-1)] 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Template validation failed: $validateResult"
    exit 1
}
Write-Host "✅ Template validation successful" -ForegroundColor Green

# Run deployment
if ($WhatIf) {
    Write-Host "🔍 Running what-if deployment..." -ForegroundColor Blue
    $deployCmd = @(
        "az", "deployment", "group", "what-if"
        "--resource-group", $ResourceGroupName
        "--template-file", $TemplateFile
        "--parameters", $ParametersFile
        "--name", $deploymentName
    )
}
else {
    Write-Host "🚀 Starting deployment..." -ForegroundColor Blue
    $deployCmd = @(
        "az", "deployment", "group", "create"
        "--resource-group", $ResourceGroupName
        "--template-file", $TemplateFile
        "--parameters", $ParametersFile
        "--name", $deploymentName
        "--output", "json"
    )
}

foreach ($param in $deploymentParams) {
    $deployCmd += "--parameters"
    $deployCmd += $param
}

$deployResult = & $deployCmd[0] $deployCmd[1..($deployCmd.Length-1)]
if ($LASTEXITCODE -ne 0) {
    Write-Error "❌ Deployment failed: $deployResult"
    exit 1
}

if (-not $WhatIf) {
    Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
    
    # Parse and display outputs
    try {
        $outputs = $deployResult | ConvertFrom-Json
        if ($outputs.properties.outputs) {
            Write-Host "📋 Deployment Outputs:" -ForegroundColor Blue
            
            if ($outputs.properties.outputs.deploymentSummary) {
                $summary = $outputs.properties.outputs.deploymentSummary.value
                Write-Host "  Function App: $($summary.functionAppName)" -ForegroundColor Gray
                Write-Host "  Function App URL: $($summary.functionAppUrl)" -ForegroundColor Gray
                Write-Host "  Monitoring Enabled: $($summary.monitoringEnabled)" -ForegroundColor Gray
                Write-Host "  PagerDuty Enabled: $($summary.pagerDutyEnabled)" -ForegroundColor Gray
            }
            
            if ($outputs.properties.outputs.endpoints) {
                $endpoints = $outputs.properties.outputs.endpoints.value
                Write-Host "🔗 API Endpoints:" -ForegroundColor Blue
                Write-Host "  Critical Notification: $($endpoints.criticalNotification)" -ForegroundColor Gray
                Write-Host "  Notification Webhook: $($endpoints.notificationWebhook)" -ForegroundColor Gray
                Write-Host "  Health Check: $($endpoints.healthCheck)" -ForegroundColor Gray
            }
            
            if ($outputs.properties.outputs.configurationNotes) {
                $notes = $outputs.properties.outputs.configurationNotes.value
                Write-Host "📝 Configuration Notes:" -ForegroundColor Blue
                foreach ($note in $notes) {
                    Write-Host "  • $note" -ForegroundColor Gray
                }
            }
        }
    }
    catch {
        Write-Host "⚠️ Could not parse deployment outputs, but deployment was successful" -ForegroundColor Yellow
    }
    
    Write-Host "🎉 Notification service deployment completed for $Environment environment!" -ForegroundColor Green
}
else {
    Write-Host "✅ What-if analysis completed" -ForegroundColor Green
}

# Clear sensitive variables
$teamsWebhookPlain = $null
$adoPatPlain = $null
$openAiKeyPlain = $null
if ($pagerDutyKeyPlain) { $pagerDutyKeyPlain = $null }
