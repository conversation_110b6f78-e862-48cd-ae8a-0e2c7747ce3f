"""
AutoDefectTriage Workflow Orchestrator
=====================================

Step-by-step workflow orchestration for the complete defect triage process:
1. Work Item Creation Trigger
2. Historical Analysis & Pattern Recognition  
3. AI-Powered Notification Message Generation
4. Email Notification Delivery
5. Teams Message Broadcasting

This orchestrator coordinates all components to ensure proper sequencing and error handling.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from .common.models.schemas import WorkItem, TriageResult
from .common.adapters.ado_client import AdoClient
from .common.adapters.search_client import SearchClient
from .common.adapters.teams_client import TeamsClient
from .common.ai.duplicate import DuplicateDetector
from .common.ai.assigner import AssignmentEngine
from .common.ai.priority import PriorityEngine
from .common.notifications.message_generator import MessageGenerator
from .common.notifications.notification_engine import NotificationEngine
from .common.utils.config import get_config
from .common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


@dataclass
class WorkflowContext:
    """Context object that carries data through the workflow steps."""
    work_item: WorkItem
    historical_items: List[Dict[str, Any]]
    triage_result: Optional[TriageResult]
    ai_message: Optional[str]
    notification_ids: List[str]
    email_sent: bool
    teams_sent: bool
    errors: List[str]
    start_time: datetime
    step_timings: Dict[str, float]


class WorkflowOrchestrator:
    """
    Main orchestrator for the step-by-step defect triage workflow.
    
    Coordinates the complete process from work item creation to final notifications.
    """
    
    def __init__(self):
        self.config = get_config()
        self.clients = {}
        self.ai_engines = {}
        self.notification_engine = None
        
    async def initialize(self):
        """Initialize all required clients and AI engines."""
        try:
            log_structured(logger, "info", "Initializing workflow orchestrator")
            
            # Initialize clients
            self.clients = {
                'ado': AdoClient(self.config),
                'search': SearchClient(self.config),
                'teams': TeamsClient(self.config)
            }
            
            # Initialize AI engines
            self.ai_engines = {
                'duplicate': DuplicateDetector(self.clients['search'], self.config),
                'assigner': AssignmentEngine(self.clients['search'], self.config),
                'priority': PriorityEngine(self.config)
            }
            
            # Initialize message generator
            self.message_generator = MessageGenerator(
                self.config,
                self.ai_engines['duplicate'],
                self.ai_engines['assigner'],
                self.ai_engines['priority'],
                self.clients['search']
            )
            
            # Initialize notification engine
            self.notification_engine = NotificationEngine(
                self.config,
                self.clients['ado'],
                self.clients['teams'],
                search_client=self.clients['search'],
                duplicate_detector=self.ai_engines['duplicate'],
                assignment_engine=self.ai_engines['assigner'],
                priority_engine=self.ai_engines['priority']
            )
            
            log_structured(logger, "info", "Workflow orchestrator initialized successfully")
            
        except Exception as e:
            log_structured(logger, "error", f"Failed to initialize workflow orchestrator: {e}", exc_info=True)
            raise
    
    async def execute_workflow(self, work_item_id: str) -> WorkflowContext:
        """
        Execute the complete step-by-step workflow for a work item.
        
        Args:
            work_item_id: ID of the work item to process
            
        Returns:
            WorkflowContext with results from all steps
        """
        context = WorkflowContext(
            work_item=None,
            historical_items=[],
            triage_result=None,
            ai_message=None,
            notification_ids=[],
            email_sent=False,
            teams_sent=False,
            errors=[],
            start_time=datetime.utcnow(),
            step_timings={}
        )
        
        try:
            log_structured(
                logger, 
                "info", 
                f"Starting workflow execution for work item: {work_item_id}",
                extra={"work_item_id": work_item_id}
            )
            
            # Step 1: Work Item Creation/Retrieval
            context = await self._step_1_work_item_creation(context, work_item_id)
            
            # Step 2: Historical Analysis
            context = await self._step_2_historical_analysis(context)
            
            # Step 3: AI Triage & Message Generation
            context = await self._step_3_ai_triage_and_message_generation(context)
            
            # Step 4: Email Notification
            context = await self._step_4_email_notification(context)
            
            # Step 5: Teams Message
            context = await self._step_5_teams_message(context)
            
            # Log workflow completion
            total_time = (datetime.utcnow() - context.start_time).total_seconds()
            log_structured(
                logger,
                "info",
                f"Workflow completed successfully for work item: {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "total_time_seconds": total_time,
                    "step_timings": context.step_timings,
                    "email_sent": context.email_sent,
                    "teams_sent": context.teams_sent,
                    "errors_count": len(context.errors)
                }
            )
            
            return context
            
        except Exception as e:
            context.errors.append(f"Workflow execution failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Workflow execution failed for work item: {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "error": str(e),
                    "errors": context.errors
                },
                exc_info=True
            )
            return context
    
    async def _step_1_work_item_creation(self, context: WorkflowContext, work_item_id: str) -> WorkflowContext:
        """
        Step 1: Work Item Creation/Retrieval
        
        Retrieves the work item from Azure DevOps and validates it.
        """
        step_start = datetime.utcnow()
        
        try:
            log_structured(logger, "info", f"Step 1: Retrieving work item {work_item_id}")
            
            # Get work item from ADO
            work_item_data = await self.clients['ado'].get_work_item(work_item_id)
            
            if not work_item_data:
                raise ValueError(f"Work item {work_item_id} not found")
            
            # Convert to WorkItem object
            context.work_item = WorkItem(
                id=work_item_data.get('id'),
                title=work_item_data.get('fields', {}).get('System.Title', ''),
                description=work_item_data.get('fields', {}).get('System.Description', ''),
                work_item_type=work_item_data.get('fields', {}).get('System.WorkItemType', ''),
                state=work_item_data.get('fields', {}).get('System.State', ''),
                assigned_to=work_item_data.get('fields', {}).get('System.AssignedTo', {}).get('displayName', ''),
                created_date=work_item_data.get('fields', {}).get('System.CreatedDate', ''),
                project=work_item_data.get('fields', {}).get('System.TeamProject', ''),
                priority=work_item_data.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 2),
                severity=work_item_data.get('fields', {}).get('Microsoft.VSTS.Common.Severity', ''),
                tags=work_item_data.get('fields', {}).get('System.Tags', ''),
                area_path=work_item_data.get('fields', {}).get('System.AreaPath', ''),
                iteration_path=work_item_data.get('fields', {}).get('System.IterationPath', '')
            )
            
            step_time = (datetime.utcnow() - step_start).total_seconds()
            context.step_timings['step_1_work_item_creation'] = step_time
            
            log_structured(
                logger,
                "info",
                f"Step 1 completed: Work item retrieved",
                extra={
                    "work_item_id": work_item_id,
                    "title": context.work_item.title,
                    "type": context.work_item.work_item_type,
                    "step_time_seconds": step_time
                }
            )
            
        except Exception as e:
            context.errors.append(f"Step 1 failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Step 1 failed for work item {work_item_id}: {e}",
                exc_info=True
            )
        
        return context
    
    async def _step_2_historical_analysis(self, context: WorkflowContext) -> WorkflowContext:
        """
        Step 2: Historical Analysis & Pattern Recognition
        
        Analyzes similar historical work items to provide context.
        """
        step_start = datetime.utcnow()
        
        try:
            if not context.work_item:
                raise ValueError("Work item not available from Step 1")
            
            log_structured(
                logger, 
                "info", 
                f"Step 2: Analyzing historical patterns for work item {context.work_item.id}"
            )
            
            # Find similar historical items
            similar_items = await self.ai_engines['duplicate'].find_duplicates(context.work_item)
            
            # Convert to dictionary format for easier processing
            context.historical_items = []
            if similar_items:
                for item in similar_items:
                    context.historical_items.append({
                        'id': item.work_item_id,
                        'title': item.title,
                        'similarity_score': item.similarity_score,
                        'resolution': getattr(item, 'resolution', ''),
                        'assigned_to': getattr(item, 'assigned_to', ''),
                        'resolution_time': getattr(item, 'resolution_time', None)
                    })
            
            step_time = (datetime.utcnow() - step_start).total_seconds()
            context.step_timings['step_2_historical_analysis'] = step_time
            
            log_structured(
                logger,
                "info",
                f"Step 2 completed: Historical analysis",
                extra={
                    "work_item_id": context.work_item.id,
                    "similar_items_found": len(context.historical_items),
                    "step_time_seconds": step_time
                }
            )
            
        except Exception as e:
            context.errors.append(f"Step 2 failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Step 2 failed for work item {context.work_item.id if context.work_item else 'unknown'}: {e}",
                exc_info=True
            )
        
        return context

    async def _step_3_ai_triage_and_message_generation(self, context: WorkflowContext) -> WorkflowContext:
        """
        Step 3: AI Triage & Message Generation

        Runs AI triage pipeline and generates intelligent notification messages.
        """
        step_start = datetime.utcnow()

        try:
            if not context.work_item:
                raise ValueError("Work item not available from Step 1")

            log_structured(
                logger,
                "info",
                f"Step 3: Running AI triage and message generation for work item {context.work_item.id}"
            )

            # Run AI triage pipeline
            duplicates = await self.ai_engines['duplicate'].find_duplicates(context.work_item)
            priority = await self.ai_engines['priority'].calculate_priority(context.work_item)
            assignment = await self.ai_engines['assigner'].assign_work_item(context.work_item)

            # Create triage result
            reasoning = assignment.get('reasoning', '')
            if isinstance(reasoning, list):
                reasoning = '; '.join(reasoning)

            context.triage_result = TriageResult(
                work_item_id=context.work_item.id,
                assigned_to=assignment.get('assigned_to', ''),
                priority=priority,
                duplicates=[dup.dict() for dup in duplicates] if duplicates else [],
                confidence_score=assignment.get('confidence', 0.0),
                reasoning=reasoning
            )

            # Generate AI-powered notification message
            from .common.models import NotificationTrigger
            trigger = NotificationTrigger(
                trigger_type="work_item_created",
                work_item_id=context.work_item.id,
                project=context.work_item.project,
                trigger_conditions={"event": "created"},
                severity_score=0.8,
                escalation_level="normal",
                created_at=datetime.utcnow()
            )

            notification_context = await self.message_generator.generate_notification_context(
                context.work_item, trigger
            )

            context.ai_message = notification_context.message_content

            step_time = (datetime.utcnow() - step_start).total_seconds()
            context.step_timings['step_3_ai_triage_and_message_generation'] = step_time

            log_structured(
                logger,
                "info",
                f"Step 3 completed: AI triage and message generation",
                extra={
                    "work_item_id": context.work_item.id,
                    "assigned_to": context.triage_result.assigned_to,
                    "priority": context.triage_result.priority,
                    "confidence_score": context.triage_result.confidence_score,
                    "duplicates_found": len(context.triage_result.duplicates),
                    "step_time_seconds": step_time
                }
            )

        except Exception as e:
            context.errors.append(f"Step 3 failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Step 3 failed for work item {context.work_item.id if context.work_item else 'unknown'}: {e}",
                exc_info=True
            )

        return context

    async def _step_4_email_notification(self, context: WorkflowContext) -> WorkflowContext:
        """
        Step 4: Email Notification

        Sends professional email notifications with work item details.
        """
        step_start = datetime.utcnow()

        try:
            if not context.work_item:
                raise ValueError("Work item not available from Step 1")

            log_structured(
                logger,
                "info",
                f"Step 4: Sending email notification for work item {context.work_item.id}"
            )

            # Prepare email content
            email_data = {
                "work_item": context.work_item.dict(),
                "triage_result": context.triage_result.dict() if context.triage_result else None,
                "historical_items": context.historical_items,
                "ai_message": context.ai_message,
                "timestamp": datetime.utcnow().isoformat()
            }

            # Note: Email sending would typically be handled by Logic Apps
            # For now, we'll simulate the email sending process
            # In production, this would trigger the Logic App workflow

            # Simulate email sending (replace with actual Logic App trigger)
            await self._trigger_email_logic_app(email_data)
            context.email_sent = True

            step_time = (datetime.utcnow() - step_start).total_seconds()
            context.step_timings['step_4_email_notification'] = step_time

            log_structured(
                logger,
                "info",
                f"Step 4 completed: Email notification sent",
                extra={
                    "work_item_id": context.work_item.id,
                    "email_sent": context.email_sent,
                    "step_time_seconds": step_time
                }
            )

        except Exception as e:
            context.errors.append(f"Step 4 failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Step 4 failed for work item {context.work_item.id if context.work_item else 'unknown'}: {e}",
                exc_info=True
            )

        return context

    async def _step_5_teams_message(self, context: WorkflowContext) -> WorkflowContext:
        """
        Step 5: Teams Message Broadcasting

        Sends Teams adaptive cards with intelligent notifications.
        """
        step_start = datetime.utcnow()

        try:
            if not context.work_item:
                raise ValueError("Work item not available from Step 1")

            log_structured(
                logger,
                "info",
                f"Step 5: Sending Teams message for work item {context.work_item.id}"
            )

            # Send Teams notification using the notification engine
            if context.triage_result:
                await self.clients['teams'].send_triage_notification(
                    context.work_item,
                    context.triage_result
                )
                context.teams_sent = True

            step_time = (datetime.utcnow() - step_start).total_seconds()
            context.step_timings['step_5_teams_message'] = step_time

            log_structured(
                logger,
                "info",
                f"Step 5 completed: Teams message sent",
                extra={
                    "work_item_id": context.work_item.id,
                    "teams_sent": context.teams_sent,
                    "step_time_seconds": step_time
                }
            )

        except Exception as e:
            context.errors.append(f"Step 5 failed: {str(e)}")
            log_structured(
                logger,
                "error",
                f"Step 5 failed for work item {context.work_item.id if context.work_item else 'unknown'}: {e}",
                exc_info=True
            )

        return context

    async def _trigger_email_logic_app(self, email_data: Dict[str, Any]) -> bool:
        """
        Trigger the Logic App for email notifications.

        Args:
            email_data: Data to send in the email

        Returns:
            True if successfully triggered, False otherwise
        """
        try:
            # This would typically make an HTTP request to trigger the Logic App
            # For now, we'll log the action
            log_structured(
                logger,
                "info",
                "Email Logic App triggered",
                extra={
                    "work_item_id": email_data.get("work_item", {}).get("id"),
                    "has_triage_result": email_data.get("triage_result") is not None,
                    "historical_items_count": len(email_data.get("historical_items", []))
                }
            )

            # Simulate successful trigger
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to trigger email Logic App: {e}",
                exc_info=True
            )
            return False


# Workflow execution functions for Azure Functions integration

async def execute_workflow_for_work_item(work_item_id: str) -> Dict[str, Any]:
    """
    Execute the complete workflow for a single work item.

    Args:
        work_item_id: ID of the work item to process

    Returns:
        Dictionary with workflow results
    """
    orchestrator = WorkflowOrchestrator()
    await orchestrator.initialize()

    context = await orchestrator.execute_workflow(work_item_id)

    return {
        "work_item_id": work_item_id,
        "success": len(context.errors) == 0,
        "errors": context.errors,
        "email_sent": context.email_sent,
        "teams_sent": context.teams_sent,
        "step_timings": context.step_timings,
        "total_time": sum(context.step_timings.values()),
        "triage_result": context.triage_result.dict() if context.triage_result else None
    }


async def execute_workflow_for_recent_items(hours_back: int = 24) -> Dict[str, Any]:
    """
    Execute workflow for all work items created in the last N hours.

    Args:
        hours_back: Number of hours to look back for work items

    Returns:
        Dictionary with batch processing results
    """
    orchestrator = WorkflowOrchestrator()
    await orchestrator.initialize()

    # Get recent work items
    since_date = datetime.utcnow() - timedelta(hours=hours_back)
    work_items = await orchestrator.clients['ado'].get_work_items_since(since_date)

    results = []
    for work_item_data in work_items:
        work_item_id = work_item_data.get('id')
        if work_item_id:
            context = await orchestrator.execute_workflow(str(work_item_id))
            results.append({
                "work_item_id": work_item_id,
                "success": len(context.errors) == 0,
                "errors": context.errors,
                "email_sent": context.email_sent,
                "teams_sent": context.teams_sent
            })

    return {
        "total_items_processed": len(results),
        "successful_items": len([r for r in results if r["success"]]),
        "failed_items": len([r for r in results if not r["success"]]),
        "results": results
    }
