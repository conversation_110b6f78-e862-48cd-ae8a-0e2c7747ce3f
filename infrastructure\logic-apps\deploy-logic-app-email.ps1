# Deploy Logic App for Email Notifications
# This script deploys the Logic App to send daily email reports for work items

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$LogicAppName,
    
    [Parameter(Mandatory=$true)]
    [string]$Location = "UK South",
    
    [Parameter(Mandatory=$true)]
    [string]$AdoPatToken,
    
    [Parameter(Mandatory=$true)]
    [string]$EmailRecipients,
    
    [Parameter(Mandatory=$false)]
    [string]$EmailSender = "<EMAIL>",
    
    [Parameter(Mandatory=$false)]
    [string]$AdoOrganization = "virginatlantic",
    
    [Parameter(Mandatory=$false)]
    [string]$AdoProject = "Air4 Channels Testing"
)

Write-Host "🚀 Deploying Logic App Email Notification System..." -ForegroundColor Green
Write-Host "📧 Logic App Name: $LogicAppName" -ForegroundColor Cyan
Write-Host "📍 Location: $Location" -ForegroundColor Cyan
Write-Host "👥 Recipients: $EmailRecipients" -ForegroundColor Cyan

# Check if Azure CLI is installed
try {
    az --version | Out-Null
    Write-Host "✅ Azure CLI found" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found. Please install Azure CLI first." -ForegroundColor Red
    exit 1
}

# Login to Azure (if not already logged in)
Write-Host "🔐 Checking Azure login status..." -ForegroundColor Yellow
$loginStatus = az account show 2>$null
if (-not $loginStatus) {
    Write-Host "🔐 Please login to Azure..." -ForegroundColor Yellow
    az login
}

# Create Resource Group if it doesn't exist
Write-Host "📦 Checking/Creating Resource Group: $ResourceGroupName..." -ForegroundColor Yellow
$rgExists = az group exists --name $ResourceGroupName
if ($rgExists -eq "false") {
    Write-Host "📦 Creating Resource Group: $ResourceGroupName..." -ForegroundColor Yellow
    az group create --name $ResourceGroupName --location $Location
    Write-Host "✅ Resource Group created successfully" -ForegroundColor Green
} else {
    Write-Host "✅ Resource Group already exists" -ForegroundColor Green
}

# Create Office 365 API Connection
$office365ConnectionName = "$LogicAppName-office365-connection"
Write-Host "📧 Creating Office 365 API Connection: $office365ConnectionName..." -ForegroundColor Yellow

$office365ConnectionJson = @"
{
    "properties": {
        "displayName": "$office365ConnectionName",
        "api": {
            "id": "/subscriptions/$(az account show --query id -o tsv)/providers/Microsoft.Web/locations/$Location/managedApis/office365"
        }
    }
}
"@

# Deploy Office 365 Connection
az resource create `
    --resource-group $ResourceGroupName `
    --resource-type "Microsoft.Web/connections" `
    --name $office365ConnectionName `
    --properties $office365ConnectionJson `
    --location $Location

Write-Host "✅ Office 365 API Connection created" -ForegroundColor Green

# Get the connection ID
$connectionId = az resource show `
    --resource-group $ResourceGroupName `
    --resource-type "Microsoft.Web/connections" `
    --name $office365ConnectionName `
    --query "id" -o tsv

Write-Host "🔗 Connection ID: $connectionId" -ForegroundColor Cyan

# Read the Logic App definition
$logicAppDefinitionPath = "logic-app-email-notification.json"
if (-not (Test-Path $logicAppDefinitionPath)) {
    Write-Host "❌ Logic App definition file not found: $logicAppDefinitionPath" -ForegroundColor Red
    exit 1
}

$logicAppDefinition = Get-Content $logicAppDefinitionPath -Raw | ConvertFrom-Json

# Update parameters in the definition
$logicAppDefinition.parameters.'$connections'.defaultValue = @{
    office365 = @{
        connectionId = $connectionId
        connectionName = $office365ConnectionName
        id = "/subscriptions/$(az account show --query id -o tsv)/providers/Microsoft.Web/locations/$Location/managedApis/office365"
    }
}

# Convert back to JSON
$updatedDefinition = $logicAppDefinition | ConvertTo-Json -Depth 20

# Create temporary file with updated definition
$tempDefinitionPath = "temp-logic-app-definition.json"
$updatedDefinition | Out-File -FilePath $tempDefinitionPath -Encoding UTF8

Write-Host "📋 Creating Logic App: $LogicAppName..." -ForegroundColor Yellow

# Create the Logic App
az logic workflow create `
    --resource-group $ResourceGroupName `
    --name $LogicAppName `
    --location $Location `
    --definition $tempDefinitionPath `
    --parameters ado_organization=$AdoOrganization `
    --parameters ado_project=$AdoProject `
    --parameters ado_pat_token=$AdoPatToken `
    --parameters email_recipients=$EmailRecipients `
    --parameters email_sender=$EmailSender

# Clean up temporary file
Remove-Item $tempDefinitionPath -Force

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Logic App created successfully!" -ForegroundColor Green
    
    # Get the Logic App URL
    $logicAppUrl = az logic workflow show `
        --resource-group $ResourceGroupName `
        --name $LogicAppName `
        --query "accessEndpoint" -o tsv
    
    Write-Host "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "=" * 50 -ForegroundColor Green
    Write-Host "📧 Logic App Name: $LogicAppName" -ForegroundColor Cyan
    Write-Host "🔗 Logic App URL: $logicAppUrl" -ForegroundColor Cyan
    Write-Host "📅 Schedule: Daily at 9:00 AM GMT" -ForegroundColor Cyan
    Write-Host "👥 Recipients: $EmailRecipients" -ForegroundColor Cyan
    Write-Host "📊 Scope: Bugs, Defects, Stories created in last 48 hours" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "⚠️  IMPORTANT: You need to authorize the Office 365 connection:" -ForegroundColor Yellow
    Write-Host "1. Go to Azure Portal" -ForegroundColor White
    Write-Host "2. Navigate to Resource Group: $ResourceGroupName" -ForegroundColor White
    Write-Host "3. Open API Connection: $office365ConnectionName" -ForegroundColor White
    Write-Host "4. Click 'Edit API connection' and authorize" -ForegroundColor White
    Write-Host ""
    Write-Host "🧪 To test the Logic App:" -ForegroundColor Yellow
    Write-Host "1. Go to the Logic App in Azure Portal" -ForegroundColor White
    Write-Host "2. Click 'Run Trigger' -> 'Recurrence'" -ForegroundColor White
    
} else {
    Write-Host "❌ Logic App deployment failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Authorize the Office 365 connection in Azure Portal" -ForegroundColor White
Write-Host "2. Test the Logic App manually" -ForegroundColor White
Write-Host "3. Monitor the daily runs at 9:00 AM GMT" -ForegroundColor White
Write-Host "4. Customize email recipients or schedule as needed" -ForegroundColor White
