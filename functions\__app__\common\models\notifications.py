"""
Pydantic models for the Intelligent Notifications & Reminders system.
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, <PERSON>, validator
from enum import Enum

from .schemas import WorkItem, TeamMember, Priority


class TriggerType(str, Enum):
    """Types of notification triggers."""
    CRITICAL_CREATED = "critical_created"
    AGING = "aging"
    ESCALATION = "escalation"
    ASSIGNMENT_SUGGESTION = "assignment_suggestion"
    DUPLICATE_DETECTED = "duplicate_detected"
    SECURITY_ALERT = "security_alert"


class RecipientType(str, Enum):
    """Types of notification recipients."""
    USER = "user"
    TEAM_CHANNEL = "team_channel"
    PAGERDUTY = "pagerduty"
    EMAIL = "email"
    WEBHOOK = "webhook"


class DeliveryMethod(str, Enum):
    """Notification delivery methods."""
    TEAMS_ADAPTIVE_CARD = "teams_adaptive_card"
    TEAMS_MESSAGE = "teams_message"
    EMAIL_HTML = "email_html"
    EMAIL_TEXT = "email_text"
    PAGERDUTY_INCIDENT = "pagerduty_incident"
    WEBHOOK_JSON = "webhook_json"


class NotificationStatus(str, Enum):
    """Notification delivery status."""
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    RATE_LIMITED = "rate_limited"
    DEDUPLICATED = "deduplicated"
    QUIET_HOURS = "quiet_hours"


class EscalationLevel(int, Enum):
    """Escalation levels for notifications."""
    NORMAL = 0
    ELEVATED = 1
    URGENT = 2
    CRITICAL = 3


class NotificationTrigger(BaseModel):
    """Represents a notification trigger condition."""
    
    trigger_type: TriggerType = Field(..., description="Type of trigger")
    work_item_id: int = Field(..., description="Work item ID")
    project: str = Field(..., description="Project name")
    trigger_conditions: Dict[str, Any] = Field(default_factory=dict, description="Trigger conditions")
    severity_score: float = Field(0.0, description="Calculated severity score")
    escalation_level: EscalationLevel = Field(EscalationLevel.NORMAL, description="Escalation level")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Trigger creation time")
    expires_at: Optional[datetime] = Field(None, description="Trigger expiration time")
    
    @validator('severity_score')
    def validate_severity_score(cls, v):
        return max(0.0, min(10.0, v))
    
    class Config:
        use_enum_values = True


class StakeholderRoute(BaseModel):
    """Represents a stakeholder routing decision."""
    
    recipient_type: RecipientType = Field(..., description="Type of recipient")
    recipient_id: str = Field(..., description="Recipient identifier")
    recipient_name: Optional[str] = Field(None, description="Recipient display name")
    priority: int = Field(1, description="Routing priority (1=highest)")
    escalation_level: EscalationLevel = Field(EscalationLevel.NORMAL, description="Escalation level")
    delivery_method: DeliveryMethod = Field(..., description="Delivery method")
    routing_reason: str = Field("", description="Reason for this routing")
    confidence_score: float = Field(0.0, description="Confidence in routing decision")
    
    @validator('priority')
    def validate_priority(cls, v):
        return max(1, v)
    
    @validator('confidence_score')
    def validate_confidence_score(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class AssignmentSuggestion(BaseModel):
    """Represents an assignment suggestion."""
    
    assignee_email: str = Field(..., description="Suggested assignee email")
    assignee_name: str = Field(..., description="Suggested assignee name")
    confidence_score: float = Field(..., description="Confidence in suggestion")
    reasoning: str = Field("", description="Reason for suggestion")
    expertise_match: List[str] = Field(default_factory=list, description="Matching expertise areas")
    historical_success: Optional[float] = Field(None, description="Historical success rate")
    current_load: int = Field(0, description="Current workload")
    availability: float = Field(1.0, description="Availability factor")
    
    @validator('confidence_score')
    def validate_confidence_score(cls, v):
        return max(0.0, min(1.0, v))
    
    @validator('availability')
    def validate_availability(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class SimilarWorkItem(BaseModel):
    """Represents a similar historical work item."""
    
    work_item_id: int = Field(..., description="Work item ID")
    title: str = Field(..., description="Work item title")
    similarity_score: float = Field(..., description="Similarity score")
    resolution: Optional[str] = Field(None, description="How it was resolved")
    assignee: Optional[str] = Field(None, description="Who resolved it")
    resolution_time: Optional[timedelta] = Field(None, description="Time to resolution")
    tags: List[str] = Field(default_factory=list, description="Relevant tags")
    
    @validator('similarity_score')
    def validate_similarity_score(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True
        json_encoders = {
            timedelta: lambda v: v.total_seconds()
        }


class DuplicateCandidate(BaseModel):
    """Represents a potential duplicate work item."""
    
    work_item_id: int = Field(..., description="Potential duplicate work item ID")
    title: str = Field(..., description="Work item title")
    similarity_score: float = Field(..., description="Similarity score")
    state: str = Field(..., description="Current state")
    assignee: Optional[str] = Field(None, description="Current assignee")
    created_date: datetime = Field(..., description="Creation date")
    duplicate_type: str = Field("content", description="Type of duplication detected")
    
    @validator('similarity_score')
    def validate_similarity_score(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class PriorityAnalysis(BaseModel):
    """Represents AI-driven priority analysis."""
    
    suggested_priority: Priority = Field(..., description="Suggested priority")
    confidence_score: float = Field(..., description="Confidence in suggestion")
    factors: Dict[str, float] = Field(default_factory=dict, description="Priority factors")
    reasoning: str = Field("", description="Reasoning for priority")
    customer_impact: bool = Field(False, description="Has customer impact")
    security_impact: bool = Field(False, description="Has security implications")
    blocking_impact: bool = Field(False, description="Blocks other work")
    
    @validator('confidence_score')
    def validate_confidence_score(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class NotificationContext(BaseModel):
    """Complete context for a notification."""
    
    work_item: WorkItem = Field(..., description="Work item being notified about")
    trigger: NotificationTrigger = Field(..., description="Trigger that caused notification")
    suggested_assignees: List[AssignmentSuggestion] = Field(default_factory=list, description="Assignment suggestions")
    similar_items: List[SimilarWorkItem] = Field(default_factory=list, description="Similar historical items")
    priority_analysis: Optional[PriorityAnalysis] = Field(None, description="Priority analysis")
    duplicate_candidates: List[DuplicateCandidate] = Field(default_factory=list, description="Potential duplicates")
    routing_decisions: List[StakeholderRoute] = Field(default_factory=list, description="Routing decisions")
    message_content: Optional[str] = Field(None, description="Generated message content")
    
    class Config:
        use_enum_values = True


class DeduplicationKey(BaseModel):
    """Represents a deduplication key for noise control."""
    
    work_item_id: int = Field(..., description="Work item ID")
    notification_type: TriggerType = Field(..., description="Notification type")
    recipient_id: str = Field(..., description="Recipient identifier")
    key_hash: str = Field(..., description="Computed hash key")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Key creation time")
    expires_at: datetime = Field(..., description="Key expiration time")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class RateLimitBucket(BaseModel):
    """Represents a rate limiting bucket."""
    
    bucket_key: str = Field(..., description="Rate limit bucket key")
    current_count: int = Field(0, description="Current request count")
    max_count: int = Field(..., description="Maximum allowed requests")
    window_start: datetime = Field(default_factory=datetime.utcnow, description="Window start time")
    window_duration: timedelta = Field(..., description="Window duration")
    
    @property
    def is_exceeded(self) -> bool:
        """Check if rate limit is exceeded."""
        return self.current_count >= self.max_count
    
    @property
    def is_expired(self) -> bool:
        """Check if window has expired."""
        return datetime.utcnow() > self.window_start + self.window_duration
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            timedelta: lambda v: v.total_seconds()
        }


class NotificationAudit(BaseModel):
    """Audit record for notification actions."""
    
    notification_id: str = Field(..., description="Unique notification ID")
    work_item_id: int = Field(..., description="Work item ID")
    trigger_type: TriggerType = Field(..., description="Trigger type")
    recipient_type: RecipientType = Field(..., description="Recipient type")
    recipient_id: str = Field(..., description="Recipient identifier")
    delivery_method: DeliveryMethod = Field(..., description="Delivery method")
    status: NotificationStatus = Field(..., description="Delivery status")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation time")
    sent_at: Optional[datetime] = Field(None, description="Send time")
    delivered_at: Optional[datetime] = Field(None, description="Delivery time")
    error_message: Optional[str] = Field(None, description="Error message if failed")
    response_data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class QuietHours(BaseModel):
    """Represents quiet hours configuration."""
    
    start_time: str = Field(..., description="Start time (HH:MM format)")
    end_time: str = Field(..., description="End time (HH:MM format)")
    timezone: str = Field("UTC", description="Timezone")
    days_of_week: List[int] = Field(default_factory=lambda: list(range(7)), description="Days of week (0=Monday)")
    
    @validator('start_time', 'end_time')
    def validate_time_format(cls, v):
        try:
            datetime.strptime(v, '%H:%M')
            return v
        except ValueError:
            raise ValueError('Time must be in HH:MM format')
    
    @validator('days_of_week')
    def validate_days_of_week(cls, v):
        for day in v:
            if not 0 <= day <= 6:
                raise ValueError('Days of week must be 0-6 (0=Monday)')
        return v
    
    class Config:
        use_enum_values = True


class ProjectNotificationConfig(BaseModel):
    """Project-specific notification configuration."""

    project_name: str = Field(..., description="Project name")
    enabled: bool = Field(True, description="Whether notifications are enabled")

    # Trigger configuration
    critical_priorities: List[int] = Field(default_factory=lambda: [1, 2], description="Critical priority levels")
    aging_thresholds: Dict[str, str] = Field(
        default_factory=lambda: {"P1": "4h", "P2": "24h", "P3": "72h", "P4": "168h"},
        description="Aging thresholds by priority"
    )
    security_keywords: List[str] = Field(
        default_factory=lambda: ["security", "vulnerability", "exploit", "breach"],
        description="Keywords that trigger security alerts"
    )
    customer_impact_keywords: List[str] = Field(
        default_factory=lambda: ["customer", "production", "outage", "critical"],
        description="Keywords that indicate customer impact"
    )

    # Routing configuration
    default_team_channel: Optional[str] = Field(None, description="Default Teams channel webhook URL")
    escalation_managers: List[str] = Field(default_factory=list, description="Escalation manager emails")
    pagerduty_service_key: Optional[str] = Field(None, description="PagerDuty service key")

    # Noise control configuration
    quiet_hours: Optional[QuietHours] = Field(None, description="Quiet hours configuration")
    rate_limits: Dict[str, int] = Field(
        default_factory=lambda: {
            "per_user_per_hour": 5,
            "per_channel_per_hour": 20,
            "per_pagerduty_per_hour": 10
        },
        description="Rate limits"
    )
    dedup_window_minutes: int = Field(60, description="Deduplication window in minutes")

    # Escalation configuration
    escalation_delays: Dict[str, str] = Field(
        default_factory=lambda: {
            "elevated": "30m",
            "urgent": "15m",
            "critical": "5m"
        },
        description="Escalation delays by level"
    )

    class Config:
        use_enum_values = True


class NotificationRule(BaseModel):
    """Represents a notification rule."""

    rule_id: str = Field(..., description="Unique rule identifier")
    name: str = Field(..., description="Rule name")
    description: str = Field("", description="Rule description")
    enabled: bool = Field(True, description="Whether rule is enabled")

    # Conditions
    trigger_types: List[TriggerType] = Field(..., description="Trigger types this rule applies to")
    work_item_types: List[str] = Field(default_factory=list, description="Work item types (empty = all)")
    priorities: List[int] = Field(default_factory=list, description="Priorities (empty = all)")
    projects: List[str] = Field(default_factory=list, description="Projects (empty = all)")
    tags: List[str] = Field(default_factory=list, description="Required tags")

    # Actions
    routing_rules: List[StakeholderRoute] = Field(default_factory=list, description="Routing rules")
    message_template: Optional[str] = Field(None, description="Custom message template")
    escalation_enabled: bool = Field(True, description="Whether escalation is enabled")

    # Metadata
    created_by: str = Field(..., description="Rule creator")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation time")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update time")

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class NotificationTemplate(BaseModel):
    """Represents a notification message template."""

    template_id: str = Field(..., description="Unique template identifier")
    name: str = Field(..., description="Template name")
    trigger_type: TriggerType = Field(..., description="Trigger type")
    delivery_method: DeliveryMethod = Field(..., description="Delivery method")

    # Template content
    subject_template: Optional[str] = Field(None, description="Subject template")
    body_template: str = Field(..., description="Body template")
    adaptive_card_template: Optional[Dict[str, Any]] = Field(None, description="Adaptive card template")

    # Template variables
    variables: List[str] = Field(default_factory=list, description="Available template variables")

    # Metadata
    created_by: str = Field(..., description="Template creator")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation time")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update time")

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class NotificationMetrics(BaseModel):
    """Represents notification system metrics."""

    # Performance metrics
    total_notifications_sent: int = Field(0, description="Total notifications sent")
    avg_processing_time_ms: float = Field(0.0, description="Average processing time")
    p50_processing_time_ms: float = Field(0.0, description="P50 processing time")
    p95_processing_time_ms: float = Field(0.0, description="P95 processing time")

    # Delivery metrics
    successful_deliveries: int = Field(0, description="Successful deliveries")
    failed_deliveries: int = Field(0, description="Failed deliveries")
    rate_limited_notifications: int = Field(0, description="Rate limited notifications")
    deduplicated_notifications: int = Field(0, description="Deduplicated notifications")

    # Engagement metrics
    teams_card_clicks: int = Field(0, description="Teams card clicks")
    teams_actions_taken: int = Field(0, description="Teams actions taken")
    email_opens: int = Field(0, description="Email opens")
    email_clicks: int = Field(0, description="Email clicks")

    # Error metrics
    error_rate: float = Field(0.0, description="Error rate")
    timeout_rate: float = Field(0.0, description="Timeout rate")

    # Time period
    period_start: datetime = Field(..., description="Metrics period start")
    period_end: datetime = Field(..., description="Metrics period end")

    @validator('error_rate', 'timeout_rate')
    def validate_rates(cls, v):
        return max(0.0, min(1.0, v))

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
