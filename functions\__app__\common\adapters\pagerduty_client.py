"""
PagerDuty Client for Critical Escalations.
Handles incident creation and management for critical work items.
"""

import logging
import uuid
from typing import Dict, Any, Optional, List, TYPE_CHECKING
from datetime import datetime
import httpx

if TYPE_CHECKING:
    from ..models import (
        NotificationContext,
        NotificationTrigger,
        StakeholderRoute,
        EscalationLevel,
        TriggerType
    )
from ..models.schemas import WorkItem, Priority
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class PagerDutyClient:
    """
    PagerDuty client for creating and managing incidents for critical work items.
    
    Features:
    - Incident creation with proper severity mapping
    - Work item context in incident details
    - Automatic incident resolution
    - Integration with notification routing
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.api_base_url = "https://api.pagerduty.com"
        self.events_api_url = "https://events.pagerduty.com/v2/enqueue"
        
        # Setup HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "Content-Type": "application/json",
                "User-Agent": "QA-AI-Triage/1.0"
            },
            timeout=30.0
        )
    
    async def create_incident(
        self,
        context: "NotificationContext",
        route: "StakeholderRoute",
        service_key: str
    ) -> Optional[Dict[str, Any]]:
        """
        Create a PagerDuty incident for a critical work item.
        
        Args:
            context: Notification context
            route: Stakeholder route with PagerDuty service key
            service_key: PagerDuty integration key
        
        Returns:
            Incident response data or None if failed
        """
        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        try:
            work_item = context.work_item
            trigger = context.trigger
            
            # Build incident payload
            incident_payload = self._build_incident_payload(
                work_item, trigger, context, service_key
            )
            
            # Send to PagerDuty Events API
            response = await self.client.post(
                self.events_api_url,
                json=incident_payload
            )
            response.raise_for_status()
            
            incident_data = response.json()
            
            log_structured(
                logger,
                "info",
                "Created PagerDuty incident",
                extra={
                    "work_item_id": work_item.id,
                    "incident_key": incident_data.get("dedup_key"),
                    "pagerduty_response": incident_data,
                    "severity": self._map_priority_to_severity(work_item.priority)
                }
            )
            
            return incident_data
            
        except httpx.HTTPStatusError as e:
            log_structured(
                logger,
                "error",
                f"HTTP error creating PagerDuty incident: {e}",
                extra={
                    "work_item_id": context.work_item.id,
                    "status_code": e.response.status_code,
                    "response_text": e.response.text
                }
            )
            return None
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error creating PagerDuty incident: {e}",
                extra={"work_item_id": context.work_item.id},
                exc_info=True
            )
            return None
    
    async def resolve_incident(
        self,
        work_item_id: int,
        service_key: str,
        resolution_note: Optional[str] = None
    ) -> bool:
        """
        Resolve a PagerDuty incident for a work item.
        
        Args:
            work_item_id: Work item ID
            service_key: PagerDuty integration key
            resolution_note: Optional resolution note
        
        Returns:
            True if incident was resolved successfully
        """
        try:
            # Build resolution payload
            resolution_payload = {
                "routing_key": service_key,
                "event_action": "resolve",
                "dedup_key": f"workitem-{work_item_id}",
                "payload": {
                    "summary": f"Work item #{work_item_id} resolved",
                    "source": "QA-AI-Triage",
                    "severity": "info",
                    "custom_details": {
                        "resolution_note": resolution_note or "Work item resolved",
                        "resolved_at": datetime.utcnow().isoformat()
                    }
                }
            }
            
            # Send to PagerDuty Events API
            response = await self.client.post(
                self.events_api_url,
                json=resolution_payload
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Resolved PagerDuty incident",
                extra={
                    "work_item_id": work_item_id,
                    "resolution_note": resolution_note
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error resolving PagerDuty incident: {e}",
                extra={"work_item_id": work_item_id},
                exc_info=True
            )
            return False
    
    async def update_incident(
        self,
        work_item_id: int,
        service_key: str,
        update_summary: str,
        custom_details: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update a PagerDuty incident with new information.
        
        Args:
            work_item_id: Work item ID
            service_key: PagerDuty integration key
            update_summary: Update summary
            custom_details: Additional custom details
        
        Returns:
            True if incident was updated successfully
        """
        try:
            # Build update payload
            update_payload = {
                "routing_key": service_key,
                "event_action": "trigger",
                "dedup_key": f"workitem-{work_item_id}",
                "payload": {
                    "summary": update_summary,
                    "source": "QA-AI-Triage",
                    "severity": "warning",
                    "custom_details": custom_details or {}
                }
            }
            
            # Send to PagerDuty Events API
            response = await self.client.post(
                self.events_api_url,
                json=update_payload
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Updated PagerDuty incident",
                extra={
                    "work_item_id": work_item_id,
                    "update_summary": update_summary
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error updating PagerDuty incident: {e}",
                extra={"work_item_id": work_item_id},
                exc_info=True
            )
            return False
    
    def _build_incident_payload(
        self,
        work_item: WorkItem,
        trigger: "NotificationTrigger",
        context: "NotificationContext",
        service_key: str
    ) -> Dict[str, Any]:
        """Build PagerDuty incident payload."""
        
        # Generate deduplication key
        dedup_key = f"workitem-{work_item.id}"
        
        # Map priority to severity
        severity = self._map_priority_to_severity(work_item.priority)
        
        # Build summary
        summary = self._build_incident_summary(work_item, trigger)
        
        # Build custom details
        custom_details = self._build_custom_details(work_item, trigger, context)
        
        # Build incident payload
        payload = {
            "routing_key": service_key,
            "event_action": "trigger",
            "dedup_key": dedup_key,
            "payload": {
                "summary": summary,
                "source": "QA-AI-Triage",
                "severity": severity,
                "component": work_item.project,
                "group": "Work Items",
                "class": work_item.work_item_type,
                "custom_details": custom_details
            }
        }
        
        # Add links
        if work_item.id:
            ado_url = f"https://dev.azure.com/{work_item.project}/_workitems/edit/{work_item.id}"
            payload["payload"]["links"] = [
                {
                    "href": ado_url,
                    "text": f"View Work Item #{work_item.id}"
                }
            ]
        
        return payload
    
    def _build_incident_summary(self, work_item: WorkItem, trigger: "NotificationTrigger") -> str:
        """Build incident summary."""
        # Import here to avoid circular dependencies
        from ..models import TriggerType

        trigger_descriptions = {
            TriggerType.CRITICAL_CREATED: "Critical Work Item",
            TriggerType.SECURITY_ALERT: "Security Alert",
            TriggerType.ESCALATION: "Escalated Work Item",
            TriggerType.AGING: "Aging Work Item"
        }
        
        trigger_desc = trigger_descriptions.get(trigger.trigger_type, "Work Item Alert")
        return f"{trigger_desc}: #{work_item.id} - {work_item.title}"
    
    def _build_custom_details(
        self,
        work_item: WorkItem,
        trigger: "NotificationTrigger",
        context: "NotificationContext"
    ) -> Dict[str, Any]:
        """Build custom details for the incident."""
        details = {
            "work_item_id": work_item.id,
            "work_item_title": work_item.title,
            "work_item_type": work_item.work_item_type,
            "priority": f"P{work_item.priority}",
            "state": work_item.state,
            "project": work_item.project,
            "assigned_to": work_item.assigned_to or "Unassigned",
            "trigger_type": trigger.trigger_type,
            "escalation_level": trigger.escalation_level.name,
            "severity_score": trigger.severity_score,
            "created_at": datetime.utcnow().isoformat()
        }
        
        # Add age information if available
        if work_item.created_date:
            age = datetime.utcnow() - work_item.created_date
            details["age_days"] = age.days
            details["age_hours"] = age.total_seconds() / 3600
        
        # Add trigger conditions
        if trigger.trigger_conditions:
            details["trigger_conditions"] = trigger.trigger_conditions
        
        # Add assignment suggestions
        if context.suggested_assignees:
            details["suggested_assignees"] = [
                {
                    "name": suggestion.assignee_name,
                    "confidence": suggestion.confidence_score,
                    "reasoning": suggestion.reasoning
                }
                for suggestion in context.suggested_assignees[:3]
            ]
        
        # Add duplicate information
        if context.duplicate_candidates:
            details["potential_duplicates"] = [
                {
                    "id": duplicate.work_item_id,
                    "title": duplicate.title,
                    "similarity": duplicate.similarity_score
                }
                for duplicate in context.duplicate_candidates[:3]
            ]
        
        # Add priority analysis
        if context.priority_analysis:
            details["priority_analysis"] = {
                "suggested_priority": context.priority_analysis.suggested_priority,
                "confidence": context.priority_analysis.confidence_score,
                "customer_impact": context.priority_analysis.customer_impact,
                "security_impact": context.priority_analysis.security_impact,
                "reasoning": context.priority_analysis.reasoning
            }
        
        return details
    
    def _map_priority_to_severity(self, priority: int) -> str:
        """Map work item priority to PagerDuty severity."""
        severity_map = {
            1: "critical",  # P1 -> Critical
            2: "error",     # P2 -> Error
            3: "warning",   # P3 -> Warning
            4: "info"       # P4 -> Info
        }
        return severity_map.get(priority, "warning")
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
