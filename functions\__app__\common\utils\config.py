"""
Configuration management for environment variables and Key Vault secrets.
"""

import os
import logging
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from azure.keyvault.secrets import SecretClient
from azure.identity import DefaultAzureCredential
import json

logger = logging.getLogger(__name__)


@dataclass
class Config:
    """Configuration class that loads from environment variables and Key Vault."""
    
    # Azure DevOps settings
    ADO_ORGANIZATION: str = field(default="")
    ADO_PROJECT: str = field(default="")
    ADO_PAT_TOKEN: str = field(default="")
    
    # Azure Search settings
    AZURE_SEARCH_SERVICE_NAME: str = field(default="")
    AZURE_SEARCH_ADMIN_KEY: str = field(default="")
    SEARCH_INDEX_NAME: str = field(default="workitems")
    
    # Azure Key Vault settings
    KEY_VAULT_URL: str = field(default="")
    
    # OpenAI settings
    OPENAI_API_KEY: str = field(default="")
    OPENAI_EMBEDDING_MODEL: str = field(default="text-embedding-3-small")
    
    # Azure OpenAI settings
    AZURE_OPENAI_API_KEY: str = field(default="")
    AZURE_OPENAI_ENDPOINT: str = field(default="")
    AZURE_OPENAI_API_VERSION: str = field(default="2024-02-01")
    AZURE_OPENAI_EMBEDDING_MODEL: str = field(default="text-embedding-3-small")
    
    # Embedding settings
    EMBEDDING_PROVIDER: str = field(default="sentence_transformers")
    EMBEDDING_MODEL: str = field(default="all-MiniLM-L6-v2")
    EMBEDDING_MAX_TOKENS: int = field(default=8192)
    
    # Teams settings
    TEAMS_WEBHOOK_URL: str = field(default="")
    TEAMS_GRAPH_TOKEN: str = field(default="")
    TEAMS_LOGIC_APP_URL: str = field(default="")

    # Email settings
    Email_LOGIC_APP_URL: str = field(default="")

    # Vector storage settings
    VECTOR_STORAGE_TYPE: str = field(default="azure_search")  # "azure_search" or "sql"
    SQL_CONNECTION_STRING: str = field(default="")
    SQL_DATABASE_TYPE: str = field(default="azure_sql")  # "azure_sql" or "postgresql"
    
    # Duplicate detection settings
    DUPLICATE_SIMILARITY_THRESHOLD: float = field(default=0.85)
    DUPLICATE_TEXT_THRESHOLD: float = field(default=0.8)
    DUPLICATE_MAX_CANDIDATES: int = field(default=10)
    DUPLICATE_MAX_RESULTS: int = field(default=5)
    DUPLICATE_INCLUDE_CLOSED: bool = field(default=False)
    
    # Assignment settings
    ASSIGNMENT_KNN_K: int = field(default=10)
    ASSIGNMENT_MIN_CONFIDENCE: float = field(default=0.6)
    ASSIGNMENT_LOAD_WEIGHT: float = field(default=0.3)
    ASSIGNMENT_OWNERSHIP_WEIGHT: float = field(default=0.4)
    ASSIGNMENT_SIMILARITY_WEIGHT: float = field(default=0.3)
    
    # Priority settings
    PRIORITY_WEIGHT_SEVERITY: float = field(default=0.25)
    PRIORITY_WEIGHT_CUSTOMER: float = field(default=0.20)
    PRIORITY_WEIGHT_SECURITY: float = field(default=0.20)
    PRIORITY_WEIGHT_PERFORMANCE: float = field(default=0.15)
    PRIORITY_WEIGHT_BLOCKING: float = field(default=0.10)
    PRIORITY_WEIGHT_AREA: float = field(default=0.10)
    
    # Backfill settings
    BACKFILL_DAYS_BACK: int = field(default=30)
    BACKFILL_BATCH_SIZE: int = field(default=50)
    
    # Environment
    ENVIRONMENT: str = field(default="dev")
    
    # Application Insights
    APPINSIGHTS_INSTRUMENTATIONKEY: str = field(default="")
    APPLICATIONINSIGHTS_CONNECTION_STRING: str = field(default="")

    # Notification settings
    NOTIFICATIONS_ENABLED: bool = field(default=True)
    PAGERDUTY_ENABLED: bool = field(default=False)
    PAGERDUTY_API_KEY: str = field(default="")
    PAGERDUTY_DEFAULT_SERVICE_KEY: str = field(default="")
    NOTIFICATION_RATE_LIMIT_PER_USER_HOUR: int = field(default=5)
    NOTIFICATION_RATE_LIMIT_PER_CHANNEL_HOUR: int = field(default=20)
    NOTIFICATION_RATE_LIMIT_PER_PAGERDUTY_HOUR: int = field(default=10)
    NOTIFICATION_DEDUP_WINDOW_MINUTES: int = field(default=60)
    NOTIFICATION_QUIET_HOURS_START: str = field(default="22:00")
    NOTIFICATION_QUIET_HOURS_END: str = field(default="08:00")
    NOTIFICATION_QUIET_HOURS_TIMEZONE: str = field(default="UTC")
    NOTIFICATION_CRITICAL_BYPASS_QUIET_HOURS: bool = field(default=True)
    NOTIFICATION_AGING_CHECK_INTERVAL_HOURS: int = field(default=4)
    NOTIFICATION_AUDIT_RETENTION_DAYS: int = field(default=30)
    NOTIFICATION_MAX_PROCESSING_TIME_MS: int = field(default=5000)
    NOTIFICATION_DEFAULT_PROJECT_CONFIG: str = field(default="")

    # Internal cache for Key Vault secrets
    _kv_client: Optional[SecretClient] = field(default=None, init=False)
    _secret_cache: Dict[str, str] = field(default_factory=dict, init=False)
    
    def __post_init__(self):
        """Initialize configuration after object creation."""
        self._load_from_environment()
        self._initialize_key_vault()
        self._load_from_key_vault()
    
    def _load_from_environment(self):
        """Load configuration from environment variables."""
        for field_name in self.__dataclass_fields__:
            if field_name.startswith('_'):
                continue
            
            env_value = os.getenv(field_name)
            if env_value is not None:
                field_type = self.__dataclass_fields__[field_name].type
                
                # Convert to appropriate type
                if field_type == bool:
                    setattr(self, field_name, env_value.lower() in ('true', '1', 'yes', 'on'))
                elif field_type == int:
                    try:
                        setattr(self, field_name, int(env_value))
                    except ValueError:
                        logger.warning(f"Invalid integer value for {field_name}: {env_value}")
                elif field_type == float:
                    try:
                        setattr(self, field_name, float(env_value))
                    except ValueError:
                        logger.warning(f"Invalid float value for {field_name}: {env_value}")
                else:
                    setattr(self, field_name, env_value)
    
    def _initialize_key_vault(self):
        """Initialize Key Vault client if URL is provided."""
        if self.KEY_VAULT_URL:
            try:
                credential = DefaultAzureCredential()
                self._kv_client = SecretClient(
                    vault_url=self.KEY_VAULT_URL,
                    credential=credential
                )
                logger.info("Key Vault client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Key Vault client: {e}")
                self._kv_client = None
    
    def _load_from_key_vault(self):
        """Load sensitive configuration from Key Vault."""
        if not self._kv_client:
            return
        
        # Map of config fields to Key Vault secret names
        secret_mappings = {
            'ADO_PAT_TOKEN': 'ado-pat-token',
            'AZURE_SEARCH_ADMIN_KEY': 'search-admin-key',
            'OPENAI_API_KEY': 'openai-api-key',
            'AZURE_OPENAI_API_KEY': 'azure-openai-api-key',
            'TEAMS_WEBHOOK_URL': 'teams-webhook-url',
            'TEAMS_GRAPH_TOKEN': 'teams-graph-token'
        }
        
        for config_field, secret_name in secret_mappings.items():
            try:
                # Only load from Key Vault if not already set from environment
                if not getattr(self, config_field):
                    secret_value = self._get_secret(secret_name)
                    if secret_value:
                        setattr(self, config_field, secret_value)
                        logger.debug(f"Loaded {config_field} from Key Vault")
            except Exception as e:
                logger.warning(f"Failed to load secret {secret_name}: {e}")
    
    def _get_secret(self, secret_name: str) -> Optional[str]:
        """Get a secret from Key Vault with caching."""
        if secret_name in self._secret_cache:
            return self._secret_cache[secret_name]
        
        if not self._kv_client:
            return None
        
        try:
            secret = self._kv_client.get_secret(secret_name)
            self._secret_cache[secret_name] = secret.value
            return secret.value
        except Exception as e:
            logger.warning(f"Failed to retrieve secret {secret_name}: {e}")
            return None
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value with optional default."""
        return getattr(self, key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set a configuration value."""
        setattr(self, key, value)
    
    def to_dict(self, include_secrets: bool = False) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        result = {}
        secret_fields = {
            'ADO_PAT_TOKEN', 'AZURE_SEARCH_ADMIN_KEY', 'OPENAI_API_KEY',
            'AZURE_OPENAI_API_KEY', 'TEAMS_WEBHOOK_URL', 'TEAMS_GRAPH_TOKEN'
        }
        
        for field_name in self.__dataclass_fields__:
            if field_name.startswith('_'):
                continue
            
            value = getattr(self, field_name)
            
            if not include_secrets and field_name in secret_fields:
                result[field_name] = "***" if value else ""
            else:
                result[field_name] = value
        
        return result
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Required fields
        required_fields = [
            'ADO_ORGANIZATION',
            'ADO_PROJECT',
            'ADO_PAT_TOKEN',
            'AZURE_SEARCH_SERVICE_NAME',
            'AZURE_SEARCH_ADMIN_KEY'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                errors.append(f"Required field {field} is not set")
        
        # Validate embedding provider
        valid_providers = ['openai', 'azure_openai', 'sentence_transformers', 'e5_large']
        if self.EMBEDDING_PROVIDER not in valid_providers:
            errors.append(f"Invalid embedding provider: {self.EMBEDDING_PROVIDER}")
        
        # Validate provider-specific settings
        if self.EMBEDDING_PROVIDER == 'openai' and not self.OPENAI_API_KEY:
            errors.append("OPENAI_API_KEY required for OpenAI embedding provider")
        
        if self.EMBEDDING_PROVIDER == 'azure_openai':
            if not self.AZURE_OPENAI_API_KEY:
                errors.append("AZURE_OPENAI_API_KEY required for Azure OpenAI provider")
            if not self.AZURE_OPENAI_ENDPOINT:
                errors.append("AZURE_OPENAI_ENDPOINT required for Azure OpenAI provider")
        
        # Validate thresholds
        if not 0.0 <= self.DUPLICATE_SIMILARITY_THRESHOLD <= 1.0:
            errors.append("DUPLICATE_SIMILARITY_THRESHOLD must be between 0.0 and 1.0")
        
        if not 0.0 <= self.ASSIGNMENT_MIN_CONFIDENCE <= 1.0:
            errors.append("ASSIGNMENT_MIN_CONFIDENCE must be between 0.0 and 1.0")
        
        return errors


# Global configuration instance
_config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global _config
    if _config is None:
        _config = Config()
    return _config


def reload_config() -> Config:
    """Reload configuration from environment and Key Vault."""
    global _config
    _config = Config()
    return _config


def validate_config() -> List[str]:
    """Validate the current configuration."""
    config = get_config()
    return config.validate()
