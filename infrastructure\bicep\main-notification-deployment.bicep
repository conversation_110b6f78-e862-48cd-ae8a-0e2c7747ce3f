@description('Main deployment template for Intelligent Notifications & Reminders service')

// Parameters
@description('Environment name (dev, staging, prod)')
@allowed(['dev', 'staging', 'prod'])
param environment string = 'dev'

@description('Location for all resources')
param location string = resourceGroup().location

@description('Application name prefix')
param appName string = 'qa-ai-triage'

@description('Enable PagerDuty integration')
param enablePagerDuty bool = false

@description('Enable monitoring and alerting')
param enableMonitoring bool = true

@description('PagerDuty API key (will be stored in Key Vault)')
@secure()
param pagerDutyApiKey string = ''

@description('Teams webhook URL (will be stored in Key Vault)')
@secure()
param teamsWebhookUrl string = ''

@description('Azure DevOps organization URL')
param adoOrganization string = ''

@description('Azure DevOps project name')
param adoProject string = ''

@description('Azure DevOps PAT (will be stored in Key Vault)')
@secure()
param adoPat string = ''

@description('OpenAI API key (will be stored in Key Vault)')
@secure()
param openAiApiKey string = ''

@description('Email addresses for alert notifications')
param alertEmailAddresses array = []

@description('Teams webhook URL for alert notifications')
param alertTeamsWebhookUrl string = ''

@description('Tags to apply to all resources')
param tags object = {
  Environment: environment
  Application: 'QA-AI-Triage'
  Component: 'Notifications'
  ManagedBy: 'Bicep'
  DeployedAt: utcNow()
}

// Deploy notification service infrastructure
module notificationService 'notification-service.bicep' = {
  name: 'notification-service-deployment'
  params: {
    environment: environment
    location: location
    appName: appName
    enablePagerDuty: enablePagerDuty
    pagerDutyApiKey: pagerDutyApiKey
    teamsWebhookUrl: teamsWebhookUrl
    adoOrganization: adoOrganization
    adoProject: adoProject
    adoPat: adoPat
    openAiApiKey: openAiApiKey
    tags: tags
  }
}

// Deploy monitoring and alerting (if enabled)
module notificationMonitoring 'notification-monitoring.bicep' = if (enableMonitoring) {
  name: 'notification-monitoring-deployment'
  params: {
    environment: environment
    location: location
    appName: appName
    functionAppResourceId: notificationService.outputs.functionAppName
    appInsightsResourceId: notificationService.outputs.appInsightsConnectionString
    logAnalyticsWorkspaceId: notificationService.outputs.logAnalyticsWorkspaceId
    alertEmailAddresses: alertEmailAddresses
    alertTeamsWebhookUrl: alertTeamsWebhookUrl
    tags: tags
  }
  dependsOn: [
    notificationService
  ]
}

// Outputs
output deploymentSummary object = {
  functionAppName: notificationService.outputs.functionAppName
  functionAppUrl: notificationService.outputs.functionAppUrl
  functionAppPrincipalId: notificationService.outputs.functionAppPrincipalId
  appInsightsConnectionString: notificationService.outputs.appInsightsConnectionString
  logAnalyticsWorkspaceId: notificationService.outputs.logAnalyticsWorkspaceId
  monitoringEnabled: enableMonitoring
  pagerDutyEnabled: enablePagerDuty
  alertingConfigured: enableMonitoring && length(alertEmailAddresses) > 0
}

output endpoints object = {
  criticalNotification: '${notificationService.outputs.functionAppUrl}/api/critical_notification'
  notificationWebhook: '${notificationService.outputs.functionAppUrl}/api/notification_webhook'
  healthCheck: '${notificationService.outputs.functionAppUrl}/api/health'
}

output monitoringResources object = enableMonitoring ? {
  actionGroupId: notificationMonitoring.outputs.actionGroupId
  workbookId: notificationMonitoring.outputs.workbookId
  alertIds: notificationMonitoring.outputs.alertIds
} : {}

output configurationNotes array = [
  'Function App deployed successfully'
  enablePagerDuty ? 'PagerDuty integration enabled' : 'PagerDuty integration disabled'
  enableMonitoring ? 'Monitoring and alerting configured' : 'Monitoring disabled'
  !empty(teamsWebhookUrl) ? 'Teams webhook configured' : 'Teams webhook not configured'
  length(alertEmailAddresses) > 0 ? 'Email alerts configured' : 'No email alerts configured'
  'Configure ADO webhook to point to the critical_notification endpoint'
  'Set up aging reminder timer trigger schedule as needed'
  'Review and adjust notification rate limits based on usage patterns'
]
