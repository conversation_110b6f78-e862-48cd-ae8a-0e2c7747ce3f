#!/usr/bin/env python3
"""
Execute AutoDefectTriage workflow with real Azure DevOps work items.
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Optional

# Add the functions directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.workflow_orchestrator import WorkflowOrchestrator, WorkflowContext
from __app__.common.models.schemas import WorkItem
from __app__.common.utils.logging import setup_logging


def load_local_settings():
    """Load environment variables from local.settings.json."""
    settings_file = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)
            
            # Set environment variables from the Values section
            if "Values" in settings:
                for key, value in settings["Values"].items():
                    os.environ[key] = str(value)
            
            print("✅ Environment variables loaded from local.settings.json")
            return True
        except Exception as e:
            print(f"⚠️  Warning: Could not load local.settings.json: {e}")
            return False
    else:
        print("⚠️  Warning: local.settings.json not found")
        return False


async def fetch_recent_work_items(orchestrator: WorkflowOrchestrator, limit: int = 10) -> list:
    """Fetch recent work items from Azure DevOps."""
    try:
        print(f"🔍 Fetching recent work items from Azure DevOps...")

        # Use the ADO client to get recent work items
        ado_client = orchestrator.clients['ado']

        # Simple WIQL query for recent work items
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State],
               [System.AssignedTo], [System.CreatedDate], [System.Priority]
        FROM WorkItems
        WHERE [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Issue')
        ORDER BY [System.CreatedDate] DESC
        """

        # Get work items using WIQL query
        work_items = await ado_client.query_work_items(wiql_query)

        # Limit results
        if work_items and len(work_items) > limit:
            work_items = work_items[:limit]

        if work_items:
            print(f"✅ Found {len(work_items)} work items")
            for item in work_items[:5]:  # Show first 5
                fields = item.get('fields', {})
                title = fields.get('System.Title', 'No title')
                print(f"   📋 {item.get('id', 'N/A')}: {title[:60]}...")
        else:
            print("⚠️  No work items found")

        return work_items or []

    except Exception as e:
        print(f"❌ Error fetching work items: {e}")
        import traceback
        traceback.print_exc()
        return []


async def execute_with_real_work_item(work_item_id: int):
    """Execute the workflow with a real work item ID."""

    print(f"\n🚀 EXECUTING WITH WORK ITEM: {work_item_id}")
    print("=" * 80)

    # Safety check for testing
    if work_item_id != 748404:
        print("⚠️  WARNING: For testing, please use only work item 748404")
        print("   All other work items contain production data and should not be modified during testing.")
        print("   Use work item 748404 for safe testing of update functionality.")

        confirm = input("Are you sure you want to proceed with this work item? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("❌ Execution cancelled for safety")
            return
    else:
        print("✅ Using designated test work item 748404 - safe for testing")

    try:
        # Initialize workflow orchestrator
        print("🔧 Initializing Azure Function components...")
        orchestrator = WorkflowOrchestrator()
        await orchestrator.initialize()  # Initialize clients

        print(f"📋 Executing workflow for work item: {work_item_id}")

        # Execute the complete workflow (orchestrator handles context creation)
        result_context = await orchestrator.execute_workflow(str(work_item_id))
        
        # Display results
        print(f"\n✅ WORKFLOW COMPLETED SUCCESSFULLY!")
        print(f"   📋 Work Item: {result_context.work_item.id if result_context.work_item else 'N/A'}")
        print(f"   📝 Title: {result_context.work_item.title if result_context.work_item else 'N/A'}")
        print(f"   👤 Assigned: {result_context.triage_result.assigned_to if result_context.triage_result else 'N/A'}")
        print(f"   📊 Priority: P{result_context.triage_result.priority if result_context.triage_result else 'N/A'}")
        print(f"   🎯 Confidence: {int(result_context.triage_result.confidence_score * 100) if result_context.triage_result else 'N/A'}%")
        print(f"   ⏱️  Total time: {sum(result_context.step_timings.values()):.2f} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ Error executing workflow: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main execution function."""
    
    print("🚀 AUTODEFECTTRIAGE - REAL DATA EXECUTION")
    print("=" * 80)
    print(f"🕐 Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # Load environment variables
    if not load_local_settings():
        print("❌ Failed to load configuration. Please check local.settings.json")
        return
    
    # Setup logging
    setup_logging()
    
    try:
        # Initialize orchestrator to fetch work items
        print("\n🔧 Initializing components...")
        orchestrator = WorkflowOrchestrator()
        await orchestrator.initialize()  # Initialize clients
        
        # Check if user provided a specific work item ID
        if len(sys.argv) > 1:
            try:
                work_item_id = int(sys.argv[1])
                print(f"📋 Using provided work item ID: {work_item_id}")
                await execute_with_real_work_item(work_item_id)
                return
            except ValueError:
                print(f"❌ Invalid work item ID: {sys.argv[1]}")
                return
        
        # Fetch recent work items
        work_items = await fetch_recent_work_items(orchestrator)
        
        if not work_items:
            print("❌ No work items found. Please provide a specific work item ID:")
            print("   python scripts/execute_with_real_data.py <work_item_id>")
            return
        
        # Show available work items
        print(f"\n📋 AVAILABLE WORK ITEMS:")
        print("-" * 40)
        for i, item in enumerate(work_items[:10], 1):
            fields = item.get('fields', {})
            title = fields.get('System.Title', 'No title')
            work_type = fields.get('System.WorkItemType', 'Unknown')
            state = fields.get('System.State', 'Unknown')
            created = fields.get('System.CreatedDate', '')[:10]  # Just date part
            
            print(f"{i:2d}. ID: {item.get('id', 'N/A'):>6} | {work_type:>10} | {state:>8} | {created} | {title[:40]}...")
        
        # Ask user to select a work item
        print(f"\n🎯 Select a work item to process (1-{min(len(work_items), 10)}) or enter a specific ID:")
        try:
            user_input = input("Enter choice: ").strip()
            
            if user_input.isdigit():
                choice = int(user_input)
                if 1 <= choice <= min(len(work_items), 10):
                    # User selected from the list
                    selected_item = work_items[choice - 1]
                    work_item_id = selected_item.get('id')
                else:
                    # User entered a specific work item ID
                    work_item_id = choice
            else:
                print("❌ Invalid input")
                return
            
            # Execute with selected work item
            await execute_with_real_work_item(work_item_id)
            
        except KeyboardInterrupt:
            print("\n👋 Execution cancelled by user")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
