# Teams Notification Setup Guide

## Overview

To receive Teams notifications from the AutoDefectTriage system, you need to configure Teams webhooks or Logic Apps. Here's how to set it up step by step.

## Option 1: Teams Incoming Webhook (Recommended)

### Step 1: Create Teams Incoming Webhook

1. **Open Microsoft Teams**
2. **Navigate to your channel** where you want notifications
3. **Click the three dots (...)** next to the channel name
4. **Select "Connectors"**
5. **Find "Incoming Webhook"** and click "Configure"
6. **Provide a name** (e.g., "AutoDefectTriage Notifications")
7. **Upload an icon** (optional)
8. **Click "Create"**
9. **Copy the webhook URL** - it looks like:
   ```
   https://outlook.office.com/webhook/YOUR-WEBHOOK-ID/IncomingWebhook/YOUR-CHANNEL-ID/YOUR-TOKEN
   ```

### Step 2: Configure Environment Variables

Add these to your `local.settings.json` or Azure Function App settings:

```json
{
  "Values": {
    "TEAMS_WEBHOOK_URL": "https://outlook.office.com/webhook/YOUR-WEBHOOK-ID/...",
    "TEAMS_NOTIFICATIONS_ENABLED": "true",
    "TEAMS_NOTIFICATION_CHANNEL": "QA Team - Critical Issues"
  }
}
```

### Step 3: Test the Webhook

You can test the webhook with this PowerShell command:

```powershell
$webhookUrl = "YOUR_WEBHOOK_URL"
$body = @{
    text = "🧪 Test notification from AutoDefectTriage system"
} | ConvertTo-Json

Invoke-RestMethod -Uri $webhookUrl -Method POST -Body $body -ContentType "application/json"
```

## Option 2: Teams Logic App

### Step 1: Create Logic App

1. **Go to Azure Portal**
2. **Create new Logic App**
3. **Choose "Post a message (V3)" Teams connector**
4. **Configure authentication**
5. **Set up the trigger** (HTTP request)
6. **Configure the Teams action**

### Step 2: Configure Environment Variables

```json
{
  "Values": {
    "TEAMS_LOGIC_APP_URL": "https://your-logic-app.azurewebsites.net/api/teams-notification",
    "TEAMS_NOTIFICATIONS_ENABLED": "true"
  }
}
```

## Option 3: Microsoft Graph API

### Step 1: Register App in Azure AD

1. **Go to Azure Portal > Azure Active Directory**
2. **App registrations > New registration**
3. **Configure permissions:**
   - `ChannelMessage.Send`
   - `Team.ReadBasic.All`
   - `Channel.ReadBasic.All`

### Step 2: Configure Environment Variables

```json
{
  "Values": {
    "TEAMS_APP_ID": "your-app-id",
    "TEAMS_APP_SECRET": "your-app-secret",
    "TEAMS_TENANT_ID": "your-tenant-id",
    "TEAMS_TEAM_ID": "your-team-id",
    "TEAMS_CHANNEL_ID": "your-channel-id",
    "TEAMS_NOTIFICATIONS_ENABLED": "true"
  }
}
```

## Quick Setup for Testing

For immediate testing, use the webhook approach:

### 1. Get Teams Webhook URL

```bash
# In Teams:
# 1. Go to your channel
# 2. Click "..." > Connectors
# 3. Add "Incoming Webhook"
# 4. Copy the URL
```

### 2. Update Configuration

Create or update `functions/local.settings.json`:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "python",
    "TEAMS_WEBHOOK_URL": "YOUR_WEBHOOK_URL_HERE",
    "TEAMS_NOTIFICATIONS_ENABLED": "true",
    "TEAMS_NOTIFICATION_CHANNEL": "AutoDefectTriage Alerts"
  }
}
```

### 3. Test Teams Notification

Run this test script:

```python
import asyncio
import aiohttp
import json

async def test_teams_notification():
    webhook_url = "YOUR_WEBHOOK_URL"
    
    message = {
        "@type": "MessageCard",
        "@context": "http://schema.org/extensions",
        "themeColor": "0076D7",
        "summary": "AutoDefectTriage Test",
        "sections": [{
            "activityTitle": "🧪 Test Notification",
            "activitySubtitle": "AutoDefectTriage System",
            "activityImage": "https://teamsnodesample.azurewebsites.net/static/img/image5.png",
            "facts": [{
                "name": "Work Item ID:",
                "value": "TEST-12345"
            }, {
                "name": "Priority:",
                "value": "1 (Critical)"
            }, {
                "name": "Assigned To:",
                "value": "auth-team"
            }],
            "markdown": True
        }],
        "potentialAction": [{
            "@type": "OpenUri",
            "name": "View Work Item",
            "targets": [{
                "os": "default",
                "uri": "https://dev.azure.com/your-org/your-project/_workitems/edit/12345"
            }]
        }]
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(webhook_url, json=message) as response:
            if response.status == 200:
                print("✅ Teams notification sent successfully!")
            else:
                print(f"❌ Failed to send notification: {response.status}")

# Run the test
asyncio.run(test_teams_notification())
```

## Troubleshooting

### Common Issues

1. **"No notification channels configured"**
   - Check that `TEAMS_WEBHOOK_URL` or `TEAMS_LOGIC_APP_URL` is set
   - Verify `TEAMS_NOTIFICATIONS_ENABLED=true`

2. **Webhook returns 400 Bad Request**
   - Check the webhook URL is correct
   - Verify the JSON payload format
   - Ensure the webhook is still active in Teams

3. **Authentication errors**
   - For Graph API: Check app permissions and consent
   - For Logic Apps: Verify connection authentication

4. **Messages not appearing**
   - Check if the channel still exists
   - Verify webhook hasn't been deleted
   - Check Teams notification settings

### Debug Steps

1. **Check configuration:**
   ```python
   from functions.__app__.common.utils.config import get_config
   config = get_config()
   print(f"Teams enabled: {getattr(config, 'TEAMS_NOTIFICATIONS_ENABLED', False)}")
   print(f"Webhook URL: {getattr(config, 'TEAMS_WEBHOOK_URL', 'Not set')}")
   ```

2. **Test webhook directly:**
   ```bash
   curl -X POST "YOUR_WEBHOOK_URL" \
     -H "Content-Type: application/json" \
     -d '{"text": "Test from AutoDefectTriage"}'
   ```

3. **Check function logs:**
   ```bash
   # Look for Teams-related log messages
   grep -i "teams" function-logs.txt
   ```

## Next Steps

Once Teams notifications are configured:

1. **Run the workflow again:**
   ```bash
   python scripts/execute_function_step_by_step.py --local-test
   ```

2. **Check Teams channel** for the notification

3. **Verify adaptive card formatting** and interactive elements

4. **Test with real work items** from Azure DevOps

The Teams notifications will include:
- 🚨 Work item details
- 🤖 AI triage results  
- 👤 Assignment recommendations
- 🔘 Interactive action buttons
- 📊 Priority and confidence scores
