"""
Smart Trigger Engine for Intelligent Notifications & Reminders.
Evaluates when notifications should be sent based on work item conditions.
"""

import logging
import re
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

# Import types for type hints only
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..models import (
        NotificationTrigger,
        TriggerType,
        EscalationLevel,
        ProjectNotificationConfig
    )
from ..models.schemas import WorkItem, Priority
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


@dataclass
class TriggerCondition:
    """Represents a trigger condition evaluation result."""
    triggered: bool
    trigger_type: "TriggerType"
    severity_score: float
    escalation_level: "EscalationLevel"
    conditions: Dict[str, Any]
    reasoning: str


class TriggerEngine:
    """
    Smart trigger engine that evaluates when notifications should be sent.
    
    Supports:
    - Critical work item detection on creation/update
    - Aging work item analysis on schedule
    - Security vulnerability detection
    - Customer impact assessment
    - Escalation level determination
    """
    
    def __init__(self, config: Config):
        self.config = config
        self._project_configs: Dict[str, "ProjectNotificationConfig"] = {}
        
    def load_project_config(self, project: str, config: "ProjectNotificationConfig") -> None:
        """Load project-specific notification configuration."""
        self._project_configs[project] = config
        log_structured(
            logger,
            "info",
            f"Loaded notification config for project: {project}",
            extra={"project": project, "enabled": config.enabled}
        )
    
    def evaluate_work_item(
        self,
        work_item: WorkItem,
        event_type: str = "created"
    ) -> List["NotificationTrigger"]:
        """
        Evaluate a work item for notification triggers.
        
        Args:
            work_item: Work item to evaluate
            event_type: Type of event (created, updated, etc.)
        
        Returns:
            List of notification triggers
        """
        triggers = []
        
        try:
            project_config = self._get_project_config(work_item.project)
            if not project_config.enabled:
                return triggers
            
            # Evaluate different trigger conditions
            conditions = [
                self._evaluate_critical_creation(work_item, project_config),
                self._evaluate_security_alert(work_item, project_config),
                self._evaluate_customer_impact(work_item, project_config),
                self._evaluate_aging(work_item, project_config),
                self._evaluate_escalation(work_item, project_config)
            ]
            
            # Create triggers for triggered conditions
            # Import here to avoid circular dependencies
            from ..models import NotificationTrigger

            for condition in conditions:
                if condition.triggered:
                    trigger = NotificationTrigger(
                        trigger_type=condition.trigger_type,
                        work_item_id=work_item.id,
                        project=work_item.project,
                        trigger_conditions=condition.conditions,
                        severity_score=condition.severity_score,
                        escalation_level=condition.escalation_level,
                        created_at=datetime.utcnow()
                    )
                    triggers.append(trigger)
                    
                    log_structured(
                        logger,
                        "info",
                        f"Trigger activated: {condition.trigger_type}",
                        extra={
                            "work_item_id": work_item.id,
                            "trigger_type": condition.trigger_type,
                            "severity_score": condition.severity_score,
                            "reasoning": condition.reasoning
                        }
                    )
            
            return triggers
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error evaluating work item triggers: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            return []
    
    def evaluate_aging_batch(
        self,
        work_items: List[WorkItem]
    ) -> List["NotificationTrigger"]:
        """
        Evaluate a batch of work items for aging triggers.

        Args:
            work_items: List of work items to evaluate

        Returns:
            List of aging notification triggers
        """
        # Import here to avoid circular dependencies
        from ..models import TriggerType

        triggers = []

        for work_item in work_items:
            aging_triggers = [
                trigger for trigger in self.evaluate_work_item(work_item, "aging")
                if trigger.trigger_type == TriggerType.AGING
            ]
            triggers.extend(aging_triggers)
        
        log_structured(
            logger,
            "info",
            f"Aging evaluation completed",
            extra={
                "work_items_evaluated": len(work_items),
                "aging_triggers_found": len(triggers)
            }
        )
        
        return triggers
    
    def _evaluate_critical_creation(
        self,
        work_item: WorkItem,
        config: "ProjectNotificationConfig"
    ) -> TriggerCondition:
        """Evaluate if work item creation should trigger critical notification."""

        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        # Check if priority is in critical list
        is_critical_priority = work_item.priority in config.critical_priorities
        
        # Calculate severity score based on multiple factors
        severity_score = 0.0
        conditions = {}
        reasoning_parts = []
        
        if is_critical_priority:
            severity_score += 5.0
            conditions["critical_priority"] = True
            reasoning_parts.append(f"Priority {work_item.priority} is critical")
        
        # Check for blocking keywords
        blocking_keywords = ["blocking", "blocker", "blocks", "critical path"]
        has_blocking = self._check_keywords(work_item, blocking_keywords)
        if has_blocking:
            severity_score += 2.0
            conditions["has_blocking_keywords"] = True
            reasoning_parts.append("Contains blocking keywords")
        
        # Check for production keywords
        production_keywords = ["production", "prod", "live", "outage"]
        has_production = self._check_keywords(work_item, production_keywords)
        if has_production:
            severity_score += 3.0
            conditions["has_production_keywords"] = True
            reasoning_parts.append("Affects production environment")
        
        # Determine escalation level
        escalation_level = EscalationLevel.NORMAL
        if severity_score >= 8.0:
            escalation_level = EscalationLevel.CRITICAL
        elif severity_score >= 6.0:
            escalation_level = EscalationLevel.URGENT
        elif severity_score >= 4.0:
            escalation_level = EscalationLevel.ELEVATED
        
        triggered = severity_score >= 4.0  # Threshold for critical notifications
        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "No critical conditions met"
        
        return TriggerCondition(
            triggered=triggered,
            trigger_type=TriggerType.CRITICAL_CREATED,
            severity_score=severity_score,
            escalation_level=escalation_level,
            conditions=conditions,
            reasoning=reasoning
        )
    
    def _evaluate_security_alert(
        self,
        work_item: WorkItem,
        config: "ProjectNotificationConfig"
    ) -> TriggerCondition:
        """Evaluate if work item should trigger security alert."""

        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        has_security_keywords = self._check_keywords(work_item, config.security_keywords)
        
        severity_score = 0.0
        conditions = {}
        reasoning_parts = []
        
        if has_security_keywords:
            severity_score += 7.0  # Security is always high priority
            conditions["has_security_keywords"] = True
            reasoning_parts.append("Contains security-related keywords")
        
        # Check for CVE references
        cve_pattern = r'CVE-\d{4}-\d{4,}'
        has_cve = bool(re.search(cve_pattern, work_item.title + " " + (work_item.description or "")))
        if has_cve:
            severity_score += 3.0
            conditions["has_cve_reference"] = True
            reasoning_parts.append("Contains CVE reference")
        
        triggered = severity_score >= 7.0
        escalation_level = EscalationLevel.URGENT if triggered else EscalationLevel.NORMAL
        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "No security conditions met"
        
        return TriggerCondition(
            triggered=triggered,
            trigger_type=TriggerType.SECURITY_ALERT,
            severity_score=severity_score,
            escalation_level=escalation_level,
            conditions=conditions,
            reasoning=reasoning
        )
    
    def _evaluate_customer_impact(
        self,
        work_item: WorkItem,
        config: "ProjectNotificationConfig"
    ) -> TriggerCondition:
        """Evaluate if work item has customer impact."""

        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        has_customer_keywords = self._check_keywords(work_item, config.customer_impact_keywords)
        
        severity_score = 0.0
        conditions = {}
        reasoning_parts = []
        
        if has_customer_keywords:
            severity_score += 4.0
            conditions["has_customer_keywords"] = True
            reasoning_parts.append("Has customer impact keywords")
        
        # Check for high priority with customer impact
        if work_item.priority <= 2 and has_customer_keywords:
            severity_score += 2.0
            conditions["high_priority_customer_impact"] = True
            reasoning_parts.append("High priority with customer impact")
        
        triggered = severity_score >= 4.0
        escalation_level = EscalationLevel.ELEVATED if triggered else EscalationLevel.NORMAL
        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "No customer impact detected"
        
        return TriggerCondition(
            triggered=triggered,
            trigger_type=TriggerType.CRITICAL_CREATED,  # Use critical for customer impact
            severity_score=severity_score,
            escalation_level=escalation_level,
            conditions=conditions,
            reasoning=reasoning
        )
    
    def _evaluate_aging(
        self,
        work_item: WorkItem,
        config: "ProjectNotificationConfig"
    ) -> TriggerCondition:
        """Evaluate if work item has aged beyond thresholds."""

        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        if not work_item.created_date:
            return TriggerCondition(
                triggered=False,
                trigger_type=TriggerType.AGING,
                severity_score=0.0,
                escalation_level=EscalationLevel.NORMAL,
                conditions={},
                reasoning="No creation date available"
            )
        
        age = datetime.utcnow() - work_item.created_date
        priority_key = f"P{work_item.priority}"
        threshold_str = config.aging_thresholds.get(priority_key, "168h")  # Default 1 week
        
        threshold = self._parse_duration(threshold_str)
        aged_beyond_threshold = age > threshold
        
        severity_score = 0.0
        conditions = {}
        reasoning_parts = []
        
        if aged_beyond_threshold:
            # Calculate severity based on how much it's overdue
            overdue_ratio = age.total_seconds() / threshold.total_seconds()
            severity_score = min(5.0, 2.0 + (overdue_ratio - 1.0) * 2.0)
            
            conditions["aged_beyond_threshold"] = True
            conditions["age_hours"] = age.total_seconds() / 3600
            conditions["threshold_hours"] = threshold.total_seconds() / 3600
            conditions["overdue_ratio"] = overdue_ratio
            
            reasoning_parts.append(f"Aged {age.days} days, threshold {threshold.days} days")
        
        triggered = aged_beyond_threshold
        escalation_level = EscalationLevel.NORMAL
        if severity_score >= 4.0:
            escalation_level = EscalationLevel.ELEVATED
        
        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "Within aging threshold"
        
        return TriggerCondition(
            triggered=triggered,
            trigger_type=TriggerType.AGING,
            severity_score=severity_score,
            escalation_level=escalation_level,
            conditions=conditions,
            reasoning=reasoning
        )
    
    def _evaluate_escalation(
        self,
        work_item: WorkItem,
        config: "ProjectNotificationConfig"
    ) -> TriggerCondition:
        """Evaluate if work item needs escalation."""

        # Import here to avoid circular dependencies
        from ..models import TriggerType, EscalationLevel

        # This would typically check for previous notifications and escalation history
        # For now, we'll implement basic escalation logic

        severity_score = 0.0
        conditions = {}
        reasoning_parts = []
        
        # Check if high priority item is unassigned for too long
        if work_item.priority <= 2 and not work_item.assigned_to:
            if work_item.created_date:
                age = datetime.utcnow() - work_item.created_date
                if age > timedelta(hours=4):  # 4 hours unassigned
                    severity_score += 3.0
                    conditions["unassigned_too_long"] = True
                    reasoning_parts.append("High priority item unassigned for > 4 hours")
        
        triggered = severity_score >= 3.0
        escalation_level = EscalationLevel.ELEVATED if triggered else EscalationLevel.NORMAL
        reasoning = "; ".join(reasoning_parts) if reasoning_parts else "No escalation needed"
        
        return TriggerCondition(
            triggered=triggered,
            trigger_type=TriggerType.ESCALATION,
            severity_score=severity_score,
            escalation_level=escalation_level,
            conditions=conditions,
            reasoning=reasoning
        )
    
    def _check_keywords(self, work_item: WorkItem, keywords: List[str]) -> bool:
        """Check if work item contains any of the specified keywords."""
        text = (work_item.title + " " + (work_item.description or "")).lower()
        return any(keyword.lower() in text for keyword in keywords)
    
    def _parse_duration(self, duration_str: str) -> timedelta:
        """Parse duration string like '4h', '24h', '72h' into timedelta."""
        duration_str = duration_str.strip().lower()
        
        if duration_str.endswith('h'):
            hours = int(duration_str[:-1])
            return timedelta(hours=hours)
        elif duration_str.endswith('d'):
            days = int(duration_str[:-1])
            return timedelta(days=days)
        elif duration_str.endswith('m'):
            minutes = int(duration_str[:-1])
            return timedelta(minutes=minutes)
        else:
            # Default to hours
            return timedelta(hours=int(duration_str))
    
    def _get_project_config(self, project: str) -> "ProjectNotificationConfig":
        """Get project configuration or return default."""
        if project in self._project_configs:
            return self._project_configs[project]

        # Return default configuration
        # Import here to avoid circular dependencies
        from ..models import ProjectNotificationConfig
        return ProjectNotificationConfig(project_name=project)
