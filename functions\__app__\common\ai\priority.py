"""
Priority Engine
Determines work item priority using rules and ML models.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
from dataclasses import dataclass
import re

from ..models.schemas import WorkItem
from ..utils.config import Config
from ..utils.text import extract_keywords, count_severity_indicators
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class PriorityLevel(Enum):
    """Priority levels for work items."""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


@dataclass
class PriorityFactors:
    """Factors that influence priority calculation."""
    severity_keywords: int
    customer_impact: bool
    security_related: bool
    performance_impact: bool
    blocking_issue: bool
    area_criticality: float
    user_count_affected: int
    business_impact_score: float


class PriorityEngine:
    """Engine for calculating work item priority."""
    
    def __init__(self, config: Config):
        self.config = config
        
        # Priority calculation weights
        self.weights = {
            'severity_keywords': config.get('PRIORITY_WEIGHT_SEVERITY', 0.25),
            'customer_impact': config.get('PRIORITY_WEIGHT_CUSTOMER', 0.20),
            'security': config.get('PRIORITY_WEIGHT_SECURITY', 0.20),
            'performance': config.get('PRIORITY_WEIGHT_PERFORMANCE', 0.15),
            'blocking': config.get('PRIORITY_WEIGHT_BLOCKING', 0.10),
            'area_criticality': config.get('PRIORITY_WEIGHT_AREA', 0.10)
        }
        
        # Keyword patterns for different priority factors
        self.severity_keywords = {
            'critical': ['crash', 'hang', 'freeze', 'data loss', 'corruption', 'fatal', 'critical'],
            'high': ['error', 'exception', 'fail', 'broken', 'not working', 'regression'],
            'medium': ['slow', 'performance', 'improvement', 'enhancement'],
            'low': ['cosmetic', 'minor', 'suggestion', 'nice to have']
        }
        
        self.security_keywords = [
            'security', 'vulnerability', 'exploit', 'injection', 'xss', 'csrf',
            'authentication', 'authorization', 'privilege', 'escalation',
            'password', 'token', 'encryption', 'ssl', 'tls'
        ]
        
        self.performance_keywords = [
            'slow', 'performance', 'timeout', 'latency', 'memory leak',
            'cpu', 'memory', 'disk', 'network', 'bottleneck', 'optimization'
        ]
        
        self.blocking_keywords = [
            'blocking', 'blocker', 'blocks', 'prevents', 'cannot',
            'unable to', 'stops', 'halts', 'breaks build'
        ]
        
        self.customer_keywords = [
            'customer', 'client', 'user', 'production', 'live', 'external',
            'public', 'revenue', 'business critical', 'sla', 'escalation'
        ]
        
        # Area criticality mapping
        self.area_criticality = {
            'authentication': 0.9,
            'security': 0.9,
            'payment': 0.9,
            'core': 0.8,
            'api': 0.8,
            'database': 0.8,
            'ui': 0.6,
            'reporting': 0.5,
            'documentation': 0.3,
            'test': 0.2
        }
    
    async def calculate_priority(self, work_item: WorkItem) -> int:
        """
        Calculate priority for a work item.
        
        Args:
            work_item: The work item to prioritize
        
        Returns:
            Priority level (1=Critical, 2=High, 3=Medium, 4=Low)
        """
        try:
            log_structured(
                logger,
                "info",
                "Calculating work item priority",
                extra={
                    "work_item_id": work_item.id,
                    "work_item_type": work_item.work_item_type,
                    "current_priority": work_item.priority
                }
            )
            
            # Extract priority factors
            factors = self._extract_priority_factors(work_item)
            
            # Calculate priority score
            priority_score = self._calculate_priority_score(factors)
            
            # Convert score to priority level
            priority_level = self._score_to_priority_level(priority_score)
            
            log_structured(
                logger,
                "info",
                "Priority calculated",
                extra={
                    "work_item_id": work_item.id,
                    "calculated_priority": priority_level,
                    "priority_score": priority_score,
                    "factors": {
                        "severity_keywords": factors.severity_keywords,
                        "customer_impact": factors.customer_impact,
                        "security_related": factors.security_related,
                        "performance_impact": factors.performance_impact,
                        "blocking_issue": factors.blocking_issue
                    }
                }
            )
            
            return priority_level
            
        except Exception as e:
            logger.error(f"Error calculating priority for work item {work_item.id}: {e}")
            # Return medium priority as fallback
            return PriorityLevel.MEDIUM.value
    
    def _extract_priority_factors(self, work_item: WorkItem) -> PriorityFactors:
        """
        Extract factors that influence priority from the work item.
        """
        # Combine text fields for analysis
        text_content = " ".join([
            work_item.title or "",
            work_item.description or "",
            work_item.repro_steps or "",
            work_item.tags or ""
        ]).lower()
        
        # Count severity keywords
        severity_score = 0
        for level, keywords in self.severity_keywords.items():
            for keyword in keywords:
                if keyword in text_content:
                    if level == 'critical':
                        severity_score += 4
                    elif level == 'high':
                        severity_score += 3
                    elif level == 'medium':
                        severity_score += 2
                    else:
                        severity_score += 1
        
        # Check for customer impact
        customer_impact = any(keyword in text_content for keyword in self.customer_keywords)
        
        # Check for security issues
        security_related = any(keyword in text_content for keyword in self.security_keywords)
        
        # Check for performance issues
        performance_impact = any(keyword in text_content for keyword in self.performance_keywords)
        
        # Check for blocking issues
        blocking_issue = any(keyword in text_content for keyword in self.blocking_keywords)
        
        # Calculate area criticality
        area_criticality = self._calculate_area_criticality(work_item.area_path)
        
        # Extract user count if mentioned
        user_count = self._extract_user_count(text_content)
        
        # Calculate business impact score
        business_impact = self._calculate_business_impact(work_item, text_content)
        
        return PriorityFactors(
            severity_keywords=severity_score,
            customer_impact=customer_impact,
            security_related=security_related,
            performance_impact=performance_impact,
            blocking_issue=blocking_issue,
            area_criticality=area_criticality,
            user_count_affected=user_count,
            business_impact_score=business_impact
        )
    
    def _calculate_priority_score(self, factors: PriorityFactors) -> float:
        """
        Calculate a priority score from the extracted factors.
        """
        score = 0.0
        
        # Severity keywords (normalized to 0-1)
        severity_score = min(factors.severity_keywords / 10.0, 1.0)
        score += self.weights['severity_keywords'] * severity_score
        
        # Boolean factors
        if factors.customer_impact:
            score += self.weights['customer_impact']
        
        if factors.security_related:
            score += self.weights['security']
        
        if factors.performance_impact:
            score += self.weights['performance']
        
        if factors.blocking_issue:
            score += self.weights['blocking']
        
        # Area criticality
        score += self.weights['area_criticality'] * factors.area_criticality
        
        # User count impact (logarithmic scale)
        if factors.user_count_affected > 0:
            import math
            user_impact = min(math.log10(factors.user_count_affected) / 6.0, 1.0)  # Max at 1M users
            score += 0.1 * user_impact
        
        # Business impact
        score += 0.1 * factors.business_impact_score
        
        return min(score, 1.0)
    
    def _score_to_priority_level(self, score: float) -> int:
        """
        Convert priority score to priority level.
        """
        if score >= 0.8:
            return PriorityLevel.CRITICAL.value
        elif score >= 0.6:
            return PriorityLevel.HIGH.value
        elif score >= 0.3:
            return PriorityLevel.MEDIUM.value
        else:
            return PriorityLevel.LOW.value
    
    def _calculate_area_criticality(self, area_path: Optional[str]) -> float:
        """
        Calculate criticality score based on area path.
        """
        if not area_path:
            return 0.5  # Default medium criticality
        
        area_lower = area_path.lower()
        
        for area, criticality in self.area_criticality.items():
            if area in area_lower:
                return criticality
        
        return 0.5  # Default if no match
    
    def _extract_user_count(self, text: str) -> int:
        """
        Extract user count from text if mentioned.
        """
        # Look for patterns like "affects 1000 users", "100k users", etc.
        patterns = [
            r'(\d+(?:,\d+)*)\s*(?:thousand|k)\s*users?',
            r'(\d+(?:,\d+)*)\s*(?:million|m)\s*users?',
            r'(\d+(?:,\d+)*)\s*users?',
            r'affects?\s*(\d+(?:,\d+)*)',
            r'(\d+(?:,\d+)*)\s*customers?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                count_str = match.group(1).replace(',', '')
                try:
                    count = int(count_str)
                    # Handle k/m suffixes
                    if 'k' in match.group(0).lower():
                        count *= 1000
                    elif 'm' in match.group(0).lower():
                        count *= 1000000
                    return count
                except ValueError:
                    continue
        
        return 0
    
    def _calculate_business_impact(self, work_item: WorkItem, text: str) -> float:
        """
        Calculate business impact score.
        """
        impact_score = 0.0
        
        # High impact keywords
        high_impact_keywords = [
            'revenue', 'money', 'payment', 'billing', 'financial',
            'sla', 'contract', 'legal', 'compliance', 'audit'
        ]
        
        for keyword in high_impact_keywords:
            if keyword in text:
                impact_score += 0.2
        
        # Work item type impact
        if work_item.work_item_type in ['Bug', 'Defect']:
            impact_score += 0.3
        elif work_item.work_item_type in ['Feature', 'Epic']:
            impact_score += 0.1
        
        # Severity field impact
        if hasattr(work_item, 'severity'):
            severity = getattr(work_item, 'severity', '').lower()
            if severity in ['critical', '1 - critical']:
                impact_score += 0.4
            elif severity in ['high', '2 - high']:
                impact_score += 0.3
            elif severity in ['medium', '3 - medium']:
                impact_score += 0.2
        
        return min(impact_score, 1.0)
