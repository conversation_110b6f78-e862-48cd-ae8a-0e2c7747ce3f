[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "qa-ai-triage"
version = "1.0.0"
description = "AI-powered defect triage system for Azure DevOps"
authors = [
    {name = "QA AI Triage Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "azure-functions>=1.18.0",
    "azure-functions-worker>=1.0.0",
    "azure-search-documents>=11.4.0",
    "azure-keyvault-secrets>=4.7.0",
    "azure-identity>=1.15.0",
    "azure-storage-blob>=12.19.0",
    "requests>=2.31.0",
    "httpx>=0.25.0",
    "openai>=1.3.0",
    "azure-ai-textanalytics>=5.3.0",
    "sentence-transformers>=2.2.2",
    "transformers>=4.35.0",
    "torch>=2.1.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.3.0",
    "pydantic>=2.5.0",
    "pandas>=2.1.0",
    "python-dateutil>=2.8.2",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
    "jinja2>=3.1.2",
    "markdown>=3.5.0",
    "beautifulsoup4>=4.12.0",
    "lxml>=4.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.12.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
