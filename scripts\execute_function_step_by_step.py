#!/usr/bin/env python3
"""
Execute Azure Function Step by Step
===================================

This script executes the actual Azure Function step by step, showing real execution
of each component in the workflow orchestrator.

Usage:
    python execute_function_step_by_step.py --work-item 12345
    python execute_function_step_by_step.py --local-test
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

def load_local_settings():
    """Load environment variables from local.settings.json."""
    settings_file = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r') as f:
                settings = json.load(f)

            # Set environment variables from the Values section
            if "Values" in settings:
                for key, value in settings["Values"].items():
                    os.environ[key] = str(value)

            print("✅ Environment variables loaded from local.settings.json")
            return True
        except Exception as e:
            print(f"⚠️  Warning: Could not load local.settings.json: {e}")
            return False
    else:
        print("⚠️  Warning: local.settings.json not found")
        return False

try:
    from __app__.workflow_orchestrator import WorkflowOrchestrator, WorkflowContext
    from __app__.common.utils.logging import setup_logging, log_structured
    from __app__.common.models.schemas import WorkItem, TriageResult
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running from the project root and all dependencies are installed.")
    sys.exit(1)

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


class StepByStepExecutor:
    """Executes the Azure Function workflow step by step with detailed output."""
    
    def __init__(self):
        self.orchestrator = None
        
    async def initialize_function_components(self):
        """Initialize all Azure Function components."""
        print("🔧 INITIALIZING AZURE FUNCTION COMPONENTS")
        print("=" * 60)
        
        try:
            print("📦 Creating WorkflowOrchestrator instance...")
            self.orchestrator = WorkflowOrchestrator()
            
            print("🔌 Initializing clients and AI engines...")
            await self.orchestrator.initialize()
            
            print("✅ All components initialized successfully!")
            print(f"   • ADO Client: {'✅' if self.orchestrator.clients.get('ado') else '❌'}")
            print(f"   • Search Client: {'✅' if self.orchestrator.clients.get('search') else '❌'}")
            print(f"   • Teams Client: {'✅' if self.orchestrator.clients.get('teams') else '❌'}")
            print(f"   • Duplicate Detector: {'✅' if self.orchestrator.ai_engines.get('duplicate') else '❌'}")
            print(f"   • Assignment Engine: {'✅' if self.orchestrator.ai_engines.get('assigner') else '❌'}")
            print(f"   • Priority Engine: {'✅' if self.orchestrator.ai_engines.get('priority') else '❌'}")
            print(f"   • Message Generator: {'✅' if self.orchestrator.message_generator else '❌'}")
            print(f"   • Notification Engine: {'✅' if self.orchestrator.notification_engine else '❌'}")
            print()
            
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            raise
    
    async def execute_step_1(self, work_item_id: str) -> WorkflowContext:
        """Execute Step 1: Work Item Creation/Retrieval"""
        print("🔍 EXECUTING STEP 1: WORK ITEM CREATION/RETRIEVAL")
        print("=" * 60)
        
        # Create initial context
        context = WorkflowContext(
            work_item=None,
            historical_items=[],
            triage_result=None,
            ai_message=None,
            notification_ids=[],
            email_sent=False,
            teams_sent=False,
            errors=[],
            start_time=datetime.utcnow(),
            step_timings={}
        )
        
        print(f"📋 Retrieving work item: {work_item_id}")
        print(f"🕐 Step started at: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        try:
            # For demo/test purposes, create mock work item if it doesn't exist
            if work_item_id.startswith("TEST-") or work_item_id.startswith("DEMO-"):
                context = await self._create_mock_work_item(context, work_item_id)
            else:
                # Execute the actual Step 1 function for real work items
                context = await self.orchestrator._step_1_work_item_creation(context, work_item_id)

            if context.work_item:
                print(f"✅ Work item retrieved successfully!")
                print(f"   📋 ID: {context.work_item.id}")
                print(f"   📝 Title: {context.work_item.title}")
                print(f"   🏷️  Type: {context.work_item.work_item_type}")
                print(f"   📊 Priority: {context.work_item.priority}")
                print(f"   👤 Assigned: {context.work_item.assigned_to or 'Unassigned'}")
                print(f"   🏗️  Project: {context.work_item.project}")
                print(f"   📅 Created: {context.work_item.created_date}")
            else:
                print(f"❌ Failed to retrieve work item")
            
            step_time = context.step_timings.get('step_1_work_item_creation', 0)
            print(f"⏱️  Execution time: {step_time:.2f} seconds")
            
            if context.errors:
                print(f"⚠️  Errors: {len(context.errors)}")
                for error in context.errors:
                    print(f"   • {error}")
            
        except Exception as e:
            print(f"❌ Step 1 execution failed: {str(e)}")
            context.errors.append(f"Step 1 failed: {str(e)}")
        
        print()
        return context

    async def _create_mock_work_item(self, context: WorkflowContext, work_item_id: str) -> WorkflowContext:
        """Create a mock work item for testing purposes."""
        step_start = datetime.utcnow()

        print(f"🧪 Creating mock work item for testing: {work_item_id}")

        # Simulate API delay
        await asyncio.sleep(0.5)

        # Create mock work item
        context.work_item = WorkItem(
            id=12345,  # Use integer ID
            title="Critical bug in user authentication system",
            description="Users unable to login after recent deployment. Error occurs on login page with 'Authentication failed' message. Affects approximately 500+ users.",
            work_item_type="Bug",
            state="New",
            assigned_to="",
            created_date=datetime.utcnow().isoformat(),
            project="WebApp",
            priority=1,
            severity="High",
            tags="login, authentication, critical, deployment",
            area_path="WebApp\\Authentication",
            iteration_path="WebApp\\Sprint 24"
        )

        step_time = (datetime.utcnow() - step_start).total_seconds()
        context.step_timings['step_1_work_item_creation'] = step_time

        print(f"✅ Mock work item created successfully!")

        return context
    
    async def execute_step_2(self, context: WorkflowContext) -> WorkflowContext:
        """Execute Step 2: Historical Analysis"""
        print("📊 EXECUTING STEP 2: HISTORICAL ANALYSIS & PATTERN RECOGNITION")
        print("=" * 60)
        
        if not context.work_item:
            print("❌ Cannot execute Step 2: No work item from Step 1")
            context.errors.append("Step 2 skipped: No work item available")
            print()
            return context
        
        print(f"🔍 Analyzing historical patterns for: {context.work_item.id}")
        print(f"🕐 Step started at: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        try:
            # Execute the actual Step 2 function
            context = await self.orchestrator._step_2_historical_analysis(context)
            
            print(f"✅ Historical analysis completed!")
            print(f"   🔍 Similar items found: {len(context.historical_items)}")
            
            if context.historical_items:
                print(f"   📈 Top similar items:")
                for i, item in enumerate(context.historical_items[:3], 1):
                    print(f"      {i}. ID: {item.get('id', 'N/A')} (Similarity: {item.get('similarity_score', 0):.2f})")
                    if item.get('resolution'):
                        print(f"         Resolution: {item['resolution']}")
            
            step_time = context.step_timings.get('step_2_historical_analysis', 0)
            print(f"⏱️  Execution time: {step_time:.2f} seconds")
            
            if any("Step 2" in error for error in context.errors):
                step_errors = [e for e in context.errors if "Step 2" in e]
                print(f"⚠️  Errors: {len(step_errors)}")
                for error in step_errors:
                    print(f"   • {error}")
            
        except Exception as e:
            print(f"❌ Step 2 execution failed: {str(e)}")
            context.errors.append(f"Step 2 failed: {str(e)}")
        
        print()
        return context
    
    async def execute_step_3(self, context: WorkflowContext) -> WorkflowContext:
        """Execute Step 3: AI Triage & Message Generation"""
        print("🤖 EXECUTING STEP 3: AI TRIAGE & MESSAGE GENERATION")
        print("=" * 60)
        
        if not context.work_item:
            print("❌ Cannot execute Step 3: No work item from Step 1")
            context.errors.append("Step 3 skipped: No work item available")
            print()
            return context
        
        print(f"🧠 Running AI triage for: {context.work_item.id}")
        print(f"🕐 Step started at: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        try:
            # Execute the actual Step 3 function
            context = await self.orchestrator._step_3_ai_triage_and_message_generation(context)
            
            if context.triage_result:
                print(f"✅ AI triage completed!")
                print(f"   👤 Assigned to: {context.triage_result.assigned_to}")
                print(f"   📊 Priority: {context.triage_result.priority}")
                print(f"   🎯 Confidence: {context.triage_result.confidence_score:.2f}")
                print(f"   🔍 Duplicates found: {len(context.triage_result.duplicates)}")
                
                if context.triage_result.reasoning:
                    reasoning_preview = context.triage_result.reasoning[:100] + "..." if len(context.triage_result.reasoning) > 100 else context.triage_result.reasoning
                    print(f"   💭 Reasoning: {reasoning_preview}")
            else:
                print(f"❌ AI triage failed")
            
            if context.ai_message:
                print(f"   📝 AI message generated: ✅")
                message_preview = context.ai_message[:100] + "..." if len(context.ai_message) > 100 else context.ai_message
                print(f"   📄 Message preview: {message_preview}")
            
            step_time = context.step_timings.get('step_3_ai_triage_and_message_generation', 0)
            print(f"⏱️  Execution time: {step_time:.2f} seconds")
            
            if any("Step 3" in error for error in context.errors):
                step_errors = [e for e in context.errors if "Step 3" in e]
                print(f"⚠️  Errors: {len(step_errors)}")
                for error in step_errors:
                    print(f"   • {error}")
            
        except Exception as e:
            print(f"❌ Step 3 execution failed: {str(e)}")
            context.errors.append(f"Step 3 failed: {str(e)}")
        
        print()
        return context
    
    async def execute_step_4(self, context: WorkflowContext) -> WorkflowContext:
        """Execute Step 4: Email Notification"""
        print("📧 EXECUTING STEP 4: EMAIL NOTIFICATION DELIVERY")
        print("=" * 60)
        
        if not context.work_item:
            print("❌ Cannot execute Step 4: No work item from Step 1")
            context.errors.append("Step 4 skipped: No work item available")
            print()
            return context
        
        print(f"📬 Preparing email notification for: {context.work_item.id}")
        print(f"🕐 Step started at: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        try:
            # Execute the actual Step 4 function
            context = await self.orchestrator._step_4_email_notification(context)
            
            if context.email_sent:
                print(f"✅ Email notification sent successfully!")
                print(f"   📧 Logic App triggered: ✅")
                print(f"   📋 Content includes:")
                print(f"      • Work item details")
                print(f"      • AI triage results")
                print(f"      • Historical context")
                print(f"      • Recommended actions")
            else:
                print(f"❌ Email notification failed")
            
            step_time = context.step_timings.get('step_4_email_notification', 0)
            print(f"⏱️  Execution time: {step_time:.2f} seconds")
            
            if any("Step 4" in error for error in context.errors):
                step_errors = [e for e in context.errors if "Step 4" in e]
                print(f"⚠️  Errors: {len(step_errors)}")
                for error in step_errors:
                    print(f"   • {error}")
            
        except Exception as e:
            print(f"❌ Step 4 execution failed: {str(e)}")
            context.errors.append(f"Step 4 failed: {str(e)}")
        
        print()
        return context
    
    async def execute_step_5(self, context: WorkflowContext) -> WorkflowContext:
        """Execute Step 5: Teams Message"""
        print("💬 EXECUTING STEP 5: TEAMS MESSAGE BROADCASTING")
        print("=" * 60)
        
        if not context.work_item:
            print("❌ Cannot execute Step 5: No work item from Step 1")
            context.errors.append("Step 5 skipped: No work item available")
            print()
            return context
        
        print(f"📢 Sending Teams notification for: {context.work_item.id}")
        print(f"🕐 Step started at: {datetime.utcnow().strftime('%H:%M:%S')}")
        
        try:
            # Execute the actual Step 5 function
            context = await self.orchestrator._step_5_teams_message(context)
            
            if context.teams_sent:
                print(f"✅ Teams message sent successfully!")
                print(f"   🎴 Adaptive card created: ✅")
                print(f"   📢 Channel delivery: ✅")
                print(f"   🔘 Interactive elements:")
                print(f"      • Accept assignment button")
                print(f"      • Request more info button")
                print(f"      • View similar issues link")
            else:
                print(f"❌ Teams message failed")
            
            step_time = context.step_timings.get('step_5_teams_message', 0)
            print(f"⏱️  Execution time: {step_time:.2f} seconds")
            
            if any("Step 5" in error for error in context.errors):
                step_errors = [e for e in context.errors if "Step 5" in e]
                print(f"⚠️  Errors: {len(step_errors)}")
                for error in step_errors:
                    print(f"   • {error}")
            
        except Exception as e:
            print(f"❌ Step 5 execution failed: {str(e)}")
            context.errors.append(f"Step 5 failed: {str(e)}")
        
        print()
        return context
    
    def print_final_execution_summary(self, context: WorkflowContext):
        """Print final execution summary"""
        print("📈 FINAL EXECUTION SUMMARY")
        print("=" * 60)
        
        total_time = sum(context.step_timings.values())
        successful_steps = 5 - len([e for e in context.errors if "Step" in e])
        
        print(f"🎯 **AZURE FUNCTION EXECUTION COMPLETED**")
        print(f"   ⏱️  Total execution time: {total_time:.2f} seconds")
        print(f"   ✅ Successful steps: {successful_steps}/5")
        print(f"   📧 Email delivered: {'✅' if context.email_sent else '❌'}")
        print(f"   💬 Teams delivered: {'✅' if context.teams_sent else '❌'}")
        print(f"   🎯 Overall success: {'✅ SUCCESS' if successful_steps == 5 else '❌ PARTIAL'}")
        
        print(f"\n📊 **STEP EXECUTION BREAKDOWN:**")
        step_names = [
            "Work Item Creation/Retrieval",
            "Historical Analysis & Pattern Recognition",
            "AI Triage & Message Generation", 
            "Email Notification Delivery",
            "Teams Message Broadcasting"
        ]
        
        for i, step_name in enumerate(step_names, 1):
            step_key = f"step_{i}_" + step_name.lower().replace(" ", "_").replace("/", "_").replace("&", "and")
            step_time = context.step_timings.get(step_key, 0)
            step_errors = [e for e in context.errors if f"Step {i}" in e]
            status = "❌" if step_errors else "✅"
            print(f"   Step {i}: {step_name} - {status} ({step_time:.2f}s)")
        
        if context.errors:
            print(f"\n❌ **ERRORS ENCOUNTERED ({len(context.errors)}):**")
            for i, error in enumerate(context.errors, 1):
                print(f"   {i}. {error}")
        
        print(f"\n✨ Azure Function execution completed!")


async def main():
    """Main execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Execute Azure Function Step by Step")
    parser.add_argument("--work-item", type=str, help="Work item ID to process")
    parser.add_argument("--local-test", action="store_true", help="Run with test data")
    
    args = parser.parse_args()
    
    if not args.work_item and not args.local_test:
        parser.print_help()
        return
    
    work_item_id = args.work_item or "TEST-12345"

    print("🚀 AZURE FUNCTION STEP-BY-STEP EXECUTION")
    print("=" * 80)
    print(f"📝 Work Item ID: {work_item_id}")
    print(f"🕐 Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
    print()

    # Load environment variables from local.settings.json
    load_local_settings()
    print()
    
    executor = StepByStepExecutor()
    
    try:
        # Initialize Azure Function components
        await executor.initialize_function_components()
        
        # Execute each step
        context = await executor.execute_step_1(work_item_id)
        context = await executor.execute_step_2(context)
        context = await executor.execute_step_3(context)
        context = await executor.execute_step_4(context)
        context = await executor.execute_step_5(context)
        
        # Print final summary
        executor.print_final_execution_summary(context)
        
    except Exception as e:
        print(f"❌ Execution failed: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
