"""
Text processing utilities for HTML cleaning, normalization, and similarity calculation.
"""

import re
import logging
from typing import List, Optional, Set, Tuple
from html import unescape
from difflib import SequenceMatcher
import hashlib

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False
    logging.warning("BeautifulSoup not available, using regex for HTML cleaning")

logger = logging.getLogger(__name__)


def clean_html(text: str) -> str:
    """
    Remove HTML tags and decode HTML entities from text.
    
    Args:
        text: Input text that may contain HTML
    
    Returns:
        Cleaned text without HTML tags
    """
    if not text:
        return ""
    
    try:
        if BS4_AVAILABLE:
            # Use BeautifulSoup for robust HTML parsing
            soup = BeautifulSoup(text, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean up whitespace
            cleaned = soup.get_text()
        else:
            # Fallback to regex-based cleaning
            # Remove script and style content
            text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.DOTALL | re.IGNORECASE)
            text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
            
            # Remove HTML tags
            cleaned = re.sub(r'<[^>]+>', '', text)
        
        # Decode HTML entities
        cleaned = unescape(cleaned)
        
        # Clean up whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        cleaned = cleaned.strip()
        
        return cleaned
        
    except Exception as e:
        logger.warning(f"Error cleaning HTML: {e}")
        # Fallback: just remove basic tags
        return re.sub(r'<[^>]+>', '', text).strip()


def normalize_text(text: str) -> str:
    """
    Normalize text for consistent processing.
    
    Args:
        text: Input text to normalize
    
    Returns:
        Normalized text
    """
    if not text:
        return ""
    
    try:
        # Convert to lowercase
        normalized = text.lower()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized)
        
        # Remove special characters but keep basic punctuation
        normalized = re.sub(r'[^\w\s\.\,\!\?\-\(\)]', ' ', normalized)
        
        # Clean up whitespace again
        normalized = re.sub(r'\s+', ' ', normalized)
        normalized = normalized.strip()
        
        return normalized
        
    except Exception as e:
        logger.warning(f"Error normalizing text: {e}")
        return text.strip()


def extract_keywords(text: str, min_length: int = 3, max_keywords: int = 20) -> List[str]:
    """
    Extract keywords from text.
    
    Args:
        text: Input text
        min_length: Minimum keyword length
        max_keywords: Maximum number of keywords to return
    
    Returns:
        List of extracted keywords
    """
    if not text:
        return []
    
    try:
        # Normalize text
        normalized = normalize_text(text)
        
        # Split into words
        words = normalized.split()
        
        # Common stop words to filter out
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we',
            'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her',
            'its', 'our', 'their', 'when', 'where', 'why', 'how', 'what', 'which',
            'who', 'whom', 'whose', 'if', 'then', 'else', 'than', 'as', 'so',
            'can', 'may', 'might', 'must', 'shall', 'not', 'no', 'yes'
        }
        
        # Filter words
        keywords = []
        for word in words:
            # Remove punctuation
            clean_word = re.sub(r'[^\w]', '', word)
            
            if (len(clean_word) >= min_length and 
                clean_word.lower() not in stop_words and
                not clean_word.isdigit()):
                keywords.append(clean_word.lower())
        
        # Remove duplicates while preserving order
        unique_keywords = []
        seen = set()
        for keyword in keywords:
            if keyword not in seen:
                unique_keywords.append(keyword)
                seen.add(keyword)
        
        return unique_keywords[:max_keywords]
        
    except Exception as e:
        logger.warning(f"Error extracting keywords: {e}")
        return []


def calculate_text_similarity(text1: str, text2: str) -> float:
    """
    Calculate similarity between two texts using sequence matching.
    
    Args:
        text1: First text
        text2: Second text
    
    Returns:
        Similarity score between 0.0 and 1.0
    """
    if not text1 or not text2:
        return 0.0
    
    try:
        # Normalize both texts
        norm1 = normalize_text(text1)
        norm2 = normalize_text(text2)
        
        if not norm1 or not norm2:
            return 0.0
        
        # Calculate sequence similarity
        matcher = SequenceMatcher(None, norm1, norm2)
        similarity = matcher.ratio()
        
        return similarity
        
    except Exception as e:
        logger.warning(f"Error calculating text similarity: {e}")
        return 0.0


def extract_signature(title: str, description: str) -> Optional[str]:
    """
    Extract a signature/fingerprint from work item content for duplicate detection.
    
    Args:
        title: Work item title
        description: Work item description
    
    Returns:
        Signature string or None if no meaningful signature found
    """
    try:
        # Combine title and description
        combined_text = f"{title or ''} {description or ''}"
        
        if not combined_text.strip():
            return None
        
        # Look for error patterns, stack traces, or specific error messages
        signature_patterns = [
            # Exception patterns
            r'(\w+Exception[:\s]+[^\n\r]{20,100})',
            r'(Error[:\s]+[^\n\r]{20,100})',
            r'(Failed[:\s]+[^\n\r]{20,100})',
            
            # Stack trace patterns
            r'(at\s+[\w\.]+\([^\)]+\))',
            r'(in\s+[\w\.]+\s+line\s+\d+)',
            
            # HTTP error patterns
            r'(HTTP\s+\d{3}[:\s]+[^\n\r]{10,50})',
            
            # Database error patterns
            r'(SQL[:\s]+[^\n\r]{20,100})',
            
            # File path patterns
            r'([A-Za-z]:\\[^\s]{10,})',
            r'(/[^\s]{10,})',
        ]
        
        signatures = []
        for pattern in signature_patterns:
            matches = re.findall(pattern, combined_text, re.IGNORECASE)
            signatures.extend(matches)
        
        if signatures:
            # Use the first meaningful signature
            signature = signatures[0].strip()
            
            # Normalize the signature
            signature = normalize_text(signature)
            
            # Create a hash for consistent comparison
            signature_hash = hashlib.md5(signature.encode()).hexdigest()[:16]
            
            return signature_hash
        
        return None
        
    except Exception as e:
        logger.warning(f"Error extracting signature: {e}")
        return None


def count_severity_indicators(text: str) -> int:
    """
    Count severity indicators in text.
    
    Args:
        text: Input text to analyze
    
    Returns:
        Count of severity indicators
    """
    if not text:
        return 0
    
    try:
        text_lower = text.lower()
        
        severity_indicators = [
            'critical', 'urgent', 'high priority', 'blocker', 'blocking',
            'crash', 'hang', 'freeze', 'data loss', 'corruption',
            'security', 'vulnerability', 'exploit', 'breach',
            'production', 'live', 'customer impact', 'revenue',
            'sla', 'escalation', 'emergency'
        ]
        
        count = 0
        for indicator in severity_indicators:
            if indicator in text_lower:
                count += 1
        
        return count
        
    except Exception as e:
        logger.warning(f"Error counting severity indicators: {e}")
        return 0


def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate text to a maximum length.
    
    Args:
        text: Input text
        max_length: Maximum length
        suffix: Suffix to add when truncating
    
    Returns:
        Truncated text
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def extract_urls(text: str) -> List[str]:
    """
    Extract URLs from text.
    
    Args:
        text: Input text
    
    Returns:
        List of URLs found in text
    """
    if not text:
        return []
    
    try:
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text, re.IGNORECASE)
        return urls
        
    except Exception as e:
        logger.warning(f"Error extracting URLs: {e}")
        return []


def extract_email_addresses(text: str) -> List[str]:
    """
    Extract email addresses from text.
    
    Args:
        text: Input text
    
    Returns:
        List of email addresses found in text
    """
    if not text:
        return []
    
    try:
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        return emails
        
    except Exception as e:
        logger.warning(f"Error extracting email addresses: {e}")
        return []


def split_into_sentences(text: str) -> List[str]:
    """
    Split text into sentences.
    
    Args:
        text: Input text
    
    Returns:
        List of sentences
    """
    if not text:
        return []
    
    try:
        # Simple sentence splitting on periods, exclamation marks, and question marks
        # followed by whitespace and capital letter
        sentence_pattern = r'[.!?]+\s+(?=[A-Z])'
        sentences = re.split(sentence_pattern, text)
        
        # Clean up sentences
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
        
    except Exception as e:
        logger.warning(f"Error splitting into sentences: {e}")
        return [text]
