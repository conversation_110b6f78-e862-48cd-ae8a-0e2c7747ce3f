using '../main-notification-deployment.bicep'

// Development environment parameters
param environment = 'dev'
param location = 'East US 2'
param appName = 'qa-ai-triage'

// Feature flags
param enablePagerDuty = false
param enableMonitoring = true

// Azure DevOps configuration
param adoOrganization = 'https://dev.azure.com/your-org'
param adoProject = 'YourProject'

// Alert configuration
param alertEmailAddresses = [
  '<EMAIL>'
  '<EMAIL>'
]
param alertTeamsWebhookUrl = 'https://outlook.office.com/webhook/your-dev-alerts-webhook'

// Secrets (these should be provided at deployment time or via Key Vault)
// param pagerDutyApiKey = '' // Provided securely at deployment
// param teamsWebhookUrl = '' // Provided securely at deployment  
// param adoPat = '' // Provided securely at deployment
// param openAiApiKey = '' // Provided securely at deployment
