"""
Noise Control System for Intelligent Notifications.
Prevents notification spam through deduplication, rate limiting, and quiet hours.
"""

import logging
import hashlib
from typing import List, Dict, Any, Optional, Tuple, TYPE_CHECKING
from datetime import datetime, timedelta, time
import pytz

if TYPE_CHECKING:
    from ..models import (
        NotificationTrigger,
        StakeholderRoute,
        Deduplication<PERSON>ey,
        RateLimitBucket,
        NotificationStatus,
        TriggerType,
        RecipientType,
        EscalationLevel,
        QuietHours,
        ProjectNotificationConfig
    )
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class NoiseController:
    """
    Noise control system that prevents notification spam while ensuring
    critical issues get proper attention.
    
    Features:
    - Deduplication with 60-minute windows
    - Rate limiting per recipient and channel
    - Quiet hours support with timezone awareness
    - Escalation bypass for critical issues
    - Configurable thresholds per project
    """
    
    def __init__(self, config: Config):
        self.config = config
        self._dedup_cache: Dict[str, DeduplicationKey] = {}
        self._rate_limit_buckets: Dict[str, RateLimitBucket] = {}
        self._project_configs: Dict[str, ProjectNotificationConfig] = {}
        
    def load_project_config(self, project: str, config: "ProjectNotificationConfig") -> None:
        """Load project-specific noise control configuration."""
        self._project_configs[project] = config
        log_structured(
            logger,
            "info",
            f"Loaded noise control config for project: {project}",
            extra={"project": project}
        )
    
    def should_send_notification(
        self,
        trigger: "NotificationTrigger",
        route: "StakeholderRoute",
        project_config: Optional["ProjectNotificationConfig"] = None
    ) -> Tuple[bool, "NotificationStatus", str]:
        """
        Determine if a notification should be sent based on noise control rules.
        
        Args:
            trigger: Notification trigger
            route: Stakeholder route
            project_config: Project-specific configuration
        
        Returns:
            Tuple of (should_send, status, reason)
        """
        # Import here to avoid circular dependencies
        from ..models import NotificationStatus

        try:
            if not project_config:
                project_config = self._get_project_config(trigger.project)

            # Check deduplication first
            is_duplicate, dedup_reason = self._check_deduplication(
                trigger, route, project_config
            )
            if is_duplicate:
                return False, NotificationStatus.DEDUPLICATED, dedup_reason
            
            # Check rate limiting
            is_rate_limited, rate_reason = self._check_rate_limiting(
                trigger, route, project_config
            )
            if is_rate_limited:
                return False, NotificationStatus.RATE_LIMITED, rate_reason
            
            # Check quiet hours (with escalation bypass)
            is_quiet_hours, quiet_reason = self._check_quiet_hours(
                trigger, route, project_config
            )
            if is_quiet_hours:
                return False, NotificationStatus.QUIET_HOURS, quiet_reason
            
            # All checks passed
            self._record_notification_sent(trigger, route, project_config)
            return True, NotificationStatus.PENDING, "Noise control checks passed"
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error in noise control evaluation: {e}",
                extra={
                    "work_item_id": trigger.work_item_id,
                    "recipient_id": route.recipient_id
                },
                exc_info=True
            )
            # Default to allowing notification on error
            return True, NotificationStatus.PENDING, "Error in noise control - allowing notification"
    
    def _check_deduplication(
        self,
        trigger: "NotificationTrigger",
        route: "StakeholderRoute",
        config: "ProjectNotificationConfig"
    ) -> Tuple[bool, str]:
        """Check if notification is a duplicate within the dedup window."""
        
        # Generate deduplication key
        dedup_key = self._generate_dedup_key(trigger, route)
        
        # Check if key exists and is still valid
        if dedup_key in self._dedup_cache:
            existing_key = self._dedup_cache[dedup_key]
            
            if datetime.utcnow() < existing_key.expires_at:
                # Still within dedup window
                log_structured(
                    logger,
                    "info",
                    "Notification deduplicated",
                    extra={
                        "work_item_id": trigger.work_item_id,
                        "recipient_id": route.recipient_id,
                        "dedup_key": dedup_key,
                        "expires_at": existing_key.expires_at.isoformat()
                    }
                )
                return True, f"Duplicate notification within {config.dedup_window_minutes} minute window"
            else:
                # Key expired, remove it
                del self._dedup_cache[dedup_key]
        
        # Check for escalation bypass
        # Import here to avoid circular dependencies
        from ..models import EscalationLevel

        if trigger.escalation_level >= EscalationLevel.URGENT:
            log_structured(
                logger,
                "info",
                "Deduplication bypassed for urgent/critical escalation",
                extra={
                    "work_item_id": trigger.work_item_id,
                    "escalation_level": trigger.escalation_level
                }
            )
            return False, "Escalation bypass"
        
        return False, "No duplicate found"
    
    def _check_rate_limiting(
        self,
        trigger: "NotificationTrigger",
        route: "StakeholderRoute",
        config: "ProjectNotificationConfig"
    ) -> Tuple[bool, str]:
        """Check if notification exceeds rate limits."""
        
        # Determine rate limit key and threshold
        rate_key, max_count = self._get_rate_limit_params(route, config)
        
        if not rate_key or max_count <= 0:
            return False, "No rate limit configured"
        
        # Get or create rate limit bucket
        bucket = self._get_or_create_rate_bucket(rate_key, max_count)
        
        # Check if bucket is expired and reset if needed
        if bucket.is_expired:
            bucket.current_count = 0
            bucket.window_start = datetime.utcnow()
        
        # Check if rate limit is exceeded
        if bucket.is_exceeded:
            # Check for escalation bypass
            if trigger.escalation_level >= EscalationLevel.CRITICAL:
                log_structured(
                    logger,
                    "info",
                    "Rate limit bypassed for critical escalation",
                    extra={
                        "work_item_id": trigger.work_item_id,
                        "rate_key": rate_key,
                        "escalation_level": trigger.escalation_level
                    }
                )
                return False, "Critical escalation bypass"
            
            log_structured(
                logger,
                "info",
                "Notification rate limited",
                extra={
                    "work_item_id": trigger.work_item_id,
                    "recipient_id": route.recipient_id,
                    "rate_key": rate_key,
                    "current_count": bucket.current_count,
                    "max_count": bucket.max_count
                }
            )
            return True, f"Rate limit exceeded: {bucket.current_count}/{bucket.max_count} per hour"
        
        return False, "Within rate limits"
    
    def _check_quiet_hours(
        self,
        trigger: "NotificationTrigger",
        route: "StakeholderRoute",
        config: "ProjectNotificationConfig"
    ) -> Tuple[bool, str]:
        """Check if notification falls within quiet hours."""
        
        if not config.quiet_hours:
            return False, "No quiet hours configured"
        
        quiet_hours = config.quiet_hours
        
        # Check for escalation bypass
        if trigger.escalation_level >= EscalationLevel.URGENT:
            log_structured(
                logger,
                "info",
                "Quiet hours bypassed for urgent/critical escalation",
                extra={
                    "work_item_id": trigger.work_item_id,
                    "escalation_level": trigger.escalation_level
                }
            )
            return False, "Urgent escalation bypass"
        
        # Get current time in the configured timezone
        try:
            tz = pytz.timezone(quiet_hours.timezone)
            current_time = datetime.now(tz).time()
            current_weekday = datetime.now(tz).weekday()
            
            # Check if current day is in quiet hours days
            if current_weekday not in quiet_hours.days_of_week:
                return False, "Not a quiet hours day"
            
            # Parse quiet hours times
            start_time = datetime.strptime(quiet_hours.start_time, '%H:%M').time()
            end_time = datetime.strptime(quiet_hours.end_time, '%H:%M').time()
            
            # Check if current time is within quiet hours
            if start_time <= end_time:
                # Same day quiet hours (e.g., 18:00 to 22:00)
                is_quiet = start_time <= current_time <= end_time
            else:
                # Overnight quiet hours (e.g., 22:00 to 08:00)
                is_quiet = current_time >= start_time or current_time <= end_time
            
            if is_quiet:
                log_structured(
                    logger,
                    "info",
                    "Notification blocked by quiet hours",
                    extra={
                        "work_item_id": trigger.work_item_id,
                        "recipient_id": route.recipient_id,
                        "current_time": current_time.strftime('%H:%M'),
                        "quiet_hours": f"{quiet_hours.start_time}-{quiet_hours.end_time}",
                        "timezone": quiet_hours.timezone
                    }
                )
                return True, f"Within quiet hours ({quiet_hours.start_time}-{quiet_hours.end_time} {quiet_hours.timezone})"
            
            return False, "Outside quiet hours"
            
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error checking quiet hours: {e}",
                extra={"work_item_id": trigger.work_item_id}
            )
            return False, "Error checking quiet hours - allowing notification"
    
    def _record_notification_sent(
        self,
        trigger: "NotificationTrigger",
        route: "StakeholderRoute",
        config: "ProjectNotificationConfig"
    ) -> None:
        """Record that a notification was sent for dedup and rate limiting."""
        
        # Record deduplication key
        # Import here to avoid circular dependencies
        from ..models import DeduplicationKey

        dedup_key = self._generate_dedup_key(trigger, route)
        expires_at = datetime.utcnow() + timedelta(minutes=config.dedup_window_minutes)

        self._dedup_cache[dedup_key] = DeduplicationKey(
            work_item_id=trigger.work_item_id,
            notification_type=trigger.trigger_type,
            recipient_id=route.recipient_id,
            key_hash=dedup_key,
            created_at=datetime.utcnow(),
            expires_at=expires_at
        )
        
        # Update rate limit bucket
        rate_key, max_count = self._get_rate_limit_params(route, config)
        if rate_key and max_count > 0:
            bucket = self._get_or_create_rate_bucket(rate_key, max_count)
            bucket.current_count += 1
    
    def _generate_dedup_key(self, trigger: "NotificationTrigger", route: "StakeholderRoute") -> str:
        """Generate a deduplication key for the notification."""
        key_data = f"{trigger.work_item_id}_{trigger.trigger_type}_{route.recipient_id}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_rate_limit_params(
        self,
        route: "StakeholderRoute",
        config: "ProjectNotificationConfig"
    ) -> Tuple[Optional[str], int]:
        """Get rate limit key and threshold for a route."""
        # Import here to avoid circular dependencies
        from ..models import RecipientType

        if route.recipient_type == RecipientType.USER:
            rate_key = f"user_{route.recipient_id}"
            max_count = config.rate_limits.get("per_user_per_hour", 5)
        elif route.recipient_type == RecipientType.TEAM_CHANNEL:
            rate_key = f"channel_{route.recipient_id}"
            max_count = config.rate_limits.get("per_channel_per_hour", 20)
        elif route.recipient_type == RecipientType.PAGERDUTY:
            rate_key = f"pagerduty_{route.recipient_id}"
            max_count = config.rate_limits.get("per_pagerduty_per_hour", 10)
        else:
            return None, 0
        
        return rate_key, max_count
    
    def _get_or_create_rate_bucket(self, rate_key: str, max_count: int) -> "RateLimitBucket":
        """Get or create a rate limit bucket."""
        # Import here to avoid circular dependencies
        from ..models import RateLimitBucket

        if rate_key not in self._rate_limit_buckets:
            self._rate_limit_buckets[rate_key] = RateLimitBucket(
                bucket_key=rate_key,
                current_count=0,
                max_count=max_count,
                window_start=datetime.utcnow(),
                window_duration=timedelta(hours=1)
            )
        
        return self._rate_limit_buckets[rate_key]
    
    def _get_project_config(self, project: str) -> "ProjectNotificationConfig":
        """Get project configuration or return default."""
        if project in self._project_configs:
            return self._project_configs[project]
        
        # Return default configuration
        # Import here to avoid circular dependencies
        from ..models import ProjectNotificationConfig

        return ProjectNotificationConfig(project_name=project)
    
    def cleanup_expired_entries(self) -> None:
        """Clean up expired deduplication keys and rate limit buckets."""
        current_time = datetime.utcnow()
        
        # Clean up expired dedup keys
        expired_dedup_keys = [
            key for key, dedup_key in self._dedup_cache.items()
            if current_time > dedup_key.expires_at
        ]
        
        for key in expired_dedup_keys:
            del self._dedup_cache[key]
        
        # Clean up expired rate limit buckets
        expired_rate_keys = [
            key for key, bucket in self._rate_limit_buckets.items()
            if bucket.is_expired
        ]
        
        for key in expired_rate_keys:
            del self._rate_limit_buckets[key]
        
        if expired_dedup_keys or expired_rate_keys:
            log_structured(
                logger,
                "info",
                "Cleaned up expired noise control entries",
                extra={
                    "expired_dedup_keys": len(expired_dedup_keys),
                    "expired_rate_buckets": len(expired_rate_keys)
                }
            )
    
    def get_noise_control_stats(self) -> Dict[str, Any]:
        """Get current noise control statistics."""
        return {
            "active_dedup_keys": len(self._dedup_cache),
            "active_rate_buckets": len(self._rate_limit_buckets),
            "dedup_cache_size": len(self._dedup_cache),
            "rate_bucket_cache_size": len(self._rate_limit_buckets)
        }
