# Start Azure Function Locally and Execute Step by Step
# =====================================================
#
# This script starts the Azure Function locally and then executes
# the step-by-step workflow to demonstrate the complete process.
#
# Usage:
#   .\Start-And-Execute-Function.ps1 -WorkItemId "12345"
#   .\Start-And-Execute-Function.ps1 -WorkItemId "12345" -SkipStart

param(
    [Parameter(Mandatory=$true)]
    [string]$WorkItemId,
    
    [switch]$SkipStart,
    [string]$FunctionPath = "..\functions",
    [int]$WaitSeconds = 30
)

function Write-StepHeader {
    param([string]$Title, [string]$Icon = "🔄")
    
    Write-Host ""
    Write-Host "$Icon $Title" -ForegroundColor Cyan
    Write-Host ("-" * 60) -ForegroundColor Gray
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Start-AzureFunction {
    param([string]$FunctionPath)
    
    Write-StepHeader "STARTING AZURE FUNCTION LOCALLY" "🚀"
    
    # Check if func command is available
    try {
        $funcVersion = func --version
        Write-Success "Azure Functions Core Tools found: $funcVersion"
    }
    catch {
        Write-Error "Azure Functions Core Tools not found!"
        Write-Info "Please install: npm install -g azure-functions-core-tools@4 --unsafe-perm true"
        return $false
    }
    
    # Navigate to functions directory
    $originalLocation = Get-Location
    
    try {
        if (Test-Path $FunctionPath) {
            Set-Location $FunctionPath
            Write-Info "Changed to functions directory: $FunctionPath"
        }
        else {
            Write-Error "Functions directory not found: $FunctionPath"
            return $false
        }
        
        # Check if host.json exists
        if (-not (Test-Path "host.json")) {
            Write-Error "host.json not found in functions directory"
            return $false
        }
        
        Write-Info "Starting Azure Function host..."
        Write-Info "This will start the function on http://localhost:7071"
        Write-Host ""
        
        # Start the function host in background
        $functionProcess = Start-Process -FilePath "func" -ArgumentList "start", "--python" -PassThru -WindowStyle Normal
        
        if ($functionProcess) {
            Write-Success "Function host started with PID: $($functionProcess.Id)"
            Write-Info "Waiting $WaitSeconds seconds for function to initialize..."
            
            # Wait for function to start
            Start-Sleep -Seconds $WaitSeconds
            
            # Test if function is responding
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:7071/api/step_by_step_workflow" -Method GET -TimeoutSec 5 -ErrorAction SilentlyContinue
                Write-Success "Function is responding on http://localhost:7071"
                return $functionProcess
            }
            catch {
                Write-Info "Function may still be starting up..."
                return $functionProcess
            }
        }
        else {
            Write-Error "Failed to start function host"
            return $false
        }
    }
    finally {
        Set-Location $originalLocation
    }
}

function Test-FunctionEndpoint {
    Write-StepHeader "TESTING FUNCTION ENDPOINT" "🔍"
    
    $maxAttempts = 6
    $attempt = 1
    
    while ($attempt -le $maxAttempts) {
        Write-Info "Attempt $attempt/$maxAttempts - Testing function endpoint..."
        
        try {
            $testPayload = @{
                work_item_id = "test-connection"
            } | ConvertTo-Json
            
            $response = Invoke-RestMethod -Uri "http://localhost:7071/api/step_by_step_workflow" -Method POST -Body $testPayload -ContentType "application/json" -TimeoutSec 10
            
            Write-Success "Function endpoint is responding!"
            return $true
        }
        catch {
            Write-Info "Function not ready yet... waiting 10 seconds"
            Start-Sleep -Seconds 10
            $attempt++
        }
    }
    
    Write-Error "Function endpoint not responding after $maxAttempts attempts"
    return $false
}

function Invoke-StepByStepWorkflow {
    param([string]$WorkItemId)
    
    Write-StepHeader "EXECUTING STEP-BY-STEP WORKFLOW" "⚡"
    
    Write-Info "Calling step-by-step workflow for Work Item: $WorkItemId"
    Write-Info "Function URL: http://localhost:7071/api/step_by_step_workflow"
    Write-Host ""
    
    $payload = @{
        work_item_id = $WorkItemId
    } | ConvertTo-Json -Depth 10
    
    Write-Info "Payload:"
    Write-Host $payload -ForegroundColor Gray
    Write-Host ""
    
    try {
        Write-Info "Sending request to Azure Function..."
        $startTime = Get-Date
        
        $response = Invoke-RestMethod -Uri "http://localhost:7071/api/step_by_step_workflow" -Method POST -Body $payload -ContentType "application/json" -TimeoutSec 300
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        Write-Success "Function call completed in $($duration.ToString('F2')) seconds"
        Write-Host ""
        
        Show-WorkflowResults -Result $response
        
        return $response
    }
    catch {
        Write-Error "Function call failed: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Error "HTTP Status: $statusCode"
        }
        
        return $null
    }
}

function Show-WorkflowResults {
    param([object]$Result)
    
    Write-StepHeader "WORKFLOW EXECUTION RESULTS" "📊"
    
    # Basic Information
    Write-Host "📋 Basic Information:" -ForegroundColor Yellow
    Write-Host "   Work Item ID: $($Result.work_item_id)"
    Write-Host "   Success: $(if ($Result.success) { '✅ YES' } else { '❌ NO' })"
    Write-Host "   Processing Mode: $($Result.processing_mode)"
    Write-Host "   Total Time: $($Result.total_time) seconds"
    Write-Host ""
    
    # Step-by-step execution
    if ($Result.step_timings) {
        Write-Host "⏱️  Step-by-Step Execution:" -ForegroundColor Yellow
        
        $stepNames = @{
            "step_1_work_item_creation" = "🔍 Step 1: Work Item Creation/Retrieval"
            "step_2_historical_analysis" = "📊 Step 2: Historical Analysis & Pattern Recognition"
            "step_3_ai_triage_and_message_generation" = "🤖 Step 3: AI Triage & Message Generation"
            "step_4_email_notification" = "📧 Step 4: Email Notification Delivery"
            "step_5_teams_message" = "💬 Step 5: Teams Message Broadcasting"
        }
        
        $Result.step_timings.PSObject.Properties | ForEach-Object {
            $stepName = $stepNames[$_.Name]
            if ($stepName) {
                Write-Host "   $stepName`: $($_.Value)s"
            }
        }
        Write-Host ""
    }
    
    # Workflow steps
    if ($Result.workflow_steps) {
        Write-Host "📋 Workflow Steps Executed:" -ForegroundColor Yellow
        $Result.workflow_steps.PSObject.Properties | ForEach-Object {
            Write-Host "   $($_.Name): $($_.Value)"
        }
        Write-Host ""
    }
    
    # Notifications
    Write-Host "📬 Notification Delivery:" -ForegroundColor Yellow
    Write-Host "   📧 Email Sent: $(if ($Result.email_sent) { '✅ YES' } else { '❌ NO' })"
    Write-Host "   💬 Teams Sent: $(if ($Result.teams_sent) { '✅ YES' } else { '❌ NO' })"
    Write-Host ""
    
    # AI Triage Results
    if ($Result.triage_result) {
        Write-Host "🤖 AI Triage Results:" -ForegroundColor Yellow
        Write-Host "   👤 Assigned To: $($Result.triage_result.assigned_to)"
        Write-Host "   📊 Priority: $($Result.triage_result.priority)"
        Write-Host "   🎯 Confidence: $($Result.triage_result.confidence_score)"
        Write-Host "   🔍 Duplicates Found: $($Result.triage_result.duplicates.Count)"
        
        if ($Result.triage_result.reasoning) {
            $reasoning = $Result.triage_result.reasoning
            if ($reasoning.Length -gt 100) {
                $reasoning = $reasoning.Substring(0, 100) + "..."
            }
            Write-Host "   💭 Reasoning: $reasoning"
        }
        Write-Host ""
    }
    
    # Errors
    if ($Result.errors -and $Result.errors.Count -gt 0) {
        Write-Host "❌ Errors Encountered:" -ForegroundColor Red
        $Result.errors | ForEach-Object { Write-Host "   • $_" }
        Write-Host ""
    }
    
    # Final status
    if ($Result.success) {
        Write-Host "🎉 WORKFLOW COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "   ✅ All steps executed" -ForegroundColor Green
        Write-Host "   📧 Email notification delivered" -ForegroundColor Green
        Write-Host "   💬 Teams message sent" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  WORKFLOW COMPLETED WITH ISSUES" -ForegroundColor Yellow
        Write-Host "   ❌ Some steps failed" -ForegroundColor Red
        Write-Host "   📧 Email: $(if ($Result.email_sent) { '✅' } else { '❌' })"
        Write-Host "   💬 Teams: $(if ($Result.teams_sent) { '✅' } else { '❌' })"
    }
}

# Main execution
Write-Host "🚀 Azure Function Step-by-Step Execution" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray
Write-Host ""

$functionProcess = $null

try {
    if (-not $SkipStart) {
        # Start the Azure Function locally
        $functionProcess = Start-AzureFunction -FunctionPath $FunctionPath
        
        if (-not $functionProcess) {
            Write-Error "Failed to start Azure Function"
            exit 1
        }
        
        # Test the function endpoint
        $endpointReady = Test-FunctionEndpoint
        
        if (-not $endpointReady) {
            Write-Error "Function endpoint not ready"
            exit 1
        }
    }
    else {
        Write-Info "Skipping function start - assuming function is already running"
    }
    
    # Execute the step-by-step workflow
    $result = Invoke-StepByStepWorkflow -WorkItemId $WorkItemId
    
    if ($result) {
        Write-Host ""
        Write-Host "✨ Execution completed successfully!" -ForegroundColor Green
        
        if ($result.success) {
            Write-Host "🎯 Result: SUCCESS" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  Result: PARTIAL SUCCESS" -ForegroundColor Yellow
        }
    }
    else {
        Write-Error "Execution failed"
    }
}
finally {
    # Clean up - stop the function process if we started it
    if ($functionProcess -and -not $SkipStart) {
        Write-Host ""
        Write-Info "Stopping Azure Function process..."
        try {
            Stop-Process -Id $functionProcess.Id -Force
            Write-Success "Function process stopped"
        }
        catch {
            Write-Error "Failed to stop function process: $($_.Exception.Message)"
        }
    }
}

Write-Host ""
Write-Host "🏁 Script completed!" -ForegroundColor Cyan
