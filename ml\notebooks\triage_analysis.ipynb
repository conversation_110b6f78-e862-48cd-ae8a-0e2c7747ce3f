{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AI Triage Analysis Notebook\n", "\n", "This notebook provides analysis and experimentation for the AI-powered defect triage system.\n", "\n", "## Contents\n", "1. Data Loading and Exploration\n", "2. Assignment Accuracy Analysis\n", "3. Duplicate Detection Performance\n", "4. Priority Prediction Analysis\n", "5. <PERSON> Comp<PERSON>on and Tuning"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import json\n", "import asyncio\n", "import sys\n", "import os\n", "\n", "# Add the functions directory to the path\n", "sys.path.append('../..')\n", "\n", "# Set up plotting\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "%matplotlib inline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Exploration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load evaluation results\n", "def load_latest_results(result_type):\n", "    \"\"\"Load the latest evaluation results of a given type.\"\"\"\n", "    import glob\n", "    \n", "    pattern = f'../eval/results/{result_type}_results_*.csv'\n", "    files = glob.glob(pattern)\n", "    \n", "    if not files:\n", "        print(f\"No {result_type} results found\")\n", "        return None\n", "    \n", "    # Get the latest file\n", "    latest_file = max(files, key=os.path.getctime)\n", "    print(f\"Loading {latest_file}\")\n", "    \n", "    return pd.read_csv(latest_file)\n", "\n", "# Load assignment and duplicate detection results\n", "assignment_results = load_latest_results('assignment')\n", "duplicate_results = load_latest_results('duplicate')\n", "\n", "# Display basic info\n", "if assignment_results is not None:\n", "    print(f\"Assignment results shape: {assignment_results.shape}\")\n", "    print(assignment_results.head())\n", "\n", "if duplicate_results is not None:\n", "    print(f\"\\nDuplicate results shape: {duplicate_results.shape}\")\n", "    print(duplicate_results.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Assignment Accuracy Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if assignment_results is not None:\n", "    # Calculate overall accuracy\n", "    overall_accuracy = assignment_results['correct'].mean()\n", "    coverage = assignment_results['predicted_assignee'].notna().mean()\n", "    \n", "    print(f\"Overall Assignment Accuracy: {overall_accuracy:.2%}\")\n", "    print(f\"Coverage (predictions made): {coverage:.2%}\")\n", "    \n", "    # Accuracy by confidence level\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    # Confidence distribution\n", "    ax1.hist(assignment_results['confidence'], bins=20, alpha=0.7, edgecolor='black')\n", "    ax1.set_xlabel('Confidence Score')\n", "    ax1.set_ylabel('Frequency')\n", "    ax1.set_title('Distribution of Assignment Confidence Scores')\n", "    \n", "    # Accuracy vs Confidence\n", "    confidence_bins = pd.cut(assignment_results['confidence'], bins=10)\n", "    accuracy_by_confidence = assignment_results.groupby(confidence_bins)['correct'].mean()\n", "    \n", "    ax2.plot(range(len(accuracy_by_confidence)), accuracy_by_confidence.values, marker='o')\n", "    ax2.set_xlabel('Confidence Bin')\n", "    ax2.set_ylabel('Accuracy')\n", "    ax2.set_title('Accuracy vs Confidence Level')\n", "    ax2.set_xticklabels([f'{i:.1f}' for i in accuracy_by_confidence.index], rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Top assignees by frequency\n", "    assignee_stats = assignment_results.groupby('actual_assignee').agg({\n", "        'correct': ['count', 'mean'],\n", "        'confidence': 'mean'\n", "    }).round(3)\n", "    \n", "    assignee_stats.columns = ['Total_Items', 'Accuracy', 'Avg_Confidence']\n", "    assignee_stats = assignee_stats.sort_values('Total_Items', ascending=False)\n", "    \n", "    print(\"\\nTop 10 Assignees by Volume:\")\n", "    print(assignee_stats.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Duplicate Detection Performance"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if duplicate_results is not None:\n", "    # Overall metrics\n", "    avg_precision = duplicate_results['precision'].mean()\n", "    avg_recall = duplicate_results['recall'].mean()\n", "    avg_f1 = duplicate_results['f1_score'].mean()\n", "    \n", "    print(f\"Average Precision: {avg_precision:.3f}\")\n", "    print(f\"Average Recall: {avg_recall:.3f}\")\n", "    print(f\"Average F1 Score: {avg_f1:.3f}\")\n", "    \n", "    # Distribution of metrics\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # Precision distribution\n", "    axes[0, 0].hist(duplicate_results['precision'], bins=20, alpha=0.7, edgecolor='black')\n", "    axes[0, 0].set_xlabel('Precision')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].set_title('Distribution of Precision Scores')\n", "    \n", "    # Recall distribution\n", "    axes[0, 1].hist(duplicate_results['recall'], bins=20, alpha=0.7, edgecolor='black')\n", "    axes[0, 1].set_xlabel('Recall')\n", "    axes[0, 1].set_ylabel('Frequency')\n", "    axes[0, 1].set_title('Distribution of Recall Scores')\n", "    \n", "    # F1 Score distribution\n", "    axes[1, 0].hist(duplicate_results['f1_score'], bins=20, alpha=0.7, edgecolor='black')\n", "    axes[1, 0].set_xlabel('F1 Score')\n", "    axes[1, 0].set_ylabel('Frequency')\n", "    axes[1, 0].set_title('Distribution of F1 Scores')\n", "    \n", "    # Precision vs Recall scatter\n", "    axes[1, 1].scatter(duplicate_results['recall'], duplicate_results['precision'], alpha=0.6)\n", "    axes[1, 1].set_xlabel('Recall')\n", "    axes[1, 1].set_ylabel('Precision')\n", "    axes[1, 1].set_title('Precision vs Recall')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Analysis by number of true duplicates\n", "    duplicate_count_analysis = duplicate_results.groupby('true_duplicates_count').agg({\n", "        'precision': 'mean',\n", "        'recall': 'mean',\n", "        'f1_score': 'mean',\n", "        'work_item_id': 'count'\n", "    }).round(3)\n", "    \n", "    duplicate_count_analysis.columns = ['Avg_Precision', 'Avg_Recall', 'Avg_F1', 'Count']\n", "    \n", "    print(\"\\nPerformance by Number of True Duplicates:\")\n", "    print(duplicate_count_analysis)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Time Series Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load metrics over time (if multiple evaluation runs exist)\n", "def load_metrics_over_time(metric_type):\n", "    \"\"\"Load metrics from multiple evaluation runs.\"\"\"\n", "    import glob\n", "    \n", "    pattern = f'../eval/results/{metric_type}_metrics_*.json'\n", "    files = glob.glob(pattern)\n", "    \n", "    if not files:\n", "        print(f\"No {metric_type} metrics found\")\n", "        return None\n", "    \n", "    metrics_data = []\n", "    for file in files:\n", "        with open(file, 'r') as f:\n", "            data = json.load(f)\n", "            data['file'] = file\n", "            metrics_data.append(data)\n", "    \n", "    return metrics_data\n", "\n", "# Load assignment metrics over time\n", "assignment_metrics = load_metrics_over_time('assignment')\n", "duplicate_metrics = load_metrics_over_time('duplicate')\n", "\n", "if assignment_metrics and len(assignment_metrics) > 1:\n", "    # Create time series plot\n", "    dates = [datetime.fromisoformat(m['evaluation_date']) for m in assignment_metrics]\n", "    accuracies = [m['metrics']['accuracy'] for m in assignment_metrics]\n", "    coverages = [m['metrics']['coverage'] for m in assignment_metrics]\n", "    \n", "    plt.figure(figsize=(12, 6))\n", "    plt.plot(dates, accuracies, marker='o', label='Accuracy')\n", "    plt.plot(dates, coverages, marker='s', label='Coverage')\n", "    plt.xlabel('Evaluation Date')\n", "    plt.ylabel('Score')\n", "    plt.title('Assignment Performance Over Time')\n", "    plt.legend()\n", "    plt.xticks(rotation=45)\n", "    plt.tight_layout()\n", "    plt.show()\n", "else:\n", "    print(\"Not enough historical data for time series analysis\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Comparison and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary of current performance\n", "print(\"=== AI Triage System Performance Summary ===\")\n", "print()\n", "\n", "if assignment_results is not None:\n", "    print(\"Assignment Performance:\")\n", "    print(f\"  • Overall Accuracy: {assignment_results['correct'].mean():.2%}\")\n", "    print(f\"  • Coverage: {assignment_results['predicted_assignee'].notna().mean():.2%}\")\n", "    print(f\"  • Average Confidence: {assignment_results['confidence'].mean():.2f}\")\n", "    \n", "    # High confidence accuracy\n", "    high_conf = assignment_results[assignment_results['confidence'] >= 0.8]\n", "    if len(high_conf) > 0:\n", "        print(f\"  • High Confidence (≥0.8) Accuracy: {high_conf['correct'].mean():.2%}\")\n", "    print()\n", "\n", "if duplicate_results is not None:\n", "    print(\"Duplicate Detection Performance:\")\n", "    print(f\"  • Average Precision: {duplicate_results['precision'].mean():.3f}\")\n", "    print(f\"  • Average Recall: {duplicate_results['recall'].mean():.3f}\")\n", "    print(f\"  • Average F1 Score: {duplicate_results['f1_score'].mean():.3f}\")\n", "    print()\n", "\n", "print(\"Recommendations for Improvement:\")\n", "print(\"  1. Increase training data for underperforming assignees\")\n", "print(\"  2. Tune similarity thresholds for duplicate detection\")\n", "print(\"  3. Implement feedback loop for continuous learning\")\n", "print(\"  4. Add more sophisticated feature engineering\")\n", "print(\"  5. Consider ensemble methods for assignment prediction\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Interactive Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive widgets for exploring results\n", "try:\n", "    from ipywidgets import interact, widgets\n", "    \n", "    if assignment_results is not None:\n", "        @interact(confidence_threshold=widgets.FloatSlider(min=0.0, max=1.0, step=0.1, value=0.5))\n", "        def analyze_by_confidence(confidence_threshold):\n", "            filtered_results = assignment_results[assignment_results['confidence'] >= confidence_threshold]\n", "            \n", "            if len(filtered_results) > 0:\n", "                accuracy = filtered_results['correct'].mean()\n", "                coverage = len(filtered_results) / len(assignment_results)\n", "                \n", "                print(f\"Results for confidence ≥ {confidence_threshold:.1f}:\")\n", "                print(f\"  Accuracy: {accuracy:.2%}\")\n", "                print(f\"  Coverage: {coverage:.2%}\")\n", "                print(f\"  Items: {len(filtered_results)}/{len(assignment_results)}\")\n", "            else:\n", "                print(f\"No results found for confidence ≥ {confidence_threshold:.1f}\")\n", "                \n", "except ImportError:\n", "    print(\"Install ipywidgets for interactive analysis: pip install ipywidgets\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}