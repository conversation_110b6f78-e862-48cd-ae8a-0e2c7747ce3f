-- Azure SQL Database Vector Functions
-- These functions provide vector similarity operations for Azure SQL Database

-- Cosine Similarity Function
-- Calculates cosine similarity between two JSON arrays representing vectors
CREATE OR ALTER FUNCTION dbo.cosine_similarity(@vector1 NVARCHAR(MAX), @vector2 NVARCHAR(MAX))
RETURNS FLOAT
AS
BEGIN
    DECLARE @result FLOAT = 0;
    DECLARE @magnitude1 FLOAT = 0;
    DECLARE @magnitude2 FLOAT = 0;
    DECLARE @dotProduct FLOAT = 0;
    DECLARE @i INT = 0;
    DECLARE @len INT;
    DECLARE @val1 FLOAT;
    DECLARE @val2 FLOAT;
    
    -- Parse JSON arrays
    DECLARE @array1 TABLE (idx INT, value FLOAT);
    DECLARE @array2 TABLE (idx INT, value FLOAT);
    
    INSERT INTO @array1 (idx, value)
    SELECT [key], CAST([value] AS FLOAT)
    FROM OPENJSON(@vector1);
    
    INSERT INTO @array2 (idx, value)
    SELECT [key], CAST([value] AS FLOAT)
    FROM OPENJSON(@vector2);
    
    -- Get vector length
    SELECT @len = COUNT(*) FROM @array1;
    
    -- Calculate dot product and magnitudes
    WHILE @i < @len
    BEGIN
        SELECT @val1 = value FROM @array1 WHERE idx = @i;
        SELECT @val2 = value FROM @array2 WHERE idx = @i;
        
        SET @dotProduct = @dotProduct + (@val1 * @val2);
        SET @magnitude1 = @magnitude1 + (@val1 * @val1);
        SET @magnitude2 = @magnitude2 + (@val2 * @val2);
        
        SET @i = @i + 1;
    END
    
    -- Calculate cosine similarity
    IF @magnitude1 > 0 AND @magnitude2 > 0
        SET @result = @dotProduct / (SQRT(@magnitude1) * SQRT(@magnitude2));
    
    RETURN @result;
END;
GO

-- Euclidean Distance Function
CREATE OR ALTER FUNCTION dbo.euclidean_distance(@vector1 NVARCHAR(MAX), @vector2 NVARCHAR(MAX))
RETURNS FLOAT
AS
BEGIN
    DECLARE @result FLOAT = 0;
    DECLARE @i INT = 0;
    DECLARE @len INT;
    DECLARE @val1 FLOAT;
    DECLARE @val2 FLOAT;
    DECLARE @diff FLOAT;
    
    -- Parse JSON arrays
    DECLARE @array1 TABLE (idx INT, value FLOAT);
    DECLARE @array2 TABLE (idx INT, value FLOAT);
    
    INSERT INTO @array1 (idx, value)
    SELECT [key], CAST([value] AS FLOAT)
    FROM OPENJSON(@vector1);
    
    INSERT INTO @array2 (idx, value)
    SELECT [key], CAST([value] AS FLOAT)
    FROM OPENJSON(@vector2);
    
    -- Get vector length
    SELECT @len = COUNT(*) FROM @array1;
    
    -- Calculate squared differences
    WHILE @i < @len
    BEGIN
        SELECT @val1 = value FROM @array1 WHERE idx = @i;
        SELECT @val2 = value FROM @array2 WHERE idx = @i;
        
        SET @diff = @val1 - @val2;
        SET @result = @result + (@diff * @diff);
        
        SET @i = @i + 1;
    END
    
    RETURN SQRT(@result);
END;
GO

-- Vector Magnitude Function
CREATE OR ALTER FUNCTION dbo.vector_magnitude(@vector NVARCHAR(MAX))
RETURNS FLOAT
AS
BEGIN
    DECLARE @result FLOAT = 0;
    DECLARE @val FLOAT;
    
    -- Parse JSON array and calculate magnitude
    SELECT @result = SQRT(SUM(CAST([value] AS FLOAT) * CAST([value] AS FLOAT)))
    FROM OPENJSON(@vector);
    
    RETURN @result;
END;
GO

-- Normalize Vector Function
CREATE OR ALTER FUNCTION dbo.normalize_vector(@vector NVARCHAR(MAX))
RETURNS NVARCHAR(MAX)
AS
BEGIN
    DECLARE @magnitude FLOAT;
    DECLARE @result NVARCHAR(MAX);
    
    -- Get magnitude
    SELECT @magnitude = dbo.vector_magnitude(@vector);
    
    -- Normalize if magnitude > 0
    IF @magnitude > 0
    BEGIN
        SELECT @result = '[' + STRING_AGG(CAST(CAST([value] AS FLOAT) / @magnitude AS VARCHAR(20)), ',') + ']'
        FROM OPENJSON(@vector);
    END
    ELSE
    BEGIN
        SET @result = @vector;
    END
    
    RETURN @result;
END;
GO

-- Example usage queries:

-- Find similar work items using cosine similarity
/*
SELECT TOP 10 
    w1.work_item_id,
    w1.title,
    dbo.cosine_similarity(w1.content_vector, @query_vector) AS similarity_score
FROM work_items_vectors w1
WHERE w1.work_item_id != @target_work_item_id
ORDER BY similarity_score DESC;
*/

-- Find work items within a certain distance threshold
/*
SELECT 
    work_item_id,
    title,
    dbo.euclidean_distance(content_vector, @query_vector) AS distance
FROM work_items_vectors
WHERE dbo.euclidean_distance(content_vector, @query_vector) < @threshold
ORDER BY distance ASC;
*/

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_work_items_vectors_type_state 
ON work_items_vectors(work_item_type, state);

CREATE INDEX IF NOT EXISTS idx_work_items_vectors_assigned_to 
ON work_items_vectors(assigned_to) 
WHERE assigned_to IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_work_items_vectors_created_date 
ON work_items_vectors(created_date);

-- Performance optimization: Create computed columns for common filters
ALTER TABLE work_items_vectors 
ADD is_open AS CASE 
    WHEN state IN ('New', 'Active', 'In Progress') THEN 1 
    ELSE 0 
END;

CREATE INDEX idx_work_items_vectors_is_open 
ON work_items_vectors(is_open);
