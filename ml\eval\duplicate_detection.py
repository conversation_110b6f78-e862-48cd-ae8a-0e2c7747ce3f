"""
Duplicate detection evaluation script.
Measures precision and recall of duplicate detection.
"""

import asyncio
import json
import logging
from typing import Dict, List, Tuple, Set, Optional
from datetime import datetime, timedelta
import pandas as pd
from dataclasses import dataclass

from functions.__app__.common.models.schemas import WorkItem, DuplicateHit
from functions.__app__.common.ai.duplicate import DuplicateDetector
from functions.__app__.common.adapters.ado_client import AdoClient
from functions.__app__.common.adapters.search_client import SearchClient
from functions.__app__.common.utils.config import get_config
from functions.__app__.common.utils.logging import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@dataclass
class DuplicateEvaluation:
    """Results of duplicate detection evaluation."""
    work_item_id: int
    true_duplicates: Set[int]
    predicted_duplicates: Set[int]
    precision: float
    recall: float
    f1_score: float


class DuplicateDetectionEvaluator:
    """Evaluates the accuracy of duplicate detection."""
    
    def __init__(self):
        self.config = get_config()
        self.ado_client = AdoClient(self.config)
        self.search_client = SearchClient(self.config)
        self.duplicate_detector = DuplicateDetector(self.search_client, self.config)
    
    async def evaluate_duplicate_detection(
        self,
        start_date: datetime,
        end_date: datetime,
        sample_size: Optional[int] = None
    ) -> Dict[str, float]:
        """
        Evaluate duplicate detection accuracy over a date range.
        
        Args:
            start_date: Start date for evaluation
            end_date: End date for evaluation
            sample_size: Optional limit on number of items to evaluate
        
        Returns:
            Dictionary with precision, recall, and F1 metrics
        """
        try:
            logger.info(f"Starting duplicate detection evaluation from {start_date} to {end_date}")
            
            # Fetch work items for evaluation
            work_items = await self._fetch_evaluation_work_items(start_date, end_date, sample_size)
            logger.info(f"Fetched {len(work_items)} work items for evaluation")
            
            if not work_items:
                logger.warning("No work items found for evaluation")
                return {}
            
            # Build ground truth duplicate relationships
            ground_truth = await self._build_ground_truth_duplicates(work_items)
            logger.info(f"Built ground truth for {len(ground_truth)} work items")
            
            # Evaluate each work item
            evaluations = []
            for i, work_item in enumerate(work_items):
                if i % 10 == 0:
                    logger.info(f"Evaluating work item {i+1}/{len(work_items)}")
                
                if work_item.id in ground_truth:
                    evaluation = await self._evaluate_single_duplicate_detection(
                        work_item, 
                        ground_truth[work_item.id]
                    )
                    if evaluation:
                        evaluations.append(evaluation)
            
            # Calculate metrics
            metrics = self._calculate_duplicate_metrics(evaluations)
            
            # Save results
            await self._save_evaluation_results(evaluations, metrics, start_date, end_date)
            
            logger.info(f"Duplicate detection evaluation completed. F1 Score: {metrics.get('f1_score', 0):.3f}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error in duplicate detection evaluation: {e}")
            raise
    
    async def _fetch_evaluation_work_items(
        self,
        start_date: datetime,
        end_date: datetime,
        sample_size: Optional[int]
    ) -> List[WorkItem]:
        """Fetch work items for evaluation."""
        try:
            # Build WIQL query for work items
            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
                   [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
                   [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
                   [Microsoft.VSTS.TCM.ReproSteps], [Microsoft.VSTS.TCM.SystemInfo]
            FROM WorkItems
            WHERE [System.CreatedDate] >= '{start_date.isoformat()}'
              AND [System.CreatedDate] <= '{end_date.isoformat()}'
              AND [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Feature')
              AND [System.State] <> 'Removed'
            ORDER BY [System.CreatedDate] DESC
            """
            
            work_items_data = await self.ado_client.query_work_items(wiql_query)
            
            # Convert to WorkItem models
            work_items = []
            for item_data in work_items_data:
                try:
                    fields = item_data.get('fields', {})
                    work_item = WorkItem(
                        id=item_data.get('id'),
                        title=fields.get('System.Title', ''),
                        description=fields.get('System.Description', ''),
                        work_item_type=fields.get('System.WorkItemType', ''),
                        state=fields.get('System.State', ''),
                        area_path=fields.get('System.AreaPath', ''),
                        assigned_to=fields.get('System.AssignedTo', {}).get('displayName', ''),
                        created_by=fields.get('System.CreatedBy', {}).get('displayName', ''),
                        created_date=fields.get('System.CreatedDate', ''),
                        changed_date=fields.get('System.ChangedDate', ''),
                        priority=fields.get('Microsoft.VSTS.Common.Priority', 2),
                        tags=fields.get('System.Tags', ''),
                        repro_steps=fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
                        system_info=fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
                    )
                    work_items.append(work_item)
                except Exception as e:
                    logger.warning(f"Failed to parse work item {item_data.get('id')}: {e}")
                    continue
            
            # Apply sample size limit
            if sample_size and len(work_items) > sample_size:
                work_items = work_items[:sample_size]
            
            return work_items
            
        except Exception as e:
            logger.error(f"Error fetching evaluation work items: {e}")
            raise
    
    async def _build_ground_truth_duplicates(self, work_items: List[WorkItem]) -> Dict[int, Set[int]]:
        """
        Build ground truth duplicate relationships.
        
        In a real implementation, this would use:
        1. Work items marked as duplicates in ADO
        2. Manual annotations
        3. Expert-labeled datasets
        
        For this example, we'll use a simple heuristic based on title similarity.
        """
        ground_truth = {}
        
        # Simple heuristic: items with very similar titles are duplicates
        for i, item1 in enumerate(work_items):
            duplicates = set()
            
            for j, item2 in enumerate(work_items):
                if i != j and item1.work_item_type == item2.work_item_type:
                    # Calculate title similarity
                    similarity = self._calculate_title_similarity(item1.title, item2.title)
                    
                    # If very similar (>90%), consider as duplicate
                    if similarity > 0.9:
                        duplicates.add(item2.id)
            
            if duplicates:
                ground_truth[item1.id] = duplicates
        
        return ground_truth
    
    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate similarity between two titles."""
        from difflib import SequenceMatcher
        
        if not title1 or not title2:
            return 0.0
        
        # Normalize titles
        norm1 = title1.lower().strip()
        norm2 = title2.lower().strip()
        
        # Calculate sequence similarity
        matcher = SequenceMatcher(None, norm1, norm2)
        return matcher.ratio()
    
    async def _evaluate_single_duplicate_detection(
        self,
        work_item: WorkItem,
        true_duplicates: Set[int]
    ) -> Optional[DuplicateEvaluation]:
        """Evaluate duplicate detection for a single work item."""
        try:
            # Get duplicate predictions
            predicted_duplicates_list = await self.duplicate_detector.find_duplicates(work_item)
            predicted_duplicates = {dup.work_item_id for dup in predicted_duplicates_list}
            
            # Calculate precision, recall, and F1
            if predicted_duplicates:
                true_positives = len(true_duplicates.intersection(predicted_duplicates))
                precision = true_positives / len(predicted_duplicates)
            else:
                precision = 1.0 if not true_duplicates else 0.0
            
            if true_duplicates:
                true_positives = len(true_duplicates.intersection(predicted_duplicates))
                recall = true_positives / len(true_duplicates)
            else:
                recall = 1.0 if not predicted_duplicates else 0.0
            
            # F1 score
            if precision + recall > 0:
                f1_score = 2 * (precision * recall) / (precision + recall)
            else:
                f1_score = 0.0
            
            return DuplicateEvaluation(
                work_item_id=work_item.id,
                true_duplicates=true_duplicates,
                predicted_duplicates=predicted_duplicates,
                precision=precision,
                recall=recall,
                f1_score=f1_score
            )
            
        except Exception as e:
            logger.warning(f"Error evaluating duplicates for work item {work_item.id}: {e}")
            return None
    
    def _calculate_duplicate_metrics(self, evaluations: List[DuplicateEvaluation]) -> Dict[str, float]:
        """Calculate overall duplicate detection metrics."""
        if not evaluations:
            return {}
        
        # Macro-averaged metrics (average across work items)
        macro_precision = sum(eval.precision for eval in evaluations) / len(evaluations)
        macro_recall = sum(eval.recall for eval in evaluations) / len(evaluations)
        macro_f1 = sum(eval.f1_score for eval in evaluations) / len(evaluations)
        
        # Micro-averaged metrics (aggregate then calculate)
        total_true_positives = 0
        total_predicted = 0
        total_actual = 0
        
        for eval in evaluations:
            true_positives = len(eval.true_duplicates.intersection(eval.predicted_duplicates))
            total_true_positives += true_positives
            total_predicted += len(eval.predicted_duplicates)
            total_actual += len(eval.true_duplicates)
        
        micro_precision = total_true_positives / total_predicted if total_predicted > 0 else 0.0
        micro_recall = total_true_positives / total_actual if total_actual > 0 else 0.0
        
        if micro_precision + micro_recall > 0:
            micro_f1 = 2 * (micro_precision * micro_recall) / (micro_precision + micro_recall)
        else:
            micro_f1 = 0.0
        
        # Additional metrics
        items_with_duplicates = sum(1 for eval in evaluations if eval.true_duplicates)
        items_with_predictions = sum(1 for eval in evaluations if eval.predicted_duplicates)
        
        metrics = {
            'total_items': len(evaluations),
            'items_with_true_duplicates': items_with_duplicates,
            'items_with_predictions': items_with_predictions,
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_f1_score': macro_f1,
            'micro_precision': micro_precision,
            'micro_recall': micro_recall,
            'micro_f1_score': micro_f1,
            'avg_true_duplicates_per_item': total_actual / len(evaluations),
            'avg_predicted_duplicates_per_item': total_predicted / len(evaluations)
        }
        
        return metrics
    
    async def _save_evaluation_results(
        self,
        evaluations: List[DuplicateEvaluation],
        metrics: Dict[str, float],
        start_date: datetime,
        end_date: datetime
    ) -> None:
        """Save evaluation results to files."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Save detailed results
            results_data = []
            for eval in evaluations:
                results_data.append({
                    'work_item_id': eval.work_item_id,
                    'true_duplicates_count': len(eval.true_duplicates),
                    'predicted_duplicates_count': len(eval.predicted_duplicates),
                    'true_duplicates': list(eval.true_duplicates),
                    'predicted_duplicates': list(eval.predicted_duplicates),
                    'precision': eval.precision,
                    'recall': eval.recall,
                    'f1_score': eval.f1_score
                })
            
            results_df = pd.DataFrame(results_data)
            results_file = f'ml/eval/results/duplicate_results_{timestamp}.csv'
            results_df.to_csv(results_file, index=False)
            
            # Save metrics summary
            metrics_data = {
                'evaluation_date': datetime.now().isoformat(),
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'metrics': metrics
            }
            
            metrics_file = f'ml/eval/results/duplicate_metrics_{timestamp}.json'
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
            
            logger.info(f"Evaluation results saved to {results_file} and {metrics_file}")
            
        except Exception as e:
            logger.error(f"Error saving evaluation results: {e}")


async def main():
    """Main function for running duplicate detection evaluation."""
    evaluator = DuplicateDetectionEvaluator()
    
    # Evaluate last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    metrics = await evaluator.evaluate_duplicate_detection(
        start_date=start_date,
        end_date=end_date,
        sample_size=50  # Limit for testing
    )
    
    print("Duplicate Detection Metrics:")
    for metric, value in metrics.items():
        if isinstance(value, float):
            print(f"  {metric}: {value:.3f}")
        else:
            print(f"  {metric}: {value}")


if __name__ == "__main__":
    asyncio.run(main())
