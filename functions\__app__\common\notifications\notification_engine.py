"""
Core Notification Engine for Intelligent Notifications & Reminders.
Orchestrates all notification components for end-to-end processing.
"""

import logging
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:
    from ..models import (
        NotificationContext,
        NotificationTrigger,
        StakeholderRoute,
        NotificationStatus,
        ProjectNotificationConfig
    )
from ..models.schemas import WorkItem
from ..adapters.ado_client import AdoClient
from ..adapters.teams_client import TeamsClient
from ..adapters.pagerduty_client import PagerDutyClient
from ..adapters.search_client import SearchClient
from ..ai.duplicate import DuplicateDetector
from ..ai.assigner import AssignmentEngine
from ..ai.priority import PriorityEngine
from .trigger_engine import TriggerEngine
from .stakeholder_router import StakeholderRouter
from .message_generator import MessageGenerator
from .noise_controller import NoiseController
from .audit_service import NotificationAuditService
from ..utils.config import Config
from ..utils.logging import log_structured, log_performance_metric

logger = logging.getLogger(__name__)


class NotificationEngine:
    """
    Core notification engine that orchestrates the entire notification pipeline.
    
    Pipeline:
    1. Trigger evaluation
    2. Stakeholder routing
    3. Context generation
    4. Noise control
    5. Delivery
    6. Audit and metrics
    """
    
    def __init__(
        self,
        config: Config,
        ado_client: AdoClient,
        teams_client: TeamsClient,
        pagerduty_client: Optional[PagerDutyClient] = None,
        search_client: Optional[SearchClient] = None,
        duplicate_detector: Optional[DuplicateDetector] = None,
        assignment_engine: Optional[AssignmentEngine] = None,
        priority_engine: Optional[PriorityEngine] = None
    ):
        self.config = config
        self.ado_client = ado_client
        self.teams_client = teams_client
        self.pagerduty_client = pagerduty_client
        self.search_client = search_client
        
        # Initialize notification components
        self.trigger_engine = TriggerEngine(config)
        self.stakeholder_router = StakeholderRouter(config, ado_client)
        self.noise_controller = NoiseController(config)
        self.audit_service = NotificationAuditService(config)
        
        # Initialize message generator if AI engines are available
        if all([duplicate_detector, assignment_engine, priority_engine, search_client]):
            self.message_generator = MessageGenerator(
                config, duplicate_detector, assignment_engine, priority_engine, search_client
            )
        else:
            self.message_generator = None
            logger.warning("Message generator not initialized - AI engines not available")
        
        self._project_configs: Dict[str, "ProjectNotificationConfig"] = {}

    def load_project_config(self, project: str, config: "ProjectNotificationConfig") -> None:
        """Load project-specific configuration."""
        self._project_configs[project] = config
        
        # Propagate to components
        self.trigger_engine.load_project_config(project, config)
        self.stakeholder_router.load_project_config(project, config)
        self.noise_controller.load_project_config(project, config)
        
        log_structured(
            logger,
            "info",
            f"Loaded project configuration: {project}",
            extra={"project": project, "enabled": config.enabled}
        )
    
    async def process_work_item_notification(
        self,
        work_item: WorkItem,
        event_type: str = "created"
    ) -> List[str]:
        """
        Process a work item for notifications.
        
        Args:
            work_item: Work item to process
            event_type: Type of event (created, updated, etc.)
        
        Returns:
            List of notification IDs created
        """
        start_time = datetime.utcnow()
        notification_ids = []
        
        try:
            log_structured(
                logger,
                "info",
                f"Processing work item notification: {work_item.id}",
                extra={
                    "work_item_id": work_item.id,
                    "event_type": event_type,
                    "project": work_item.project
                }
            )
            
            # Step 1: Evaluate triggers
            triggers = self.trigger_engine.evaluate_work_item(work_item, event_type)
            
            if not triggers:
                log_structured(
                    logger,
                    "info",
                    f"No triggers activated for work item {work_item.id}",
                    extra={"work_item_id": work_item.id}
                )
                return notification_ids
            
            # Step 2: Process each trigger
            for trigger in triggers:
                trigger_notification_ids = await self._process_trigger(work_item, trigger)
                notification_ids.extend(trigger_notification_ids)
            
            # Log performance metric
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            log_performance_metric(
                logger,
                "work_item_notification_processing_time",
                processing_time,
                "ms",
                {
                    "work_item_id": str(work_item.id),
                    "event_type": event_type,
                    "triggers_count": len(triggers),
                    "notifications_created": len(notification_ids)
                }
            )
            
            return notification_ids
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error processing work item notification: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            return notification_ids
    
    async def process_aging_notifications(self, work_items: List[WorkItem]) -> List[str]:
        """
        Process aging notifications for a batch of work items.
        
        Args:
            work_items: List of work items to check for aging
        
        Returns:
            List of notification IDs created
        """
        start_time = datetime.utcnow()
        notification_ids = []
        
        try:
            log_structured(
                logger,
                "info",
                f"Processing aging notifications for {len(work_items)} work items"
            )
            
            # Evaluate aging triggers in batch
            aging_triggers = self.trigger_engine.evaluate_aging_batch(work_items)
            
            if not aging_triggers:
                log_structured(
                    logger,
                    "info",
                    "No aging triggers found"
                )
                return notification_ids
            
            # Group triggers by work item
            triggers_by_work_item = {}
            for trigger in aging_triggers:
                work_item_id = trigger.work_item_id
                if work_item_id not in triggers_by_work_item:
                    triggers_by_work_item[work_item_id] = []
                triggers_by_work_item[work_item_id].append(trigger)
            
            # Process each work item's triggers
            for work_item in work_items:
                if work_item.id in triggers_by_work_item:
                    for trigger in triggers_by_work_item[work_item.id]:
                        trigger_notification_ids = await self._process_trigger(work_item, trigger)
                        notification_ids.extend(trigger_notification_ids)
            
            # Log performance metric
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            log_performance_metric(
                logger,
                "aging_notifications_processing_time",
                processing_time,
                "ms",
                {
                    "work_items_count": len(work_items),
                    "aging_triggers_count": len(aging_triggers),
                    "notifications_created": len(notification_ids)
                }
            )
            
            return notification_ids
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error processing aging notifications: {e}",
                exc_info=True
            )
            return notification_ids
    
    async def _process_trigger(
        self,
        work_item: WorkItem,
        trigger: "NotificationTrigger"
    ) -> List[str]:
        """Process a single trigger and create notifications."""
        # Import here to avoid circular dependencies
        from ..models import NotificationContext

        notification_ids = []

        try:
            # Step 1: Route to stakeholders
            routes = await self.stakeholder_router.route_notification(work_item, trigger)
            
            if not routes:
                log_structured(
                    logger,
                    "warning",
                    f"No routes found for trigger {trigger.trigger_type}",
                    extra={"work_item_id": work_item.id, "trigger_type": trigger.trigger_type}
                )
                return notification_ids
            
            # Step 2: Generate context (if message generator available)
            if self.message_generator:
                context = await self.message_generator.generate_notification_context(work_item, trigger)
            else:
                # Create minimal context
                context = NotificationContext(
                    work_item=work_item,
                    trigger=trigger,
                    message_content=f"Work item {work_item.id}: {work_item.title}"
                )
            
            # Step 3: Process each route
            for route in routes:
                notification_id = await self._send_notification(context, route)
                if notification_id:
                    notification_ids.append(notification_id)
            
            return notification_ids
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error processing trigger: {e}",
                extra={"work_item_id": work_item.id, "trigger_type": trigger.trigger_type},
                exc_info=True
            )
            return notification_ids
    
    async def _send_notification(
        self,
        context: "NotificationContext",
        route: "StakeholderRoute"
    ) -> Optional[str]:
        """Send a notification through the specified route."""
        # Import here to avoid circular dependencies
        from ..models import NotificationStatus

        try:
            project_config = self._get_project_config(context.work_item.project)
            
            # Step 1: Check noise control
            should_send, status, reason = self.noise_controller.should_send_notification(
                context.trigger, route, project_config
            )
            
            # Step 2: Create audit record
            notification_id = self.audit_service.create_audit_record(context.trigger, route)
            
            if not should_send:
                # Update audit with noise control status
                self.audit_service.update_notification_status(notification_id, status)
                log_structured(
                    logger,
                    "info",
                    f"Notification blocked by noise control: {reason}",
                    extra={
                        "notification_id": notification_id,
                        "work_item_id": context.work_item.id,
                        "reason": reason
                    }
                )
                return notification_id
            
            # Step 3: Send notification based on delivery method
            success = await self._deliver_notification(context, route, notification_id)
            
            # Step 4: Update audit record
            if success:
                self.audit_service.update_notification_status(
                    notification_id, NotificationStatus.SENT
                )
            else:
                self.audit_service.update_notification_status(
                    notification_id, NotificationStatus.FAILED, "Delivery failed"
                )
            
            return notification_id
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending notification: {e}",
                extra={"work_item_id": context.work_item.id},
                exc_info=True
            )
            return None
    
    async def _deliver_notification(
        self,
        context: "NotificationContext",
        route: "StakeholderRoute",
        notification_id: str
    ) -> bool:
        """Deliver notification through the appropriate channel."""
        # Import here to avoid circular dependencies
        from ..models import DeliveryMethod

        try:
            if route.delivery_method.value.startswith("teams"):
                return await self.teams_client.send_notification_card(context, route)
            
            elif route.delivery_method.value.startswith("pagerduty"):
                if self.pagerduty_client:
                    result = await self.pagerduty_client.create_incident(
                        context, route, route.recipient_id
                    )
                    return result is not None
                else:
                    logger.warning("PagerDuty client not available")
                    return False
            
            elif route.delivery_method.value.startswith("email"):
                # Email delivery would be implemented here
                logger.warning("Email delivery not yet implemented")
                return False
            
            else:
                logger.warning(f"Unknown delivery method: {route.delivery_method}")
                return False
                
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error delivering notification: {e}",
                extra={"notification_id": notification_id},
                exc_info=True
            )
            return False
    
    def _get_project_config(self, project: str) -> "ProjectNotificationConfig":
        """Get project configuration or return default."""
        if project in self._project_configs:
            return self._project_configs[project]

        # Return default configuration
        # Import here to avoid circular dependencies
        from ..models import ProjectNotificationConfig
        return ProjectNotificationConfig(project_name=project)
    
    async def cleanup(self) -> None:
        """Cleanup resources and old records."""
        try:
            # Cleanup noise controller
            self.noise_controller.cleanup_expired_entries()
            
            # Cleanup audit records (keep 30 days)
            cleaned_records = self.audit_service.cleanup_old_records(30)
            
            log_structured(
                logger,
                "info",
                f"Cleanup completed: {cleaned_records} old records removed"
            )
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error during cleanup: {e}",
                exc_info=True
            )
