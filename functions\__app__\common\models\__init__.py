"""
Data models and schemas
"""

# Core schemas
from .schemas import (
    WorkItem,
    WorkItemType,
    WorkItemState,
    Priority,
    TriageResult,
    SearchResult,
    DuplicateHit,
    AssignmentRecommendation,
    PriorityRecommendation,
    TeamMember,
    CodeOwnership,
    TriageMetrics,
    WebhookPayload,
    EmbeddingRequest,
    EmbeddingResponse
)

# Notification schemas
from .notifications import (
    TriggerType,
    RecipientType,
    DeliveryMethod,
    NotificationStatus,
    EscalationLevel,
    NotificationTrigger,
    StakeholderRoute,
    AssignmentSuggestion,
    SimilarWorkItem,
    DuplicateCandidate,
    PriorityAnalysis,
    NotificationContext,
    DeduplicationKey,
    RateLimitBucket,
    NotificationAudit,
    QuietHours,
    ProjectNotificationConfig,
    NotificationRule,
    NotificationTemplate,
    NotificationMetrics
)

__all__ = [
    # Core schemas
    "WorkItem",
    "WorkItemType",
    "WorkItemState",
    "Priority",
    "TriageResult",
    "SearchResult",
    "DuplicateHit",
    "AssignmentRecommendation",
    "PriorityRecommendation",
    "TeamMember",
    "CodeOwnership",
    "TriageMetrics",
    "WebhookPayload",
    "EmbeddingRequest",
    "EmbeddingResponse",

    # Notification schemas
    "TriggerType",
    "RecipientType",
    "DeliveryMethod",
    "NotificationStatus",
    "EscalationLevel",
    "NotificationTrigger",
    "StakeholderRoute",
    "AssignmentSuggestion",
    "SimilarWorkItem",
    "DuplicateCandidate",
    "PriorityAnalysis",
    "NotificationContext",
    "DeduplicationKey",
    "RateLimitBucket",
    "NotificationAudit",
    "QuietHours",
    "ProjectNotificationConfig",
    "NotificationRule",
    "NotificationTemplate",
    "NotificationMetrics"
]
