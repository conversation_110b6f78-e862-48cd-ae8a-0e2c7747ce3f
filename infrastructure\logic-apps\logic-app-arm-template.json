{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"logicAppName": {"type": "string", "defaultValue": "auto-defect-email-notifications", "metadata": {"description": "Name of the Logic App"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources"}}, "adoOrganization": {"type": "string", "defaultValue": "virginatlantic", "metadata": {"description": "Azure DevOps Organization name"}}, "adoProject": {"type": "string", "defaultValue": "Air4 Channels Testing", "metadata": {"description": "Azure DevOps Project name"}}, "adoPatToken": {"type": "securestring", "metadata": {"description": "Azure DevOps Personal Access Token"}}, "emailRecipients": {"type": "string", "defaultValue": "<EMAIL>", "metadata": {"description": "Email recipients (comma-separated)"}}, "emailSender": {"type": "string", "defaultValue": "<EMAIL>", "metadata": {"description": "Email sender address"}}}, "variables": {"office365ConnectionName": "[concat(parameters('logicAppName'), '-office365')]"}, "resources": [{"type": "Microsoft.Web/connections", "apiVersion": "2016-06-01", "name": "[variables('office365ConnectionName')]", "location": "[parameters('location')]", "properties": {"displayName": "[variables('office365ConnectionName')]", "api": {"id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', parameters('location'), 'office365')]"}}}, {"type": "Microsoft.Logic/workflows", "apiVersion": "2019-05-01", "name": "[parameters('logicAppName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/connections', variables('office365ConnectionName'))]"], "properties": {"state": "Enabled", "definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}, "ado_organization": {"type": "string", "defaultValue": "[parameters('adoOrganization')]"}, "ado_project": {"type": "string", "defaultValue": "[parameters('adoProject')]"}, "ado_pat_token": {"type": "securestring", "defaultValue": "[parameters('adoPatToken')]"}, "email_recipients": {"type": "string", "defaultValue": "[parameters('emailRecipients')]"}, "email_sender": {"type": "string", "defaultValue": "[parameters('emailSender')]"}}, "triggers": {"Recurrence": {"recurrence": {"frequency": "Day", "interval": 1, "schedule": {"hours": ["9"], "minutes": [0]}, "timeZone": "GMT Standard Time"}, "type": "Recurrence"}}, "actions": {"Initialize_Variables": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "StartDate", "type": "string", "value": "@{formatDateTime(addDays(utcNow(), -2), 'yyyy-MM-dd')}"}, {"name": "EndDate", "type": "string", "value": "@{formatDateTime(utcNow(), 'yyyy-MM-dd')}"}, {"name": "EmailBody", "type": "string", "value": ""}]}, "runAfter": {}}, "Query_Work_Items": {"type": "Http", "inputs": {"method": "POST", "uri": "https://dev.azure.com/@{parameters('ado_organization')}/@{encodeUriComponent(parameters('ado_project'))}/_apis/wit/wiql?api-version=7.0", "headers": {"Authorization": "Basic @{base64(concat(':', parameters('ado_pat_token')))}", "Content-Type": "application/json"}, "body": {"query": "SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State], [System.AssignedTo], [System.CreatedDate], [System.CreatedBy], [Microsoft.VSTS.Common.Priority], [Microsoft.VSTS.Common.Severity], [System.AreaPath] FROM WorkItems WHERE [System.WorkItemType] IN ('Bug', 'Defect', 'User Story', 'Task') AND [System.CreatedDate] >= '@{variables('StartDate')}' AND [System.CreatedDate] < '@{variables('EndDate')}' ORDER BY [System.CreatedDate] DESC"}}, "runAfter": {"Initialize_Variables": ["Succeeded"]}}, "Condition_Check_Items": {"type": "If", "expression": {"and": [{"greater": ["@length(body('Query_Work_Items')?['workItems'])", 0]}]}, "actions": {"Get_Work_Item_Details": {"type": "Http", "inputs": {"method": "GET", "uri": "https://dev.azure.com/@{parameters('ado_organization')}/@{encodeUriComponent(parameters('ado_project'))}/_apis/wit/workitems?ids=@{join(select(body('Query_Work_Items')?['workItems'], item('id')), ',')}&$expand=all&api-version=7.0", "headers": {"Authorization": "Basic @{base64(concat(':', parameters('ado_pat_token')))}", "Content-Type": "application/json"}}}, "Send_Email_With_Items": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@parameters('email_recipients')", "Subject": "📊 Daily Work Items Report - @{length(body('Get_Work_Item_Details')?['value'])} items created in last 48 hours", "Body": "@{concat('<html><body style=\"font-family: Arial, sans-serif;\"><h2>📊 Daily Work Items Report</h2><p><strong>Period:</strong> ', variables('StartDate'), ' to ', variables('EndDate'), '</p><p><strong>Total Items:</strong> ', length(body('Get_Work_Item_Details')?['value']), '</p><hr>', join(select(body('Get_Work_Item_Details')?['value'], concat('<div style=\"border: 1px solid #ddd; padding: 10px; margin: 10px 0;\"><h3><a href=\"https://dev.azure.com/', parameters('ado_organization'), '/', encodeUriComponent(parameters('ado_project')), '/_workitems/edit/', item('id'), '\">', item('fields')['System.Title'], '</a></h3><p><strong>Type:</strong> ', item('fields')['System.WorkItemType'], ' | <strong>State:</strong> ', item('fields')['System.State'], ' | <strong>Priority:</strong> ', coalesce(string(item('fields')['Microsoft.VSTS.Common.Priority']), 'N/A'), '</p><p><strong>Assigned To:</strong> ', coalesce(item('fields')['System.AssignedTo']['displayName'], 'Unassigned'), ' | <strong>Created:</strong> ', formatDateTime(item('fields')['System.CreatedDate'], 'MMM dd, yyyy HH:mm'), '</p></div>')), ''), '</body></html>')}", "IsHtml": true, "From": "@parameters('email_sender')"}}, "runAfter": {"Get_Work_Item_Details": ["Succeeded"]}}}, "else": {"actions": {"Send_No_Items_Email": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@parameters('email_recipients')", "Subject": "✅ Daily Work Items Report - No new items in last 48 hours", "Body": "<html><body style=\"font-family: Arial, sans-serif;\"><h2>✅ All Clear!</h2><p>No new Bugs, Defects, or Stories were created in the last 48 hours.</p><p><strong>Report Period:</strong> @{variables('StartDate')} to @{variables('EndDate')}</p><p><em>Generated on @{formatDateTime(utcNow(), 'MMM dd, yyyy HH:mm')} UTC</em></p></body></html>", "IsHtml": true, "From": "@parameters('email_sender')"}}}}}, "runAfter": {"Query_Work_Items": ["Succeeded"]}}}}, "parameters": {"$connections": {"value": {"office365": {"connectionId": "[resourceId('Microsoft.Web/connections', variables('office365ConnectionName'))]", "connectionName": "[variables('office365ConnectionName')]", "id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', parameters('location'), 'office365')]"}}}}}}], "outputs": {"logicAppUrl": {"type": "string", "value": "[listCallbackURL(concat(resourceId('Microsoft.Logic/workflows', parameters('logicAppName')), '/triggers/Recurrence'), '2019-05-01').value]"}, "office365ConnectionName": {"type": "string", "value": "[variables('office365ConnectionName')]"}}}