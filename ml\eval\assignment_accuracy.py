"""
Assignment accuracy evaluation script.
Measures the accuracy of automated work item assignments.
"""

import asyncio
import json
import logging
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import pandas as pd
from dataclasses import dataclass

from functions.__app__.common.models.schemas import WorkItem
from functions.__app__.common.ai.assigner import AssignmentEngine
from functions.__app__.common.adapters.ado_client import AdoClient
from functions.__app__.common.adapters.search_client import SearchClient
from functions.__app__.common.utils.config import get_config
from functions.__app__.common.utils.logging import setup_logging

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)


@dataclass
class AssignmentEvaluation:
    """Results of assignment evaluation."""
    work_item_id: int
    actual_assignee: str
    predicted_assignee: Optional[str]
    confidence: float
    correct: bool
    reasoning: str


class AssignmentAccuracyEvaluator:
    """Evaluates the accuracy of automated work item assignments."""
    
    def __init__(self):
        self.config = get_config()
        self.ado_client = AdoClient(self.config)
        self.search_client = SearchClient(self.config)
        self.assignment_engine = AssignmentEngine(self.search_client, self.config)
    
    async def evaluate_assignment_accuracy(
        self,
        start_date: datetime,
        end_date: datetime,
        sample_size: Optional[int] = None
    ) -> Dict[str, float]:
        """
        Evaluate assignment accuracy over a date range.
        
        Args:
            start_date: Start date for evaluation
            end_date: End date for evaluation
            sample_size: Optional limit on number of items to evaluate
        
        Returns:
            Dictionary with accuracy metrics
        """
        try:
            logger.info(f"Starting assignment accuracy evaluation from {start_date} to {end_date}")
            
            # Fetch work items for evaluation
            work_items = await self._fetch_evaluation_work_items(start_date, end_date, sample_size)
            logger.info(f"Fetched {len(work_items)} work items for evaluation")
            
            if not work_items:
                logger.warning("No work items found for evaluation")
                return {}
            
            # Evaluate each work item
            evaluations = []
            for i, work_item in enumerate(work_items):
                if i % 10 == 0:
                    logger.info(f"Evaluating work item {i+1}/{len(work_items)}")
                
                evaluation = await self._evaluate_single_assignment(work_item)
                if evaluation:
                    evaluations.append(evaluation)
            
            # Calculate metrics
            metrics = self._calculate_assignment_metrics(evaluations)
            
            # Save results
            await self._save_evaluation_results(evaluations, metrics, start_date, end_date)
            
            logger.info(f"Assignment evaluation completed. Accuracy: {metrics.get('accuracy', 0):.2%}")
            return metrics
            
        except Exception as e:
            logger.error(f"Error in assignment accuracy evaluation: {e}")
            raise
    
    async def _fetch_evaluation_work_items(
        self,
        start_date: datetime,
        end_date: datetime,
        sample_size: Optional[int]
    ) -> List[WorkItem]:
        """Fetch work items for evaluation."""
        try:
            # Build WIQL query for completed work items with assignees
            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
                   [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
                   [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
                   [Microsoft.VSTS.TCM.ReproSteps], [Microsoft.VSTS.TCM.SystemInfo]
            FROM WorkItems
            WHERE [System.CreatedDate] >= '{start_date.isoformat()}'
              AND [System.CreatedDate] <= '{end_date.isoformat()}'
              AND [System.AssignedTo] <> ''
              AND [System.State] IN ('Closed', 'Resolved', 'Done')
              AND [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Feature')
            ORDER BY [System.CreatedDate] DESC
            """
            
            work_items_data = await self.ado_client.query_work_items(wiql_query)
            
            # Convert to WorkItem models
            work_items = []
            for item_data in work_items_data:
                try:
                    fields = item_data.get('fields', {})
                    work_item = WorkItem(
                        id=item_data.get('id'),
                        title=fields.get('System.Title', ''),
                        description=fields.get('System.Description', ''),
                        work_item_type=fields.get('System.WorkItemType', ''),
                        state=fields.get('System.State', ''),
                        area_path=fields.get('System.AreaPath', ''),
                        assigned_to=fields.get('System.AssignedTo', {}).get('displayName', ''),
                        created_by=fields.get('System.CreatedBy', {}).get('displayName', ''),
                        created_date=fields.get('System.CreatedDate', ''),
                        changed_date=fields.get('System.ChangedDate', ''),
                        priority=fields.get('Microsoft.VSTS.Common.Priority', 2),
                        tags=fields.get('System.Tags', ''),
                        repro_steps=fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
                        system_info=fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
                    )
                    work_items.append(work_item)
                except Exception as e:
                    logger.warning(f"Failed to parse work item {item_data.get('id')}: {e}")
                    continue
            
            # Apply sample size limit
            if sample_size and len(work_items) > sample_size:
                work_items = work_items[:sample_size]
            
            return work_items
            
        except Exception as e:
            logger.error(f"Error fetching evaluation work items: {e}")
            raise
    
    async def _evaluate_single_assignment(self, work_item: WorkItem) -> Optional[AssignmentEvaluation]:
        """Evaluate assignment for a single work item."""
        try:
            # Store the actual assignee
            actual_assignee = work_item.assigned_to
            
            if not actual_assignee:
                return None
            
            # Clear the assignee to simulate new work item
            work_item_copy = WorkItem(**work_item.dict())
            work_item_copy.assigned_to = None
            
            # Get assignment prediction
            predicted_assignee = await self.assignment_engine.assign_work_item(work_item_copy)
            
            # Calculate if prediction is correct
            correct = False
            confidence = 0.0
            
            if predicted_assignee:
                # Check for exact match or email match
                correct = (
                    predicted_assignee.lower() == actual_assignee.lower() or
                    predicted_assignee.split('@')[0].lower() == actual_assignee.split('@')[0].lower()
                )
                confidence = 0.8  # Would get this from assignment engine in real implementation
            
            return AssignmentEvaluation(
                work_item_id=work_item.id,
                actual_assignee=actual_assignee,
                predicted_assignee=predicted_assignee,
                confidence=confidence,
                correct=correct,
                reasoning="Automated assignment evaluation"
            )
            
        except Exception as e:
            logger.warning(f"Error evaluating assignment for work item {work_item.id}: {e}")
            return None
    
    def _calculate_assignment_metrics(self, evaluations: List[AssignmentEvaluation]) -> Dict[str, float]:
        """Calculate assignment accuracy metrics."""
        if not evaluations:
            return {}
        
        total_items = len(evaluations)
        correct_predictions = sum(1 for eval in evaluations if eval.correct)
        predictions_made = sum(1 for eval in evaluations if eval.predicted_assignee is not None)
        
        # Basic metrics
        accuracy = correct_predictions / total_items if total_items > 0 else 0.0
        coverage = predictions_made / total_items if total_items > 0 else 0.0
        precision = correct_predictions / predictions_made if predictions_made > 0 else 0.0
        
        # Confidence-based metrics
        high_confidence_items = [eval for eval in evaluations if eval.confidence >= 0.8]
        high_confidence_accuracy = (
            sum(1 for eval in high_confidence_items if eval.correct) / len(high_confidence_items)
            if high_confidence_items else 0.0
        )
        
        # Calculate metrics by work item type
        type_metrics = {}
        for work_type in ['Bug', 'Task', 'User Story', 'Feature']:
            type_evals = [eval for eval in evaluations if work_type.lower() in str(eval.work_item_id).lower()]
            if type_evals:
                type_accuracy = sum(1 for eval in type_evals if eval.correct) / len(type_evals)
                type_metrics[f'{work_type.lower()}_accuracy'] = type_accuracy
        
        metrics = {
            'total_items': total_items,
            'accuracy': accuracy,
            'coverage': coverage,
            'precision': precision,
            'high_confidence_accuracy': high_confidence_accuracy,
            'avg_confidence': sum(eval.confidence for eval in evaluations) / total_items,
            **type_metrics
        }
        
        return metrics
    
    async def _save_evaluation_results(
        self,
        evaluations: List[AssignmentEvaluation],
        metrics: Dict[str, float],
        start_date: datetime,
        end_date: datetime
    ) -> None:
        """Save evaluation results to files."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Save detailed results
            results_data = []
            for eval in evaluations:
                results_data.append({
                    'work_item_id': eval.work_item_id,
                    'actual_assignee': eval.actual_assignee,
                    'predicted_assignee': eval.predicted_assignee,
                    'confidence': eval.confidence,
                    'correct': eval.correct,
                    'reasoning': eval.reasoning
                })
            
            results_df = pd.DataFrame(results_data)
            results_file = f'ml/eval/results/assignment_results_{timestamp}.csv'
            results_df.to_csv(results_file, index=False)
            
            # Save metrics summary
            metrics_data = {
                'evaluation_date': datetime.now().isoformat(),
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'metrics': metrics
            }
            
            metrics_file = f'ml/eval/results/assignment_metrics_{timestamp}.json'
            with open(metrics_file, 'w') as f:
                json.dump(metrics_data, f, indent=2)
            
            logger.info(f"Evaluation results saved to {results_file} and {metrics_file}")
            
        except Exception as e:
            logger.error(f"Error saving evaluation results: {e}")


async def main():
    """Main function for running assignment accuracy evaluation."""
    evaluator = AssignmentAccuracyEvaluator()
    
    # Evaluate last 30 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    metrics = await evaluator.evaluate_assignment_accuracy(
        start_date=start_date,
        end_date=end_date,
        sample_size=100  # Limit for testing
    )
    
    print("Assignment Accuracy Metrics:")
    for metric, value in metrics.items():
        if isinstance(value, float):
            print(f"  {metric}: {value:.2%}")
        else:
            print(f"  {metric}: {value}")


if __name__ == "__main__":
    asyncio.run(main())
