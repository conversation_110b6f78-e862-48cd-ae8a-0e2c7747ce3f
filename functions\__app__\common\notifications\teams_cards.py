"""
Teams Adaptive Cards for Intelligent Notifications.
Rich, interactive notification cards for different notification types.
"""

import logging
from typing import Dict, Any, List, Optional, TYPE_CHECKING
from datetime import datetime

if TYPE_CHECKING:
    from ..models import (
        NotificationContext,
        NotificationTrigger,
        TriggerType,
        EscalationLevel,
        AssignmentSuggestion,
        SimilarWorkItem,
        DuplicateCandidate
    )
from ..models.schemas import WorkItem
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class TeamsCardBuilder:
    """
    Builder for Teams Adaptive Cards for different notification types.
    
    Creates rich, interactive cards with:
    - Contextual information
    - Action buttons
    - Visual indicators for priority/urgency
    - Structured data presentation
    """
    
    def __init__(self, base_url: str = ""):
        self.base_url = base_url
    
    def build_notification_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """
        Build an adaptive card for a notification context.

        Args:
            context: Complete notification context

        Returns:
            Teams adaptive card JSON
        """
        # Import here to avoid circular dependencies
        from ..models import TriggerType

        try:
            if context.trigger.trigger_type == TriggerType.CRITICAL_CREATED:
                return self._build_critical_alert_card(context)
            elif context.trigger.trigger_type == TriggerType.AGING:
                return self._build_aging_reminder_card(context)
            elif context.trigger.trigger_type == TriggerType.SECURITY_ALERT:
                return self._build_security_alert_card(context)
            elif context.trigger.trigger_type == TriggerType.ESCALATION:
                return self._build_escalation_card(context)
            elif context.trigger.trigger_type == TriggerType.DUPLICATE_DETECTED:
                return self._build_duplicate_alert_card(context)
            else:
                return self._build_generic_notification_card(context)
                
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error building Teams card: {e}",
                extra={"work_item_id": context.work_item.id},
                exc_info=True
            )
            return self._build_fallback_card(context)
    
    def _build_critical_alert_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build card for critical work item alerts."""
        work_item = context.work_item
        trigger = context.trigger
        
        # Determine color based on escalation level
        color = self._get_escalation_color(trigger.escalation_level)

        # Import here to avoid circular dependencies
        from ..models import EscalationLevel

        card_body = [
            {
                "type": "Container",
                "style": "attention" if trigger.escalation_level >= EscalationLevel.URGENT else "emphasis",
                "items": [
                    {
                        "type": "ColumnSet",
                        "columns": [
                            {
                                "type": "Column",
                                "width": "auto",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "🚨",
                                        "size": "Large"
                                    }
                                ]
                            },
                            {
                                "type": "Column",
                                "width": "stretch",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "Critical Work Item Alert",
                                        "weight": "Bolder",
                                        "size": "Large",
                                        "color": color
                                    },
                                    {
                                        "type": "TextBlock",
                                        "text": f"Escalation Level: {trigger.escalation_level.name}",
                                        "weight": "Lighter",
                                        "size": "Small"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "type": "Container",
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"**#{work_item.id}: {work_item.title}**",
                        "wrap": True,
                        "size": "Medium"
                    },
                    {
                        "type": "FactSet",
                        "facts": self._build_work_item_facts(work_item, trigger)
                    }
                ]
            }
        ]
        
        # Add priority analysis if available
        if context.priority_analysis:
            card_body.append(self._build_priority_section(context.priority_analysis))
        
        # Add assignment suggestions
        if context.suggested_assignees:
            card_body.append(self._build_assignment_section(context.suggested_assignees))
        
        # Add duplicate information
        if context.duplicate_candidates:
            card_body.append(self._build_duplicate_section(context.duplicate_candidates))
        
        # Add action buttons
        actions = self._build_action_buttons(work_item, "critical")
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": card_body,
                        "actions": actions
                    }
                }
            ]
        }
    
    def _build_aging_reminder_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build card for aging work item reminders."""
        work_item = context.work_item
        trigger = context.trigger
        
        age_hours = trigger.trigger_conditions.get("age_hours", 0)
        threshold_hours = trigger.trigger_conditions.get("threshold_hours", 0)
        
        card_body = [
            {
                "type": "Container",
                "style": "warning",
                "items": [
                    {
                        "type": "ColumnSet",
                        "columns": [
                            {
                                "type": "Column",
                                "width": "auto",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "⏰",
                                        "size": "Large"
                                    }
                                ]
                            },
                            {
                                "type": "Column",
                                "width": "stretch",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "Aging Work Item Reminder",
                                        "weight": "Bolder",
                                        "size": "Large",
                                        "color": "Warning"
                                    },
                                    {
                                        "type": "TextBlock",
                                        "text": f"Open for {age_hours:.1f} hours (threshold: {threshold_hours:.1f}h)",
                                        "weight": "Lighter",
                                        "size": "Small"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "type": "Container",
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"**#{work_item.id}: {work_item.title}**",
                        "wrap": True,
                        "size": "Medium"
                    },
                    {
                        "type": "FactSet",
                        "facts": self._build_work_item_facts(work_item, trigger)
                    }
                ]
            }
        ]
        
        # Add similar items for context
        if context.similar_items:
            card_body.append(self._build_similar_items_section(context.similar_items))
        
        # Add assignment suggestions
        if context.suggested_assignees:
            card_body.append(self._build_assignment_section(context.suggested_assignees))
        
        # Add action buttons
        actions = self._build_action_buttons(work_item, "aging")
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": card_body,
                        "actions": actions
                    }
                }
            ]
        }
    
    def _build_security_alert_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build card for security alerts."""
        work_item = context.work_item
        
        card_body = [
            {
                "type": "Container",
                "style": "attention",
                "items": [
                    {
                        "type": "ColumnSet",
                        "columns": [
                            {
                                "type": "Column",
                                "width": "auto",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "🔒",
                                        "size": "Large"
                                    }
                                ]
                            },
                            {
                                "type": "Column",
                                "width": "stretch",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "Security Alert",
                                        "weight": "Bolder",
                                        "size": "Large",
                                        "color": "Attention"
                                    },
                                    {
                                        "type": "TextBlock",
                                        "text": "Requires immediate security review",
                                        "weight": "Lighter",
                                        "size": "Small"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "type": "Container",
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"**#{work_item.id}: {work_item.title}**",
                        "wrap": True,
                        "size": "Medium"
                    },
                    {
                        "type": "FactSet",
                        "facts": self._build_work_item_facts(work_item, context.trigger)
                    }
                ]
            }
        ]
        
        # Add security-specific actions
        actions = self._build_action_buttons(work_item, "security")
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": card_body,
                        "actions": actions
                    }
                }
            ]
        }
    
    def _build_escalation_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build card for escalation notifications."""
        return self._build_critical_alert_card(context)  # Similar to critical alert
    
    def _build_duplicate_alert_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build card for duplicate detection alerts."""
        work_item = context.work_item
        
        card_body = [
            {
                "type": "Container",
                "style": "good",
                "items": [
                    {
                        "type": "ColumnSet",
                        "columns": [
                            {
                                "type": "Column",
                                "width": "auto",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "🔍",
                                        "size": "Large"
                                    }
                                ]
                            },
                            {
                                "type": "Column",
                                "width": "stretch",
                                "items": [
                                    {
                                        "type": "TextBlock",
                                        "text": "Potential Duplicates Detected",
                                        "weight": "Bolder",
                                        "size": "Large",
                                        "color": "Good"
                                    },
                                    {
                                        "type": "TextBlock",
                                        "text": f"Found {len(context.duplicate_candidates)} potential duplicates",
                                        "weight": "Lighter",
                                        "size": "Small"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "type": "Container",
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"**#{work_item.id}: {work_item.title}**",
                        "wrap": True,
                        "size": "Medium"
                    }
                ]
            }
        ]
        
        # Add duplicate candidates
        if context.duplicate_candidates:
            card_body.append(self._build_duplicate_section(context.duplicate_candidates))
        
        # Add action buttons
        actions = self._build_action_buttons(work_item, "duplicate")
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": card_body,
                        "actions": actions
                    }
                }
            ]
        }
    
    def _build_generic_notification_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build generic notification card."""
        work_item = context.work_item
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": [
                            {
                                "type": "TextBlock",
                                "text": f"Work Item Notification",
                                "weight": "Bolder",
                                "size": "Large"
                            },
                            {
                                "type": "TextBlock",
                                "text": f"**#{work_item.id}: {work_item.title}**",
                                "wrap": True
                            },
                            {
                                "type": "FactSet",
                                "facts": self._build_work_item_facts(work_item, context.trigger)
                            }
                        ],
                        "actions": self._build_action_buttons(work_item, "generic")
                    }
                }
            ]
        }
    
    def _build_fallback_card(self, context: "NotificationContext") -> Dict[str, Any]:
        """Build fallback card when errors occur."""
        work_item = context.work_item
        
        return {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "type": "AdaptiveCard",
                        "version": "1.4",
                        "body": [
                            {
                                "type": "TextBlock",
                                "text": f"Work Item #{work_item.id}: {work_item.title}",
                                "wrap": True,
                                "weight": "Bolder"
                            },
                            {
                                "type": "TextBlock",
                                "text": f"Priority: P{work_item.priority} | State: {work_item.state}",
                                "wrap": True
                            }
                        ]
                    }
                }
            ]
        }

    def _build_work_item_facts(self, work_item: WorkItem, trigger: "NotificationTrigger") -> List[Dict[str, str]]:
        """Build fact set for work item details."""
        facts = [
            {"title": "Priority", "value": f"P{work_item.priority}"},
            {"title": "State", "value": work_item.state},
            {"title": "Project", "value": work_item.project}
        ]

        if work_item.assigned_to:
            facts.append({"title": "Assigned To", "value": work_item.assigned_to})
        else:
            facts.append({"title": "Status", "value": "Unassigned"})

        if work_item.created_date:
            age = datetime.utcnow() - work_item.created_date
            facts.append({"title": "Age", "value": f"{age.days} days"})

        if trigger.severity_score > 0:
            facts.append({"title": "Severity Score", "value": f"{trigger.severity_score:.1f}/10"})

        return facts

    def _build_priority_section(self, priority_analysis) -> Dict[str, Any]:
        """Build priority analysis section."""
        return {
            "type": "Container",
            "items": [
                {
                    "type": "TextBlock",
                    "text": "🎯 **Priority Analysis**",
                    "weight": "Bolder",
                    "size": "Medium"
                },
                {
                    "type": "FactSet",
                    "facts": [
                        {
                            "title": "Suggested Priority",
                            "value": f"P{priority_analysis.suggested_priority}"
                        },
                        {
                            "title": "Confidence",
                            "value": f"{priority_analysis.confidence_score:.1%}"
                        },
                        {
                            "title": "Customer Impact",
                            "value": "Yes" if priority_analysis.customer_impact else "No"
                        },
                        {
                            "title": "Security Impact",
                            "value": "Yes" if priority_analysis.security_impact else "No"
                        }
                    ]
                },
                {
                    "type": "TextBlock",
                    "text": priority_analysis.reasoning,
                    "wrap": True,
                    "size": "Small",
                    "isSubtle": True
                }
            ]
        }

    def _build_assignment_section(self, suggestions: List["AssignmentSuggestion"]) -> Dict[str, Any]:
        """Build assignment suggestions section."""
        items = [
            {
                "type": "TextBlock",
                "text": "👤 **Assignment Suggestions**",
                "weight": "Bolder",
                "size": "Medium"
            }
        ]

        for i, suggestion in enumerate(suggestions[:3], 1):
            items.append({
                "type": "ColumnSet",
                "columns": [
                    {
                        "type": "Column",
                        "width": "auto",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"{i}.",
                                "weight": "Bolder"
                            }
                        ]
                    },
                    {
                        "type": "Column",
                        "width": "stretch",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"**{suggestion.assignee_name}** ({suggestion.confidence_score:.1%})",
                                "wrap": True
                            },
                            {
                                "type": "TextBlock",
                                "text": suggestion.reasoning,
                                "wrap": True,
                                "size": "Small",
                                "isSubtle": True
                            }
                        ]
                    }
                ]
            })

        return {
            "type": "Container",
            "items": items
        }

    def _build_duplicate_section(self, duplicates: List["DuplicateCandidate"]) -> Dict[str, Any]:
        """Build duplicate candidates section."""
        items = [
            {
                "type": "TextBlock",
                "text": "🔍 **Potential Duplicates**",
                "weight": "Bolder",
                "size": "Medium"
            }
        ]

        for duplicate in duplicates[:3]:
            items.append({
                "type": "ColumnSet",
                "columns": [
                    {
                        "type": "Column",
                        "width": "auto",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"#{duplicate.work_item_id}",
                                "weight": "Bolder",
                                "color": "Accent"
                            }
                        ]
                    },
                    {
                        "type": "Column",
                        "width": "stretch",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"{duplicate.title} ({duplicate.similarity_score:.1%})",
                                "wrap": True
                            },
                            {
                                "type": "TextBlock",
                                "text": f"State: {duplicate.state}",
                                "size": "Small",
                                "isSubtle": True
                            }
                        ]
                    }
                ]
            })

        return {
            "type": "Container",
            "items": items
        }

    def _build_similar_items_section(self, similar_items: List["SimilarWorkItem"]) -> Dict[str, Any]:
        """Build similar items section."""
        items = [
            {
                "type": "TextBlock",
                "text": "📚 **Similar Historical Items**",
                "weight": "Bolder",
                "size": "Medium"
            }
        ]

        for item in similar_items[:3]:
            items.append({
                "type": "ColumnSet",
                "columns": [
                    {
                        "type": "Column",
                        "width": "auto",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"#{item.work_item_id}",
                                "weight": "Bolder",
                                "color": "Accent"
                            }
                        ]
                    },
                    {
                        "type": "Column",
                        "width": "stretch",
                        "items": [
                            {
                                "type": "TextBlock",
                                "text": f"{item.title} ({item.similarity_score:.1%})",
                                "wrap": True
                            },
                            {
                                "type": "TextBlock",
                                "text": f"Resolved by: {item.assignee}" if item.assignee else "Resolution info not available",
                                "size": "Small",
                                "isSubtle": True
                            }
                        ]
                    }
                ]
            })

        return {
            "type": "Container",
            "items": items
        }

    def _build_action_buttons(self, work_item: WorkItem, notification_type: str) -> List[Dict[str, Any]]:
        """Build action buttons for the card."""
        actions = []

        # View work item button (always present)
        ado_url = f"https://dev.azure.com/{work_item.project}/_workitems/edit/{work_item.id}"
        actions.append({
            "type": "Action.OpenUrl",
            "title": "View Work Item",
            "url": ado_url
        })

        # Assign to me button (if unassigned)
        if not work_item.assigned_to:
            actions.append({
                "type": "Action.Submit",
                "title": "Assign to Me",
                "data": {
                    "action": "assign_to_me",
                    "work_item_id": work_item.id
                }
            })

        # Type-specific actions
        if notification_type == "critical":
            actions.append({
                "type": "Action.Submit",
                "title": "Acknowledge",
                "data": {
                    "action": "acknowledge",
                    "work_item_id": work_item.id
                }
            })
        elif notification_type == "duplicate":
            actions.append({
                "type": "Action.Submit",
                "title": "Mark as Duplicate",
                "data": {
                    "action": "mark_duplicate",
                    "work_item_id": work_item.id
                }
            })
        elif notification_type == "security":
            actions.append({
                "type": "Action.Submit",
                "title": "Escalate to Security",
                "data": {
                    "action": "escalate_security",
                    "work_item_id": work_item.id
                }
            })

        return actions

    def _get_escalation_color(self, escalation_level: "EscalationLevel") -> str:
        """Get color based on escalation level."""
        # Import here to avoid circular dependencies
        from ..models import EscalationLevel

        color_map = {
            EscalationLevel.NORMAL: "Default",
            EscalationLevel.ELEVATED: "Warning",
            EscalationLevel.URGENT: "Attention",
            EscalationLevel.CRITICAL: "Attention"
        }
        return color_map.get(escalation_level, "Default")
