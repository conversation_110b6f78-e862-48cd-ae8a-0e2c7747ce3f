"""
Configuration loader for notification system.
Loads project-specific notification configurations from various sources.
"""

import logging
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from ..models import (
    ProjectNotificationConfig,
    NotificationRule,
    QuietHours,
    EscalationLevel,
    TriggerType,
    RecipientType,
    DeliveryMethod
)
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class NotificationConfigLoader:
    """
    Loads and manages notification configurations for projects.
    
    Configuration sources (in order of precedence):
    1. Environment variables (for global defaults)
    2. Key Vault secrets (for sensitive data)
    3. Configuration files (for project-specific settings)
    4. Database/storage (for dynamic configuration)
    """
    
    def __init__(self, config: Config):
        self.config = config
        self._project_configs: Dict[str, ProjectNotificationConfig] = {}
        self._config_cache_ttl = 300  # 5 minutes
        self._last_load_time: Optional[float] = None
    
    def load_project_config(self, project: str) -> ProjectNotificationConfig:
        """
        Load configuration for a specific project.
        
        Args:
            project: Project name
        
        Returns:
            Project notification configuration
        """
        try:
            # Check cache first
            if project in self._project_configs:
                return self._project_configs[project]
            
            # Load from various sources
            config = self._load_from_sources(project)
            
            # Cache the configuration
            self._project_configs[project] = config
            
            log_structured(
                logger,
                "info",
                f"Loaded notification config for project: {project}",
                extra={
                    "project": project,
                    "enabled": config.enabled,
                    "rules_count": len(config.notification_rules)
                }
            )
            
            return config
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error loading project config for {project}: {e}",
                extra={"project": project},
                exc_info=True
            )
            
            # Return default configuration on error
            return self._get_default_config(project)
    
    def load_all_project_configs(self) -> Dict[str, ProjectNotificationConfig]:
        """Load configurations for all known projects."""
        try:
            # This would typically query a database or configuration store
            # For now, load from configuration files
            configs = {}
            
            # Load default configuration
            default_config = self._get_default_config("default")
            configs["default"] = default_config
            
            # Load project-specific configurations from files
            config_dir = Path(__file__).parent.parent.parent / "config" / "notifications"
            if config_dir.exists():
                for config_file in config_dir.glob("*.json"):
                    project_name = config_file.stem
                    try:
                        project_config = self._load_from_file(config_file)
                        configs[project_name] = project_config
                    except Exception as e:
                        log_structured(
                            logger,
                            "error",
                            f"Error loading config file {config_file}: {e}",
                            exc_info=True
                        )
            
            self._project_configs.update(configs)
            
            log_structured(
                logger,
                "info",
                f"Loaded {len(configs)} project configurations",
                extra={"projects": list(configs.keys())}
            )
            
            return configs
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error loading all project configs: {e}",
                exc_info=True
            )
            return {"default": self._get_default_config("default")}
    
    def _load_from_sources(self, project: str) -> ProjectNotificationConfig:
        """Load configuration from multiple sources."""
        
        # Start with default configuration
        config = self._get_default_config(project)
        
        # Try to load from configuration file
        config_file = Path(__file__).parent.parent.parent / "config" / "notifications" / f"{project}.json"
        if config_file.exists():
            try:
                file_config = self._load_from_file(config_file)
                config = self._merge_configs(config, file_config)
            except Exception as e:
                log_structured(
                    logger,
                    "warning",
                    f"Error loading config file for {project}: {e}",
                    extra={"project": project, "config_file": str(config_file)}
                )
        
        # Override with environment-specific settings
        config = self._apply_environment_overrides(config, project)
        
        return config
    
    def _load_from_file(self, config_file: Path) -> ProjectNotificationConfig:
        """Load configuration from JSON file."""
        with open(config_file, 'r') as f:
            config_data = json.load(f)
        
        return self._parse_config_data(config_data)
    
    def _parse_config_data(self, config_data: Dict[str, Any]) -> ProjectNotificationConfig:
        """Parse configuration data into ProjectNotificationConfig."""
        
        # Parse notification rules
        rules = []
        for rule_data in config_data.get("notification_rules", []):
            rule = NotificationRule(
                name=rule_data["name"],
                trigger_types=[TriggerType(t) for t in rule_data.get("trigger_types", [])],
                priority_filter=rule_data.get("priority_filter"),
                project_filter=rule_data.get("project_filter"),
                assignee_filter=rule_data.get("assignee_filter"),
                enabled=rule_data.get("enabled", True),
                escalation_level=EscalationLevel(rule_data.get("escalation_level", "NORMAL")),
                recipient_types=[RecipientType(r) for r in rule_data.get("recipient_types", [])],
                delivery_methods=[DeliveryMethod(d) for d in rule_data.get("delivery_methods", [])]
            )
            rules.append(rule)
        
        # Parse quiet hours
        quiet_hours = None
        if "quiet_hours" in config_data:
            qh_data = config_data["quiet_hours"]
            quiet_hours = QuietHours(
                enabled=qh_data.get("enabled", False),
                start_time=qh_data.get("start_time", "22:00"),
                end_time=qh_data.get("end_time", "08:00"),
                timezone=qh_data.get("timezone", "UTC"),
                bypass_for_critical=qh_data.get("bypass_for_critical", True)
            )
        
        return ProjectNotificationConfig(
            project_name=config_data["project_name"],
            enabled=config_data.get("enabled", True),
            critical_priorities=config_data.get("critical_priorities", [1, 2]),
            aging_thresholds=config_data.get("aging_thresholds", {}),
            security_keywords=config_data.get("security_keywords", []),
            customer_impact_keywords=config_data.get("customer_impact_keywords", []),
            team_channels=config_data.get("team_channels", {}),
            code_owners=config_data.get("code_owners", {}),
            managers=config_data.get("managers", []),
            pagerduty_service_keys=config_data.get("pagerduty_service_keys", {}),
            rate_limits=config_data.get("rate_limits", {}),
            dedup_window_minutes=config_data.get("dedup_window_minutes", 60),
            quiet_hours=quiet_hours,
            notification_rules=rules
        )
    
    def _get_default_config(self, project: str) -> ProjectNotificationConfig:
        """Get default configuration for a project."""
        
        # Default aging thresholds
        aging_thresholds = {
            "P1": "4h",
            "P2": "24h",
            "P3": "72h",
            "P4": "168h"
        }
        
        # Default rate limits
        rate_limits = {
            "per_user_per_hour": getattr(self.config, "NOTIFICATION_RATE_LIMIT_PER_USER_HOUR", 5),
            "per_channel_per_hour": getattr(self.config, "NOTIFICATION_RATE_LIMIT_PER_CHANNEL_HOUR", 20),
            "per_pagerduty_per_hour": getattr(self.config, "NOTIFICATION_RATE_LIMIT_PER_PAGERDUTY_HOUR", 10)
        }
        
        # Default quiet hours
        quiet_hours = QuietHours(
            enabled=True,
            start_time=getattr(self.config, "NOTIFICATION_QUIET_HOURS_START", "22:00"),
            end_time=getattr(self.config, "NOTIFICATION_QUIET_HOURS_END", "08:00"),
            timezone=getattr(self.config, "NOTIFICATION_QUIET_HOURS_TIMEZONE", "UTC"),
            bypass_for_critical=getattr(self.config, "NOTIFICATION_CRITICAL_BYPASS_QUIET_HOURS", True)
        )
        
        # Default security keywords
        security_keywords = [
            "security", "vulnerability", "exploit", "breach", "cve",
            "injection", "xss", "csrf", "authentication", "authorization"
        ]
        
        # Default customer impact keywords
        customer_impact_keywords = [
            "customer", "production", "outage", "critical", "live",
            "downtime", "performance", "slow", "error", "failure"
        ]
        
        return ProjectNotificationConfig(
            project_name=project,
            enabled=getattr(self.config, "NOTIFICATIONS_ENABLED", True),
            critical_priorities=[1, 2],
            aging_thresholds=aging_thresholds,
            security_keywords=security_keywords,
            customer_impact_keywords=customer_impact_keywords,
            rate_limits=rate_limits,
            dedup_window_minutes=getattr(self.config, "NOTIFICATION_DEDUP_WINDOW_MINUTES", 60),
            quiet_hours=quiet_hours
        )
    
    def _merge_configs(
        self,
        base_config: ProjectNotificationConfig,
        override_config: ProjectNotificationConfig
    ) -> ProjectNotificationConfig:
        """Merge two configurations, with override taking precedence."""
        
        # Create a new config with base values
        merged_data = base_config.dict()
        
        # Override with non-None values from override config
        override_data = override_config.dict()
        for key, value in override_data.items():
            if value is not None:
                if isinstance(value, dict) and key in merged_data:
                    # Merge dictionaries
                    merged_data[key].update(value)
                elif isinstance(value, list) and key in merged_data:
                    # Replace lists entirely
                    merged_data[key] = value
                else:
                    # Replace scalar values
                    merged_data[key] = value
        
        return ProjectNotificationConfig(**merged_data)
    
    def _apply_environment_overrides(
        self,
        config: ProjectNotificationConfig,
        project: str
    ) -> ProjectNotificationConfig:
        """Apply environment-specific overrides to configuration."""
        
        # Check for project-specific environment variables
        env_prefix = f"NOTIFICATION_{project.upper()}_"
        
        # Override enabled status
        enabled_key = f"{env_prefix}ENABLED"
        if getattr(self.config, enabled_key, None) is not None:
            config.enabled = getattr(self.config, enabled_key, config.enabled)

        # Override rate limits
        rate_limit_user_key = f"{env_prefix}RATE_LIMIT_USER"
        if getattr(self.config, rate_limit_user_key, None) is not None:
            config.rate_limits["per_user_per_hour"] = getattr(self.config, rate_limit_user_key, 5)

        # Override dedup window
        dedup_key = f"{env_prefix}DEDUP_WINDOW_MINUTES"
        if getattr(self.config, dedup_key, None) is not None:
            config.dedup_window_minutes = getattr(self.config, dedup_key, 60)
        
        return config
    
    def reload_configs(self) -> None:
        """Reload all configurations from sources."""
        self._project_configs.clear()
        self.load_all_project_configs()
        
        log_structured(
            logger,
            "info",
            "Reloaded all notification configurations"
        )
    
    def get_cached_config(self, project: str) -> Optional[ProjectNotificationConfig]:
        """Get cached configuration without loading."""
        return self._project_configs.get(project)
    
    def clear_cache(self) -> None:
        """Clear the configuration cache."""
        self._project_configs.clear()
        
        log_structured(
            logger,
            "info",
            "Cleared notification configuration cache"
        )
