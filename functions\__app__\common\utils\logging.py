"""
Structured logging utilities for Application Insights integration.
"""

import logging
import json
import sys
from typing import Dict, Any, Optional
from datetime import datetime
import traceback
import os


def setup_logging(level: str = "INFO") -> None:
    """
    Setup structured logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create custom formatter for structured logging
    formatter = StructuredFormatter()
    
    # Setup root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Add console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger('azure').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)
    
    logging.info("Structured logging initialized", extra={"level": level})


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format log record as structured JSON.
        
        Args:
            record: Log record to format
        
        Returns:
            Formatted JSON string
        """
        # Base log entry
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }
        
        # Add process and thread info
        log_entry["process_id"] = os.getpid()
        log_entry["thread_id"] = record.thread
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # Add custom fields from extra
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # Add any other custom attributes
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'extra_fields']:
                try:
                    # Only add JSON-serializable values
                    json.dumps(value)
                    log_entry[key] = value
                except (TypeError, ValueError):
                    log_entry[key] = str(value)
        
        try:
            return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))
        except (TypeError, ValueError) as e:
            # Fallback to simple format if JSON serialization fails
            return f"{log_entry['timestamp']} {log_entry['level']} {log_entry['message']}"


def log_structured(
    logger: logging.Logger,
    level: str,
    message: str,
    extra: Optional[Dict[str, Any]] = None,
    exc_info: bool = False
) -> None:
    """
    Log a structured message with additional fields.
    
    Args:
        logger: Logger instance to use
        level: Log level (debug, info, warning, error, critical)
        message: Log message
        extra: Additional fields to include in log
        exc_info: Whether to include exception information
    """
    # Prepare extra fields
    extra_fields = extra or {}
    
    # Get numeric log level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create log record with extra fields
    if logger.isEnabledFor(numeric_level):
        # Add extra fields to the record
        logger.log(
            numeric_level,
            message,
            extra={"extra_fields": extra_fields},
            exc_info=exc_info
        )


def log_function_entry(
    logger: logging.Logger,
    function_name: str,
    args: Optional[Dict[str, Any]] = None,
    level: str = "DEBUG"
) -> None:
    """
    Log function entry with arguments.
    
    Args:
        logger: Logger instance
        function_name: Name of the function
        args: Function arguments to log
        level: Log level
    """
    extra = {
        "event_type": "function_entry",
        "function_name": function_name
    }
    
    if args:
        # Sanitize arguments (remove sensitive data)
        sanitized_args = {}
        sensitive_keys = {'password', 'token', 'key', 'secret', 'credential'}
        
        for key, value in args.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                sanitized_args[key] = "***"
            else:
                try:
                    # Test if value is JSON serializable
                    json.dumps(value)
                    sanitized_args[key] = value
                except (TypeError, ValueError):
                    sanitized_args[key] = str(value)
        
        extra["arguments"] = sanitized_args
    
    log_structured(logger, level, f"Entering function: {function_name}", extra=extra)


def log_function_exit(
    logger: logging.Logger,
    function_name: str,
    result: Optional[Any] = None,
    duration_ms: Optional[float] = None,
    level: str = "DEBUG"
) -> None:
    """
    Log function exit with result and duration.
    
    Args:
        logger: Logger instance
        function_name: Name of the function
        result: Function result to log
        duration_ms: Function execution duration in milliseconds
        level: Log level
    """
    extra = {
        "event_type": "function_exit",
        "function_name": function_name
    }
    
    if duration_ms is not None:
        extra["duration_ms"] = duration_ms
    
    if result is not None:
        try:
            # Test if result is JSON serializable
            json.dumps(result)
            extra["result"] = result
        except (TypeError, ValueError):
            extra["result_type"] = type(result).__name__
            extra["result_str"] = str(result)[:200]  # Truncate long results
    
    log_structured(logger, level, f"Exiting function: {function_name}", extra=extra)


def log_performance_metric(
    logger: logging.Logger,
    metric_name: str,
    value: float,
    unit: str = "ms",
    tags: Optional[Dict[str, str]] = None
) -> None:
    """
    Log a performance metric.
    
    Args:
        logger: Logger instance
        metric_name: Name of the metric
        value: Metric value
        unit: Unit of measurement
        tags: Additional tags for the metric
    """
    extra = {
        "event_type": "performance_metric",
        "metric_name": metric_name,
        "metric_value": value,
        "metric_unit": unit
    }
    
    if tags:
        extra["metric_tags"] = tags
    
    log_structured(
        logger,
        "INFO",
        f"Performance metric: {metric_name} = {value} {unit}",
        extra=extra
    )


def log_business_event(
    logger: logging.Logger,
    event_name: str,
    event_data: Dict[str, Any],
    user_id: Optional[str] = None
) -> None:
    """
    Log a business event for analytics.
    
    Args:
        logger: Logger instance
        event_name: Name of the business event
        event_data: Event data
        user_id: User ID associated with the event
    """
    extra = {
        "event_type": "business_event",
        "business_event_name": event_name,
        "business_event_data": event_data
    }
    
    if user_id:
        extra["user_id"] = user_id
    
    log_structured(
        logger,
        "INFO",
        f"Business event: {event_name}",
        extra=extra
    )


def log_security_event(
    logger: logging.Logger,
    event_type: str,
    description: str,
    user_id: Optional[str] = None,
    ip_address: Optional[str] = None,
    additional_data: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log a security-related event.
    
    Args:
        logger: Logger instance
        event_type: Type of security event
        description: Event description
        user_id: User ID involved in the event
        ip_address: IP address involved
        additional_data: Additional security-relevant data
    """
    extra = {
        "event_type": "security_event",
        "security_event_type": event_type,
        "security_description": description
    }
    
    if user_id:
        extra["user_id"] = user_id
    
    if ip_address:
        extra["ip_address"] = ip_address
    
    if additional_data:
        extra["security_data"] = additional_data
    
    log_structured(
        logger,
        "WARNING",
        f"Security event: {event_type} - {description}",
        extra=extra
    )
