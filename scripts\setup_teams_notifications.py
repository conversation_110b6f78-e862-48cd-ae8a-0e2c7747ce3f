#!/usr/bin/env python3
"""
Teams Notification Setup Script
==============================

This script helps you set up Teams notifications for the AutoDefectTriage system.
It will guide you through the configuration and test the setup.

Usage:
    python setup_teams_notifications.py
"""

import asyncio
import aiohttp
import json
import os
from pathlib import Path


class TeamsNotificationSetup:
    """Helper class to set up Teams notifications."""
    
    def __init__(self):
        self.config_file = Path("functions/local.settings.json")
        self.webhook_url = None
        
    def display_welcome(self):
        """Display welcome message and instructions."""
        print("🚀 AutoDefectTriage Teams Notification Setup")
        print("=" * 60)
        print()
        print("This script will help you configure Teams notifications.")
        print("You'll need to create a Teams Incoming Webhook first.")
        print()
        print("📋 Steps to create a Teams webhook:")
        print("1. Open Microsoft Teams")
        print("2. Go to the channel where you want notifications")
        print("3. Click the three dots (...) next to the channel name")
        print("4. Select 'Connectors'")
        print("5. Find 'Incoming Webhook' and click 'Configure'")
        print("6. Give it a name (e.g., 'AutoDefectTriage Notifications')")
        print("7. Click 'Create' and copy the webhook URL")
        print()
    
    def get_webhook_url(self):
        """Get webhook URL from user input."""
        print("🔗 Please enter your Teams webhook URL:")
        print("(It should start with https://outlook.office.com/webhook/...)")
        print()
        
        while True:
            webhook_url = input("Webhook URL: ").strip()
            
            if not webhook_url:
                print("❌ Please enter a webhook URL")
                continue
                
            if not webhook_url.startswith("https://outlook.office.com/webhook/"):
                print("⚠️  Warning: This doesn't look like a Teams webhook URL")
                confirm = input("Continue anyway? (y/n): ").strip().lower()
                if confirm != 'y':
                    continue
            
            self.webhook_url = webhook_url
            print(f"✅ Webhook URL saved: {webhook_url[:50]}...")
            break
    
    def update_config_file(self):
        """Update the local.settings.json file with Teams configuration."""
        print()
        print("📝 Updating configuration file...")
        
        # Load existing config or create new one
        config = {}
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
            except Exception as e:
                print(f"⚠️  Warning: Could not read existing config: {e}")
                config = {}
        
        # Ensure Values section exists
        if "Values" not in config:
            config["Values"] = {}
        
        # Add Teams configuration
        config["Values"]["TEAMS_WEBHOOK_URL"] = self.webhook_url
        config["Values"]["TEAMS_NOTIFICATIONS_ENABLED"] = "true"
        config["Values"]["TEAMS_NOTIFICATION_CHANNEL"] = "AutoDefectTriage Alerts"
        
        # Add other required settings if missing
        if "AzureWebJobsStorage" not in config["Values"]:
            config["Values"]["AzureWebJobsStorage"] = "UseDevelopmentStorage=true"
        if "FUNCTIONS_WORKER_RUNTIME" not in config["Values"]:
            config["Values"]["FUNCTIONS_WORKER_RUNTIME"] = "python"
        
        # Ensure IsEncrypted is set
        config["IsEncrypted"] = False
        
        # Create functions directory if it doesn't exist
        self.config_file.parent.mkdir(exist_ok=True)
        
        # Write updated config
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            print(f"✅ Configuration updated: {self.config_file}")
        except Exception as e:
            print(f"❌ Failed to update config file: {e}")
            return False
        
        return True
    
    async def test_webhook(self):
        """Test the Teams webhook with a sample message."""
        print()
        print("🧪 Testing Teams webhook...")
        
        # Create test message
        test_message = {
            "@type": "MessageCard",
            "@context": "http://schema.org/extensions",
            "themeColor": "0076D7",
            "summary": "AutoDefectTriage Test Notification",
            "sections": [{
                "activityTitle": "🧪 Test Notification",
                "activitySubtitle": "AutoDefectTriage System Setup",
                "facts": [{
                    "name": "Status:",
                    "value": "Configuration Test"
                }, {
                    "name": "Work Item ID:",
                    "value": "TEST-12345"
                }, {
                    "name": "Priority:",
                    "value": "1 (Critical)"
                }, {
                    "name": "Assigned To:",
                    "value": "auth-team"
                }],
                "markdown": True
            }],
            "potentialAction": [{
                "@type": "OpenUri",
                "name": "View Setup Guide",
                "targets": [{
                    "os": "default",
                    "uri": "https://github.com/your-org/AutoDefectTriage"
                }]
            }]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url, 
                    json=test_message,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        print("✅ Test notification sent successfully!")
                        print("📱 Check your Teams channel for the test message.")
                        return True
                    else:
                        error_text = await response.text()
                        print(f"❌ Test failed with status {response.status}")
                        print(f"Error: {error_text}")
                        return False
                        
        except asyncio.TimeoutError:
            print("❌ Test failed: Request timed out")
            return False
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            return False
    
    async def run_workflow_test(self):
        """Run the actual workflow to test Teams notifications."""
        print()
        print("🔄 Running workflow test with Teams notifications...")
        
        try:
            # Import and run the workflow
            import sys
            sys.path.append(str(Path(__file__).parent))
            
            from execute_function_step_by_step import StepByStepExecutor
            
            executor = StepByStepExecutor()
            await executor.initialize_function_components()
            
            print("✅ Function components initialized")
            print("🚀 Executing workflow with Teams notifications...")
            
            # Execute just the Teams notification step
            from datetime import datetime
            from __app__.workflow_orchestrator import WorkflowContext
            from __app__.common.models.schemas import WorkItem, TriageResult
            
            # Create test context
            context = WorkflowContext(
                work_item=WorkItem(
                    id=12345,
                    title="Critical bug in user authentication system",
                    description="Test notification from AutoDefectTriage setup",
                    work_item_type="Bug",
                    state="New",
                    assigned_to="auth-team",
                    created_date=datetime.utcnow().isoformat(),
                    project="WebApp",
                    priority=1,
                    severity="High",
                    tags="test, setup, notification",
                    area_path="WebApp\\Authentication",
                    iteration_path="WebApp\\Sprint 24"
                ),
                historical_items=[],
                triage_result=TriageResult(
                    work_item_id=12345,
                    assigned_to="auth-team",
                    priority=1,
                    duplicates=[],
                    confidence_score=0.85,
                    reasoning="Test assignment for Teams notification setup"
                ),
                ai_message="Test AI message for Teams notification",
                notification_ids=[],
                email_sent=False,
                teams_sent=False,
                errors=[],
                start_time=datetime.utcnow(),
                step_timings={}
            )
            
            # Execute Teams notification step
            context = await executor.orchestrator._step_5_teams_message(context)
            
            if context.teams_sent:
                print("✅ Teams notification sent successfully!")
                print("📱 Check your Teams channel for the workflow notification.")
            else:
                print("❌ Teams notification failed")
                if context.errors:
                    for error in context.errors:
                        if "Step 5" in error:
                            print(f"Error: {error}")
            
        except Exception as e:
            print(f"❌ Workflow test failed: {str(e)}")
    
    def display_completion(self):
        """Display completion message and next steps."""
        print()
        print("🎉 Teams Notification Setup Complete!")
        print("=" * 60)
        print()
        print("✅ Configuration saved to:", self.config_file)
        print("✅ Teams webhook tested successfully")
        print()
        print("🚀 Next Steps:")
        print("1. Run the full workflow:")
        print("   python scripts/execute_function_step_by_step.py --local-test")
        print()
        print("2. Check your Teams channel for notifications")
        print()
        print("3. For production deployment, add these environment variables:")
        print(f"   TEAMS_WEBHOOK_URL={self.webhook_url}")
        print("   TEAMS_NOTIFICATIONS_ENABLED=true")
        print()
        print("📚 For more configuration options, see:")
        print("   docs/teams-notification-setup.md")


async def main():
    """Main setup function."""
    setup = TeamsNotificationSetup()
    
    try:
        # Welcome and instructions
        setup.display_welcome()
        
        # Get webhook URL
        setup.get_webhook_url()
        
        # Update configuration
        if not setup.update_config_file():
            print("❌ Setup failed: Could not update configuration")
            return
        
        # Test webhook
        webhook_test_passed = await setup.test_webhook()
        
        if webhook_test_passed:
            # Run workflow test
            await setup.run_workflow_test()
            
            # Display completion
            setup.display_completion()
        else:
            print()
            print("⚠️  Webhook test failed. Please check:")
            print("1. The webhook URL is correct")
            print("2. The Teams channel still exists")
            print("3. The webhook hasn't been deleted")
            print()
            print("You can still proceed, but Teams notifications may not work.")
    
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")


if __name__ == "__main__":
    print("🔧 AutoDefectTriage Teams Notification Setup")
    print()
    
    asyncio.run(main())
