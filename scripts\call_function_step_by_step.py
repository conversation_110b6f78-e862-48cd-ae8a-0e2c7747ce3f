#!/usr/bin/env python3
"""
Call Azure Function Step by Step
================================

This script calls the actual Azure Function endpoint and shows the step-by-step execution.

Usage:
    python call_function_step_by_step.py --url https://your-function-app.azurewebsites.net --work-item 12345
    python call_function_step_by_step.py --local --work-item 12345
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any


class FunctionStepByStepCaller:
    """Calls the Azure Function and tracks step-by-step execution."""
    
    def __init__(self, function_url: str, function_key: str = None):
        self.function_url = function_url.rstrip('/')
        self.function_key = function_key
        self.workflow_endpoint = f"{self.function_url}/api/step_by_step_workflow"
    
    async def execute_workflow_with_tracking(self, work_item_id: str) -> Dict[str, Any]:
        """Execute the workflow and track each step."""
        
        print("🚀 CALLING AZURE FUNCTION STEP-BY-STEP WORKFLOW")
        print("=" * 80)
        print(f"📝 Work Item ID: {work_item_id}")
        print(f"🌐 Function URL: {self.function_url}")
        print(f"📡 Endpoint: {self.workflow_endpoint}")
        print(f"🕐 Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print()
        
        # Prepare request
        payload = {"work_item_id": work_item_id}
        headers = {"Content-Type": "application/json"}
        
        if self.function_key:
            headers["x-functions-key"] = self.function_key
        
        print("📤 SENDING REQUEST TO AZURE FUNCTION")
        print("-" * 50)
        print(f"📋 Payload: {json.dumps(payload, indent=2)}")
        print(f"🔑 Headers: {list(headers.keys())}")
        print()
        
        start_time = time.time()
        
        try:
            async with aiohttp.ClientSession() as session:
                print("🔄 Making HTTP request to Azure Function...")
                
                async with session.post(
                    self.workflow_endpoint,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5 minute timeout
                ) as response:
                    
                    request_time = time.time() - start_time
                    
                    print(f"📥 Response received!")
                    print(f"   📊 HTTP Status: {response.status}")
                    print(f"   ⏱️  Request time: {request_time:.2f} seconds")
                    print()
                    
                    if response.status == 200:
                        result = await response.json()
                        self._display_step_by_step_results(result)
                        return result
                    else:
                        error_text = await response.text()
                        print(f"❌ Function call failed!")
                        print(f"   Status: {response.status}")
                        print(f"   Error: {error_text}")
                        return {"error": f"HTTP {response.status}: {error_text}"}
        
        except asyncio.TimeoutError:
            print("❌ Request timed out after 5 minutes")
            return {"error": "Request timeout"}
        except Exception as e:
            print(f"❌ Request failed: {str(e)}")
            return {"error": str(e)}
    
    def _display_step_by_step_results(self, result: Dict[str, Any]):
        """Display the step-by-step results from the function."""
        
        print("📊 AZURE FUNCTION EXECUTION RESULTS")
        print("=" * 80)
        
        # Basic info
        print(f"📋 **BASIC INFORMATION**")
        print(f"   Work Item ID: {result.get('work_item_id', 'N/A')}")
        print(f"   Success: {'✅ YES' if result.get('success') else '❌ NO'}")
        print(f"   Processing Mode: {result.get('processing_mode', 'N/A')}")
        print(f"   Total Execution Time: {result.get('total_time', 0):.2f}s")
        print()
        
        # Step-by-step breakdown
        step_timings = result.get('step_timings', {})
        if step_timings:
            print(f"⏱️  **STEP-BY-STEP EXECUTION TIMES**")
            
            step_names = {
                'step_1_work_item_creation': '🔍 Step 1: Work Item Creation/Retrieval',
                'step_2_historical_analysis': '📊 Step 2: Historical Analysis & Pattern Recognition',
                'step_3_ai_triage_and_message_generation': '🤖 Step 3: AI Triage & Message Generation',
                'step_4_email_notification': '📧 Step 4: Email Notification Delivery',
                'step_5_teams_message': '💬 Step 5: Teams Message Broadcasting'
            }
            
            for step_key, step_name in step_names.items():
                timing = step_timings.get(step_key, 0)
                print(f"   {step_name}: {timing:.2f}s")
            print()
        
        # Workflow steps executed
        workflow_steps = result.get('workflow_steps', {})
        if workflow_steps:
            print(f"📋 **WORKFLOW STEPS EXECUTED**")
            for step_key, step_description in workflow_steps.items():
                print(f"   {step_key}: {step_description}")
            print()
        
        # Notification delivery
        print(f"📬 **NOTIFICATION DELIVERY STATUS**")
        print(f"   📧 Email Sent: {'✅ YES' if result.get('email_sent') else '❌ NO'}")
        print(f"   💬 Teams Sent: {'✅ YES' if result.get('teams_sent') else '❌ NO'}")
        print()
        
        # AI Triage Results
        triage_result = result.get('triage_result')
        if triage_result:
            print(f"🤖 **AI TRIAGE RESULTS**")
            print(f"   👤 Assigned To: {triage_result.get('assigned_to', 'N/A')}")
            print(f"   📊 Priority: {triage_result.get('priority', 'N/A')}")
            print(f"   🎯 Confidence Score: {triage_result.get('confidence_score', 0):.2f}")
            print(f"   🔍 Duplicates Found: {len(triage_result.get('duplicates', []))}")
            
            reasoning = triage_result.get('reasoning', '')
            if reasoning:
                reasoning_preview = reasoning[:150] + "..." if len(reasoning) > 150 else reasoning
                print(f"   💭 Reasoning: {reasoning_preview}")
            print()
        
        # Errors
        errors = result.get('errors', [])
        if errors:
            print(f"❌ **ERRORS ENCOUNTERED ({len(errors)})**")
            for i, error in enumerate(errors, 1):
                print(f"   {i}. {error}")
            print()
        
        # Final status
        if result.get('success'):
            print(f"🎉 **WORKFLOW COMPLETED SUCCESSFULLY!**")
            print(f"   ✅ All steps executed")
            print(f"   📧 Email notification delivered")
            print(f"   💬 Teams message sent")
        else:
            print(f"⚠️  **WORKFLOW COMPLETED WITH ISSUES**")
            print(f"   ❌ Some steps failed")
            print(f"   📧 Email: {'✅' if result.get('email_sent') else '❌'}")
            print(f"   💬 Teams: {'✅' if result.get('teams_sent') else '❌'}")


async def call_local_function(work_item_id: str):
    """Call the function running locally."""
    print("🏠 CALLING LOCAL AZURE FUNCTION")
    print("=" * 50)
    
    # Assuming local function runs on default port
    local_url = "http://localhost:7071"
    caller = FunctionStepByStepCaller(local_url)
    
    result = await caller.execute_workflow_with_tracking(work_item_id)
    return result


async def call_deployed_function(function_url: str, function_key: str, work_item_id: str):
    """Call the deployed Azure Function."""
    print("☁️  CALLING DEPLOYED AZURE FUNCTION")
    print("=" * 50)
    
    caller = FunctionStepByStepCaller(function_url, function_key)
    result = await caller.execute_workflow_with_tracking(work_item_id)
    return result


async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Call Azure Function Step by Step")
    parser.add_argument("--work-item", type=str, required=True, help="Work item ID to process")
    parser.add_argument("--url", type=str, help="Azure Function app URL")
    parser.add_argument("--key", type=str, help="Function key for authentication")
    parser.add_argument("--local", action="store_true", help="Call local function (localhost:7071)")
    
    args = parser.parse_args()
    
    try:
        if args.local:
            result = await call_local_function(args.work_item)
        elif args.url:
            result = await call_deployed_function(args.url, args.key, args.work_item)
        else:
            print("❌ Please specify either --local or --url")
            parser.print_help()
            return
        
        print("\n" + "=" * 80)
        print("✨ Function call completed!")
        
        if result.get('success'):
            print("🎯 Result: SUCCESS")
        else:
            print("⚠️  Result: PARTIAL SUCCESS or FAILURE")
            
    except Exception as e:
        print(f"❌ Execution failed: {str(e)}")
        raise


if __name__ == "__main__":
    print("📞 Azure Function Step-by-Step Caller")
    print("This script calls the actual Azure Function and shows step-by-step execution.")
    print()
    
    asyncio.run(main())
