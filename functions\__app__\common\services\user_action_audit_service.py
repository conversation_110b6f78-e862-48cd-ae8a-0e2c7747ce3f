"""
User Action Audit Service
Enhanced audit service specifically for tracking user actions and responses on defects.
"""

import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

from ..utils.config import Config
from ..utils.logging import log_structured, log_business_event
from ..notifications.audit_service import NotificationAuditService

logger = logging.getLogger(__name__)


class UserActionType(Enum):
    """Types of user actions that can be tracked."""
    FEEDBACK_SUBMITTED = "feedback_submitted"
    PRIORITY_CHANGED = "priority_changed"
    ASSIGNMENT_ACCEPTED = "assignment_accepted"
    ASSIGNMENT_REJECTED = "assignment_rejected"
    DUPLICATE_CONFIRMED = "duplicate_confirmed"
    DUPLICATE_REJECTED = "duplicate_rejected"
    ESCALATION_REQUESTED = "escalation_requested"
    QUICK_ACCEPT = "quick_accept"
    REASSIGNMENT_REQUESTED = "reassignment_requested"
    WORK_ITEM_VIEWED = "work_item_viewed"
    CARD_INTERACTION = "card_interaction"
    RESPONSE_PROVIDED = "response_provided"


class ActionSource(Enum):
    """Source of the user action."""
    TEAMS_ADAPTIVE_CARD = "teams_adaptive_card"
    TEAMS_QUICK_ACTION = "teams_quick_action"
    LOGIC_APP = "logic_app"
    EMAIL_REPLY = "email_reply"
    ADO_DIRECT = "ado_direct"
    WEBHOOK = "webhook"
    API = "api"


class ActionOutcome(Enum):
    """Outcome of the user action."""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    PENDING = "pending"
    CANCELLED = "cancelled"


@dataclass
class UserActionAudit:
    """Audit record for user actions."""
    action_id: str
    work_item_id: int
    user_email: str
    user_name: str
    action_type: UserActionType
    action_source: ActionSource
    action_data: Dict[str, Any]
    timestamp: datetime
    outcome: ActionOutcome = ActionOutcome.PENDING
    processing_duration_ms: Optional[float] = None
    error_message: Optional[str] = None
    response_data: Optional[Dict[str, Any]] = None
    related_notification_id: Optional[str] = None
    session_id: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    additional_metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UserActionMetrics:
    """Metrics for user actions over a time period."""
    period_start: datetime
    period_end: datetime
    total_actions: int = 0
    successful_actions: int = 0
    failed_actions: int = 0
    avg_processing_time_ms: float = 0.0
    actions_by_type: Dict[str, int] = field(default_factory=dict)
    actions_by_source: Dict[str, int] = field(default_factory=dict)
    actions_by_user: Dict[str, int] = field(default_factory=dict)
    most_active_work_items: List[Tuple[int, int]] = field(default_factory=list)
    response_rate: float = 0.0
    engagement_score: float = 0.0


class UserActionAuditService:
    """Service for auditing user actions and responses."""
    
    def __init__(self, config: Config):
        self.config = config
        self.notification_audit = NotificationAuditService(config)
        self._action_records: Dict[str, UserActionAudit] = {}
        self._session_tracking: Dict[str, List[str]] = {}
    
    def record_user_action(
        self,
        work_item_id: int,
        user_email: str,
        user_name: str,
        action_type: UserActionType,
        action_source: ActionSource,
        action_data: Dict[str, Any],
        session_id: Optional[str] = None,
        related_notification_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> str:
        """
        Record a user action.
        
        Args:
            work_item_id: The work item ID
            user_email: User's email address
            user_name: User's display name
            action_type: Type of action performed
            action_source: Source of the action
            action_data: Data associated with the action
            session_id: Optional session identifier
            related_notification_id: Related notification ID if applicable
            ip_address: User's IP address
            user_agent: User's browser/client information
            
        Returns:
            Action ID for tracking
        """
        action_id = str(uuid.uuid4())
        
        audit_record = UserActionAudit(
            action_id=action_id,
            work_item_id=work_item_id,
            user_email=user_email,
            user_name=user_name,
            action_type=action_type,
            action_source=action_source,
            action_data=action_data,
            timestamp=datetime.utcnow(),
            related_notification_id=related_notification_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self._action_records[action_id] = audit_record
        
        # Track session if provided
        if session_id:
            if session_id not in self._session_tracking:
                self._session_tracking[session_id] = []
            self._session_tracking[session_id].append(action_id)
        
        # Log structured event
        log_structured(
            logger,
            "info",
            f"User action recorded: {action_type.value}",
            extra={
                "action_id": action_id,
                "work_item_id": work_item_id,
                "user_email": user_email,
                "user_name": user_name,
                "action_type": action_type.value,
                "action_source": action_source.value,
                "session_id": session_id,
                "related_notification_id": related_notification_id
            }
        )
        
        # Log business event
        log_business_event(
            logger,
            "user_action_recorded",
            {
                "action_id": action_id,
                "work_item_id": work_item_id,
                "action_type": action_type.value,
                "action_source": action_source.value,
                "user_email": user_email,
                "timestamp": audit_record.timestamp.isoformat()
            },
            user_id=user_email
        )
        
        return action_id
    
    def update_action_outcome(
        self,
        action_id: str,
        outcome: ActionOutcome,
        processing_duration_ms: Optional[float] = None,
        error_message: Optional[str] = None,
        response_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Update the outcome of a user action.
        
        Args:
            action_id: The action ID
            outcome: The outcome of the action
            processing_duration_ms: Processing time in milliseconds
            error_message: Error message if failed
            response_data: Response data from processing
            
        Returns:
            True if updated successfully
        """
        try:
            if action_id not in self._action_records:
                logger.warning(f"Action record not found for action {action_id}")
                return False
            
            audit_record = self._action_records[action_id]
            audit_record.outcome = outcome
            audit_record.processing_duration_ms = processing_duration_ms
            audit_record.error_message = error_message
            audit_record.response_data = response_data
            
            log_structured(
                logger,
                "info",
                f"Action outcome updated: {outcome.value}",
                extra={
                    "action_id": action_id,
                    "work_item_id": audit_record.work_item_id,
                    "action_type": audit_record.action_type.value,
                    "outcome": outcome.value,
                    "processing_duration_ms": processing_duration_ms
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error updating action outcome: {e}",
                extra={"action_id": action_id},
                exc_info=True
            )
            return False
    
    def get_action_record(self, action_id: str) -> Optional[UserActionAudit]:
        """Get action record by ID."""
        return self._action_records.get(action_id)
    
    def get_actions_for_work_item(self, work_item_id: int) -> List[UserActionAudit]:
        """Get all actions for a work item."""
        return [
            record for record in self._action_records.values()
            if record.work_item_id == work_item_id
        ]
    
    def get_actions_for_user(self, user_email: str) -> List[UserActionAudit]:
        """Get all actions for a user."""
        return [
            record for record in self._action_records.values()
            if record.user_email == user_email
        ]
    
    def get_session_actions(self, session_id: str) -> List[UserActionAudit]:
        """Get all actions in a session."""
        if session_id not in self._session_tracking:
            return []
        
        action_ids = self._session_tracking[session_id]
        return [
            self._action_records[action_id] for action_id in action_ids
            if action_id in self._action_records
        ]

    def calculate_user_action_metrics(
        self,
        start_time: datetime,
        end_time: datetime
    ) -> UserActionMetrics:
        """
        Calculate user action metrics for a time period.

        Args:
            start_time: Start of time period
            end_time: End of time period

        Returns:
            User action metrics
        """
        try:
            # Filter records by time period
            period_records = [
                record for record in self._action_records.values()
                if start_time <= record.timestamp <= end_time
            ]

            if not period_records:
                return UserActionMetrics(
                    period_start=start_time,
                    period_end=end_time
                )

            # Calculate basic metrics
            total_actions = len(period_records)
            successful_actions = len([r for r in period_records if r.outcome == ActionOutcome.SUCCESS])
            failed_actions = len([r for r in period_records if r.outcome == ActionOutcome.FAILED])

            # Calculate average processing time
            processing_times = [
                r.processing_duration_ms for r in period_records
                if r.processing_duration_ms is not None
            ]
            avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0.0

            # Group by action type
            actions_by_type = {}
            for record in period_records:
                action_type = record.action_type.value
                actions_by_type[action_type] = actions_by_type.get(action_type, 0) + 1

            # Group by source
            actions_by_source = {}
            for record in period_records:
                source = record.action_source.value
                actions_by_source[source] = actions_by_source.get(source, 0) + 1

            # Group by user
            actions_by_user = {}
            for record in period_records:
                user = record.user_email
                actions_by_user[user] = actions_by_user.get(user, 0) + 1

            # Find most active work items
            work_item_counts = {}
            for record in period_records:
                work_item_id = record.work_item_id
                work_item_counts[work_item_id] = work_item_counts.get(work_item_id, 0) + 1

            most_active_work_items = sorted(
                work_item_counts.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]  # Top 10

            # Calculate response rate (actions vs notifications sent)
            # This would need integration with notification audit service
            response_rate = 0.0  # Placeholder

            # Calculate engagement score (weighted by action types)
            engagement_weights = {
                UserActionType.FEEDBACK_SUBMITTED: 3.0,
                UserActionType.PRIORITY_CHANGED: 2.5,
                UserActionType.ASSIGNMENT_ACCEPTED: 2.0,
                UserActionType.ASSIGNMENT_REJECTED: 2.0,
                UserActionType.QUICK_ACCEPT: 1.5,
                UserActionType.CARD_INTERACTION: 1.0,
                UserActionType.WORK_ITEM_VIEWED: 0.5
            }

            total_engagement_score = 0.0
            for record in period_records:
                weight = engagement_weights.get(record.action_type, 1.0)
                total_engagement_score += weight

            engagement_score = total_engagement_score / total_actions if total_actions > 0 else 0.0

            return UserActionMetrics(
                period_start=start_time,
                period_end=end_time,
                total_actions=total_actions,
                successful_actions=successful_actions,
                failed_actions=failed_actions,
                avg_processing_time_ms=avg_processing_time,
                actions_by_type=actions_by_type,
                actions_by_source=actions_by_source,
                actions_by_user=actions_by_user,
                most_active_work_items=most_active_work_items,
                response_rate=response_rate,
                engagement_score=engagement_score
            )

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error calculating user action metrics: {e}",
                exc_info=True
            )
            return UserActionMetrics(
                period_start=start_time,
                period_end=end_time
            )

    def generate_user_engagement_report(self, user_email: str, days_back: int = 30) -> Dict[str, Any]:
        """
        Generate a user engagement report.

        Args:
            user_email: User's email address
            days_back: Number of days to look back

        Returns:
            User engagement report
        """
        try:
            end_time = datetime.utcnow()
            start_time = end_time - timedelta(days=days_back)

            user_actions = [
                record for record in self._action_records.values()
                if record.user_email == user_email and start_time <= record.timestamp <= end_time
            ]

            if not user_actions:
                return {
                    "user_email": user_email,
                    "period_days": days_back,
                    "total_actions": 0,
                    "engagement_level": "none"
                }

            # Calculate engagement metrics
            total_actions = len(user_actions)
            unique_work_items = len(set(record.work_item_id for record in user_actions))
            action_types = set(record.action_type.value for record in user_actions)

            # Determine engagement level
            if total_actions >= 20:
                engagement_level = "high"
            elif total_actions >= 10:
                engagement_level = "medium"
            elif total_actions >= 5:
                engagement_level = "low"
            else:
                engagement_level = "minimal"

            # Calculate response patterns
            response_patterns = {}
            for record in user_actions:
                day_of_week = record.timestamp.strftime("%A")
                hour_of_day = record.timestamp.hour

                if day_of_week not in response_patterns:
                    response_patterns[day_of_week] = 0
                response_patterns[day_of_week] += 1

            return {
                "user_email": user_email,
                "period_days": days_back,
                "total_actions": total_actions,
                "unique_work_items_interacted": unique_work_items,
                "action_types_used": list(action_types),
                "engagement_level": engagement_level,
                "response_patterns": response_patterns,
                "most_recent_action": max(user_actions, key=lambda x: x.timestamp).timestamp.isoformat(),
                "avg_actions_per_day": total_actions / days_back
            }

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error generating user engagement report: {e}",
                extra={"user_email": user_email},
                exc_info=True
            )
            return {"error": str(e)}

    def cleanup_old_action_records(self, retention_days: int = 90) -> int:
        """
        Clean up old action records.

        Args:
            retention_days: Number of days to retain records

        Returns:
            Number of records cleaned up
        """
        cutoff_date = datetime.utcnow() - timedelta(days=retention_days)

        old_records = [
            action_id for action_id, record in self._action_records.items()
            if record.timestamp < cutoff_date
        ]

        # Clean up action records
        for action_id in old_records:
            del self._action_records[action_id]

        # Clean up session tracking
        sessions_to_remove = []
        for session_id, action_ids in self._session_tracking.items():
            # Remove old action IDs from session
            self._session_tracking[session_id] = [
                aid for aid in action_ids if aid not in old_records
            ]
            # Remove empty sessions
            if not self._session_tracking[session_id]:
                sessions_to_remove.append(session_id)

        for session_id in sessions_to_remove:
            del self._session_tracking[session_id]

        log_structured(
            logger,
            "info",
            f"Cleaned up {len(old_records)} old action records",
            extra={
                "retention_days": retention_days,
                "cutoff_date": cutoff_date.isoformat(),
                "records_cleaned": len(old_records),
                "sessions_cleaned": len(sessions_to_remove)
            }
        )

        return len(old_records)
