# QA AI Triage System - Operations Runbook

## Overview

The QA AI Triage system is an automated defect triage solution that uses AI to:
- Automatically assign work items to appropriate team members
- Detect potential duplicate work items
- Set appropriate priority levels
- Send notifications to Teams channels

## Architecture

```
Azure DevOps → Service Hook → Azure Functions → AI Processing → Updates & Notifications
                                     ↓
                              Azure AI Search ← Embeddings
```

## Environment Variables

### Required Configuration

| Variable | Description | Example |
|----------|-------------|---------|
| `ADO_ORGANIZATION` | Azure DevOps organization name | `mycompany` |
| `ADO_PROJECT` | Azure DevOps project name | `MyProject` |
| `ADO_PAT_TOKEN` | Personal Access Token for ADO | `***` |
| `AZURE_SEARCH_SERVICE_NAME` | Azure AI Search service name | `qa-ai-search` |
| `AZURE_SEARCH_ADMIN_KEY` | Azure AI Search admin key | `***` |
| `KEY_VAULT_URL` | Azure Key Vault URL | `https://kv.vault.azure.net/` |

### Optional Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `EMBEDDING_PROVIDER` | `sentence_transformers` | Embedding provider (openai, azure_openai, sentence_transformers) |
| `DUPLICATE_SIMILARITY_THRESHOLD` | `0.85` | Minimum similarity for duplicate detection |
| `ASSIGNMENT_MIN_CONFIDENCE` | `0.6` | Minimum confidence for auto-assignment |
| `TEAMS_WEBHOOK_URL` | - | Teams webhook URL for notifications |

## Deployment

### Prerequisites

1. Azure subscription with appropriate permissions
2. Azure DevOps organization and project
3. Azure CLI installed and configured

### Infrastructure Deployment

```bash
# Deploy infrastructure
az deployment group create \
  --resource-group rg-qa-ai-triage \
  --template-file infra/bicep/main.bicep \
  --parameters @infra/bicep/parameters.prod.json
```

### Function App Deployment

```bash
# Build and deploy function app
cd functions
func azure functionapp publish qa-ai-triage-prod
```

### Azure DevOps Service Hook Setup

1. Go to Project Settings → Service hooks
2. Create new subscription
3. Select "Web Hooks" service
4. Configure:
   - Event: Work item created/updated
   - Filters: Work item type = Bug, Task, User Story, Feature
   - URL: `https://qa-ai-triage-prod.azurewebsites.net/api/workitem_created`

## Monitoring

### Application Insights Queries

#### Function Execution Success Rate
```kusto
requests
| where timestamp > ago(24h)
| where name == "workitem_created"
| summarize 
    Total = count(),
    Success = countif(success == true),
    SuccessRate = round(100.0 * countif(success == true) / count(), 2)
by bin(timestamp, 1h)
```

#### Assignment Accuracy
```kusto
traces
| where timestamp > ago(24h)
| where message contains "Work item assigned"
| extend assignee = tostring(customDimensions.assignee)
| extend confidence = todouble(customDimensions.confidence)
| summarize 
    Count = count(),
    AvgConfidence = avg(confidence)
by assignee
```

#### Duplicate Detection Performance
```kusto
traces
| where timestamp > ago(24h)
| where message contains "duplicates found"
| extend duplicate_count = toint(customDimensions.duplicate_count)
| summarize 
    TotalItems = count(),
    ItemsWithDuplicates = countif(duplicate_count > 0),
    AvgDuplicatesPerItem = avg(duplicate_count)
```

### Key Metrics to Monitor

1. **Function Success Rate**: Should be > 95%
2. **Assignment Coverage**: Percentage of items auto-assigned
3. **Assignment Accuracy**: Measured through feedback
4. **Duplicate Detection Precision**: False positive rate
5. **Processing Time**: Average time per work item

## Troubleshooting

### Common Issues

#### 1. Function Not Triggering

**Symptoms**: No logs in Application Insights when work items are created

**Diagnosis**:
```bash
# Check service hook configuration
curl -X POST https://qa-ai-triage-prod.azurewebsites.net/api/workitem_created \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

**Solutions**:
- Verify service hook URL is correct
- Check function app is running
- Verify ADO service hook filters

#### 2. Assignment Not Working

**Symptoms**: Work items not being assigned automatically

**Diagnosis**:
```kusto
traces
| where timestamp > ago(1h)
| where message contains "No assignment candidates"
```

**Solutions**:
- Check if search index is populated
- Verify CODEOWNERS file is accessible
- Review assignment confidence thresholds

#### 3. High Error Rate

**Symptoms**: Many failed function executions

**Diagnosis**:
```kusto
exceptions
| where timestamp > ago(1h)
| where operation_Name == "workitem_created"
| summarize count() by type, outerMessage
```

**Solutions**:
- Check Azure service connectivity
- Verify configuration values
- Review Key Vault access permissions

### Performance Optimization

#### 1. Slow Processing

- **Embedding Generation**: Consider caching embeddings
- **Search Queries**: Optimize search index schema
- **Batch Processing**: Process multiple items together

#### 2. High Memory Usage

- **Model Loading**: Use lazy loading for ML models
- **Data Caching**: Implement TTL for cached data
- **Connection Pooling**: Reuse HTTP connections

## Maintenance

### Regular Tasks

#### Daily
- Monitor function execution success rate
- Review assignment accuracy metrics
- Check for any failed notifications

#### Weekly
- Analyze duplicate detection performance
- Review and update CODEOWNERS mappings
- Check search index health

#### Monthly
- Run full evaluation scripts
- Update ML model thresholds based on performance
- Review and optimize configuration

### Updates and Rollbacks

#### Deploying Updates
```bash
# Deploy to staging slot first
func azure functionapp publish qa-ai-triage-prod --slot staging

# Test staging deployment
curl https://qa-ai-triage-prod-staging.azurewebsites.net/api/health

# Swap to production
az functionapp deployment slot swap \
  --resource-group rg-qa-ai-triage \
  --name qa-ai-triage-prod \
  --slot staging
```

#### Emergency Rollback
```bash
# Swap back to previous version
az functionapp deployment slot swap \
  --resource-group rg-qa-ai-triage \
  --name qa-ai-triage-prod \
  --slot staging
```

## Security

### Access Control
- Function app uses managed identity
- Key Vault stores sensitive configuration
- ADO PAT token has minimal required permissions

### Data Protection
- All data in transit is encrypted (HTTPS)
- Search index data is encrypted at rest
- No sensitive data is logged

### Compliance
- GDPR: No personal data is permanently stored
- SOC 2: Audit logs are maintained in Application Insights
- ISO 27001: Security controls are documented

## Disaster Recovery

### Backup Strategy
- Infrastructure: ARM templates in source control
- Configuration: Key Vault with geo-replication
- Data: Search index can be rebuilt from ADO

### Recovery Procedures

#### Complete Service Outage
1. Deploy infrastructure to secondary region
2. Restore configuration from Key Vault backup
3. Rebuild search index using backfill job
4. Update ADO service hook URLs

#### Partial Service Degradation
1. Scale out function app instances
2. Increase search service tier if needed
3. Implement circuit breaker for external dependencies

## Contact Information

- **Primary On-Call**: DevOps Team (<EMAIL>)
- **Secondary**: AI/ML Team (<EMAIL>)
- **Escalation**: Engineering Manager (<EMAIL>)

## Useful Links

- [Azure Portal](https://portal.azure.com)
- [Application Insights Dashboard](https://portal.azure.com/#@tenant/resource/subscriptions/sub/resourceGroups/rg/providers/microsoft.insights/components/ai)
- [Azure DevOps Project](https://dev.azure.com/org/project)
- [Source Code Repository](https://github.com/company/qa-ai-triage)
