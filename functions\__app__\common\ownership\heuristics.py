"""
Ownership heuristics for fallback assignment rules.
"""

import logging
from typing import List, Dict, Any, Optional
import re
from datetime import datetime, timedelta

from ..models.schemas import WorkItem
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class OwnershipHeuristics:
    """Heuristic rules for determining work item ownership when CODEOWNERS doesn't match."""
    
    def __init__(self, config: Config):
        self.config = config
        self.team_mappings = self._load_team_mappings()
        self.keyword_rules = self._load_keyword_rules()
        self.priority_rules = self._load_priority_rules()
    
    def _load_team_mappings(self) -> Dict[str, List[str]]:
        """Load team member mappings."""
        # This would typically come from configuration or a database
        # For now, we'll use example mappings
        return {
            'frontend-team': [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ],
            'backend-team': [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ],
            'security-team': [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'data-team': [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'devops-team': [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'qa-team': [
                '<EMAIL>',
                '<EMAIL>'
            ],
            'docs-team': [
                '<EMAIL>',
                '<EMAIL>'
            ]
        }
    
    def _load_keyword_rules(self) -> Dict[str, Dict[str, Any]]:
        """Load keyword-based assignment rules."""
        return {
            'ui_keywords': {
                'keywords': [
                    'ui', 'user interface', 'frontend', 'react', 'angular', 'vue',
                    'css', 'html', 'javascript', 'typescript', 'component',
                    'button', 'form', 'modal', 'dialog', 'layout', 'styling'
                ],
                'teams': ['frontend-team'],
                'confidence': 0.8
            },
            'api_keywords': {
                'keywords': [
                    'api', 'rest', 'graphql', 'endpoint', 'service', 'backend',
                    'server', 'microservice', 'controller', 'route', 'middleware'
                ],
                'teams': ['backend-team'],
                'confidence': 0.8
            },
            'database_keywords': {
                'keywords': [
                    'database', 'sql', 'query', 'table', 'schema', 'migration',
                    'orm', 'entity', 'repository', 'connection', 'transaction'
                ],
                'teams': ['data-team', 'backend-team'],
                'confidence': 0.7
            },
            'security_keywords': {
                'keywords': [
                    'security', 'authentication', 'authorization', 'login',
                    'password', 'token', 'jwt', 'oauth', 'ssl', 'tls',
                    'encryption', 'vulnerability', 'xss', 'csrf'
                ],
                'teams': ['security-team'],
                'confidence': 0.9
            },
            'infrastructure_keywords': {
                'keywords': [
                    'deployment', 'infrastructure', 'docker', 'kubernetes',
                    'azure', 'aws', 'cloud', 'pipeline', 'ci/cd', 'devops',
                    'monitoring', 'logging', 'metrics'
                ],
                'teams': ['devops-team'],
                'confidence': 0.8
            },
            'testing_keywords': {
                'keywords': [
                    'test', 'testing', 'unit test', 'integration test',
                    'e2e', 'automation', 'selenium', 'cypress', 'jest',
                    'mocha', 'pytest', 'coverage'
                ],
                'teams': ['qa-team'],
                'confidence': 0.7
            },
            'documentation_keywords': {
                'keywords': [
                    'documentation', 'docs', 'readme', 'wiki', 'guide',
                    'tutorial', 'help', 'manual', 'specification'
                ],
                'teams': ['docs-team'],
                'confidence': 0.6
            }
        }
    
    def _load_priority_rules(self) -> List[Dict[str, Any]]:
        """Load priority-based assignment rules."""
        return [
            {
                'condition': 'critical_security',
                'keywords': ['security', 'vulnerability', 'breach', 'exploit'],
                'priority_threshold': 1,
                'teams': ['security-team'],
                'confidence': 0.95
            },
            {
                'condition': 'production_issue',
                'keywords': ['production', 'live', 'customer', 'outage'],
                'priority_threshold': 2,
                'teams': ['backend-team', 'devops-team'],
                'confidence': 0.85
            },
            {
                'condition': 'performance_issue',
                'keywords': ['slow', 'performance', 'timeout', 'memory'],
                'priority_threshold': 2,
                'teams': ['backend-team'],
                'confidence': 0.8
            }
        ]
    
    async def suggest_assignees(self, work_item: WorkItem) -> List[Dict[str, Any]]:
        """
        Suggest assignees based on heuristic rules.
        
        Args:
            work_item: The work item to analyze
        
        Returns:
            List of assignment suggestions with reasoning
        """
        try:
            suggestions = []
            
            # Combine text for analysis
            text_content = self._combine_work_item_text(work_item)
            
            # Apply keyword-based rules
            keyword_suggestions = self._apply_keyword_rules(text_content)
            suggestions.extend(keyword_suggestions)
            
            # Apply priority-based rules
            priority_suggestions = self._apply_priority_rules(work_item, text_content)
            suggestions.extend(priority_suggestions)
            
            # Apply area path rules
            area_suggestions = self._apply_area_path_rules(work_item.area_path)
            suggestions.extend(area_suggestions)
            
            # Apply work item type rules
            type_suggestions = self._apply_work_item_type_rules(work_item.work_item_type)
            suggestions.extend(type_suggestions)
            
            # Deduplicate and rank suggestions
            ranked_suggestions = self._rank_suggestions(suggestions)
            
            log_structured(
                logger,
                "info",
                "Generated heuristic assignment suggestions",
                extra={
                    "work_item_id": work_item.id,
                    "suggestion_count": len(ranked_suggestions)
                }
            )
            
            return ranked_suggestions
            
        except Exception as e:
            logger.error(f"Error generating heuristic suggestions for work item {work_item.id}: {e}")
            return []
    
    def _combine_work_item_text(self, work_item: WorkItem) -> str:
        """Combine work item text fields for analysis."""
        text_parts = []
        
        if work_item.title:
            text_parts.append(work_item.title)
        if work_item.description:
            text_parts.append(work_item.description)
        if work_item.repro_steps:
            text_parts.append(work_item.repro_steps)
        if work_item.tags:
            text_parts.append(work_item.tags)
        
        return " ".join(text_parts).lower()
    
    def _apply_keyword_rules(self, text_content: str) -> List[Dict[str, Any]]:
        """Apply keyword-based assignment rules."""
        suggestions = []
        
        for rule_name, rule in self.keyword_rules.items():
            keyword_matches = 0
            matched_keywords = []
            
            for keyword in rule['keywords']:
                if keyword in text_content:
                    keyword_matches += 1
                    matched_keywords.append(keyword)
            
            if keyword_matches > 0:
                # Calculate confidence based on keyword matches
                confidence = rule['confidence'] * (keyword_matches / len(rule['keywords']))
                confidence = min(confidence, 1.0)
                
                for team in rule['teams']:
                    if team in self.team_mappings:
                        for assignee in self.team_mappings[team]:
                            suggestions.append({
                                'assignee': assignee,
                                'confidence': confidence,
                                'reason': f"Keyword match: {', '.join(matched_keywords[:3])}",
                                'rule_type': 'keyword',
                                'rule_name': rule_name
                            })
        
        return suggestions
    
    def _apply_priority_rules(self, work_item: WorkItem, text_content: str) -> List[Dict[str, Any]]:
        """Apply priority-based assignment rules."""
        suggestions = []
        
        for rule in self.priority_rules:
            # Check if priority meets threshold
            if work_item.priority and work_item.priority <= rule['priority_threshold']:
                # Check for keyword matches
                keyword_matches = sum(1 for keyword in rule['keywords'] if keyword in text_content)
                
                if keyword_matches > 0:
                    for team in rule['teams']:
                        if team in self.team_mappings:
                            for assignee in self.team_mappings[team]:
                                suggestions.append({
                                    'assignee': assignee,
                                    'confidence': rule['confidence'],
                                    'reason': f"Priority rule: {rule['condition']}",
                                    'rule_type': 'priority',
                                    'rule_name': rule['condition']
                                })
        
        return suggestions
    
    def _apply_area_path_rules(self, area_path: Optional[str]) -> List[Dict[str, Any]]:
        """Apply area path-based assignment rules."""
        suggestions = []
        
        if not area_path:
            return suggestions
        
        area_lower = area_path.lower()
        
        # Area path to team mappings
        area_mappings = {
            'authentication': ['security-team'],
            'auth': ['security-team'],
            'security': ['security-team'],
            'payment': ['backend-team'],
            'billing': ['backend-team'],
            'api': ['backend-team'],
            'service': ['backend-team'],
            'ui': ['frontend-team'],
            'frontend': ['frontend-team'],
            'web': ['frontend-team'],
            'database': ['data-team'],
            'data': ['data-team'],
            'infrastructure': ['devops-team'],
            'deployment': ['devops-team'],
            'testing': ['qa-team'],
            'test': ['qa-team'],
            'documentation': ['docs-team'],
            'docs': ['docs-team']
        }
        
        for area_keyword, teams in area_mappings.items():
            if area_keyword in area_lower:
                for team in teams:
                    if team in self.team_mappings:
                        for assignee in self.team_mappings[team]:
                            suggestions.append({
                                'assignee': assignee,
                                'confidence': 0.7,
                                'reason': f"Area path match: {area_keyword}",
                                'rule_type': 'area_path',
                                'rule_name': area_keyword
                            })
        
        return suggestions
    
    def _apply_work_item_type_rules(self, work_item_type: str) -> List[Dict[str, Any]]:
        """Apply work item type-based assignment rules."""
        suggestions = []
        
        if not work_item_type:
            return suggestions
        
        type_lower = work_item_type.lower()
        
        # Work item type to team mappings
        type_mappings = {
            'bug': ['qa-team', 'backend-team'],
            'defect': ['qa-team', 'backend-team'],
            'task': ['backend-team'],
            'user story': ['frontend-team', 'backend-team'],
            'feature': ['frontend-team', 'backend-team'],
            'epic': ['backend-team'],
            'test case': ['qa-team'],
            'test plan': ['qa-team']
        }
        
        for type_keyword, teams in type_mappings.items():
            if type_keyword in type_lower:
                for team in teams:
                    if team in self.team_mappings:
                        for assignee in self.team_mappings[team]:
                            suggestions.append({
                                'assignee': assignee,
                                'confidence': 0.5,
                                'reason': f"Work item type: {type_keyword}",
                                'rule_type': 'work_item_type',
                                'rule_name': type_keyword
                            })
        
        return suggestions
    
    def _rank_suggestions(self, suggestions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank and deduplicate suggestions."""
        # Group suggestions by assignee
        assignee_suggestions = {}
        
        for suggestion in suggestions:
            assignee = suggestion['assignee']
            if assignee not in assignee_suggestions:
                assignee_suggestions[assignee] = {
                    'assignee': assignee,
                    'total_confidence': 0.0,
                    'reasons': [],
                    'rule_types': set(),
                    'rule_names': set()
                }
            
            # Accumulate confidence (with diminishing returns)
            current_confidence = assignee_suggestions[assignee]['total_confidence']
            new_confidence = suggestion['confidence']
            
            # Use weighted average to prevent confidence from exceeding 1.0
            combined_confidence = (current_confidence + new_confidence) / 2
            assignee_suggestions[assignee]['total_confidence'] = min(combined_confidence, 1.0)
            
            # Collect reasons and rule information
            assignee_suggestions[assignee]['reasons'].append(suggestion['reason'])
            assignee_suggestions[assignee]['rule_types'].add(suggestion['rule_type'])
            assignee_suggestions[assignee]['rule_names'].add(suggestion['rule_name'])
        
        # Convert to final format and sort by confidence
        final_suggestions = []
        for assignee_data in assignee_suggestions.values():
            final_suggestions.append({
                'assignee': assignee_data['assignee'],
                'confidence': assignee_data['total_confidence'],
                'reason': '; '.join(assignee_data['reasons'][:3])  # Limit to top 3 reasons
            })
        
        # Sort by confidence (descending)
        final_suggestions.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Limit to top suggestions
        max_suggestions = self.config.get('HEURISTIC_MAX_SUGGESTIONS', 5)
        return final_suggestions[:max_suggestions]
