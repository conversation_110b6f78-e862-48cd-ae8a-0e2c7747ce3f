{"project_name": "sample-project", "enabled": true, "critical_priorities": [1, 2], "aging_thresholds": {"P1": "2h", "P2": "8h", "P3": "24h", "P4": "72h"}, "security_keywords": ["security", "vulnerability", "exploit", "breach", "cve", "injection", "xss", "csrf", "authentication", "authorization", "privilege escalation", "buffer overflow"], "customer_impact_keywords": ["customer", "production", "outage", "critical", "live", "downtime", "performance", "slow", "error", "failure", "revenue impact", "sla breach"], "team_channels": {"default": "https://outlook.office.com/webhook/...", "security": "https://outlook.office.com/webhook/...", "critical": "https://outlook.office.com/webhook/..."}, "code_owners": {"src/auth/": ["<EMAIL>", "<EMAIL>"], "src/payment/": ["<EMAIL>"], "src/api/": ["<EMAIL>"]}, "managers": ["<EMAIL>", "<EMAIL>"], "pagerduty_service_keys": {"critical": "your-pagerduty-integration-key-here", "security": "your-security-pagerduty-key-here"}, "rate_limits": {"per_user_per_hour": 3, "per_channel_per_hour": 15, "per_pagerduty_per_hour": 5}, "dedup_window_minutes": 45, "quiet_hours": {"enabled": true, "start_time": "20:00", "end_time": "08:00", "timezone": "America/New_York", "bypass_for_critical": true}, "notification_rules": [{"name": "Critical P1 Immediate", "trigger_types": ["CRITICAL_CREATED"], "priority_filter": [1], "enabled": true, "escalation_level": "CRITICAL", "recipient_types": ["USER", "TEAM_CHANNEL", "PAGERDUTY"], "delivery_methods": ["TEAMS_ADAPTIVE_CARD", "PAGERDUTY_INCIDENT"]}, {"name": "Security Alerts", "trigger_types": ["SECURITY_ALERT"], "enabled": true, "escalation_level": "URGENT", "recipient_types": ["TEAM_CHANNEL", "PAGERDUTY"], "delivery_methods": ["TEAMS_ADAPTIVE_CARD", "PAGERDUTY_INCIDENT"]}, {"name": "Aging P1/P2 Reminders", "trigger_types": ["AGING"], "priority_filter": [1, 2], "enabled": true, "escalation_level": "ELEVATED", "recipient_types": ["USER", "TEAM_CHANNEL"], "delivery_methods": ["TEAMS_ADAPTIVE_CARD"]}, {"name": "Assignment Suggestions", "trigger_types": ["ASSIGNMENT_SUGGESTION"], "enabled": true, "escalation_level": "NORMAL", "recipient_types": ["USER", "TEAM_CHANNEL"], "delivery_methods": ["TEAMS_ADAPTIVE_CARD"]}, {"name": "Duplicate Detection", "trigger_types": ["DUPLICATE_DETECTED"], "enabled": true, "escalation_level": "NORMAL", "recipient_types": ["USER"], "delivery_methods": ["TEAMS_ADAPTIVE_CARD"]}]}