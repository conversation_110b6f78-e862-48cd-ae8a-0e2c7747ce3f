# Step-by-Step Defect Triage Workflow

## Overview

The AutoDefectTriage system now includes a comprehensive step-by-step workflow that orchestrates the complete defect triage process from work item creation to final notifications. This workflow ensures consistent processing and provides detailed tracking of each step.

## Workflow Steps

### Step 1: Work Item Creation/Retrieval 🔍
**Purpose**: Capture and validate the work item that triggered the workflow

**Process**:
- Retrieves work item from Azure DevOps using the provided ID
- Validates work item data and converts to internal WorkItem object
- Extracts key fields: title, description, priority, assigned user, etc.
- Logs work item details for audit trail

**Output**: Validated WorkItem object ready for processing

---

### Step 2: Historical Analysis & Pattern Recognition 📊
**Purpose**: Analyze similar historical work items to provide context and insights

**Process**:
- Uses AI-powered duplicate detection to find similar historical items
- Analyzes patterns in resolution approaches and assignment history
- Extracts resolution times and success patterns
- Identifies potential duplicate work items

**Output**: List of similar historical items with similarity scores and resolution patterns

---

### Step 3: AI Triage & Message Generation 🤖
**Purpose**: Run complete AI analysis and generate intelligent notification content

**Process**:
- **Duplicate Detection**: Identifies potential duplicate work items
- **Priority Analysis**: Calculates appropriate priority level based on content and context
- **Assignment Engine**: Suggests optimal assignee based on expertise and workload
- **Message Generation**: Creates contextual notification message with:
  - Assignment reasoning and confidence scores
  - Historical context and similar items
  - Priority recommendations
  - Suggested next steps

**Output**: TriageResult with assignments, priorities, and AI-generated notification message

---

### Step 4: Email Notification Delivery 📧
**Purpose**: Send professional email notifications to stakeholders

**Process**:
- Prepares comprehensive email content including:
  - Work item details and AI analysis results
  - Historical context and similar items
  - Assignment recommendations with reasoning
  - Priority analysis and recommendations
- Triggers Logic App for email delivery with professional formatting
- Tracks email delivery status

**Output**: Email sent confirmation and delivery tracking

---

### Step 5: Teams Message Broadcasting 💬
**Purpose**: Send real-time Teams notifications with adaptive cards

**Process**:
- Creates Teams adaptive card with:
  - Work item summary and key details
  - AI triage results and recommendations
  - Assignment suggestions with confidence scores
  - Action buttons for quick responses
- Sends to configured Teams channels
- Tracks message delivery and user interactions

**Output**: Teams message sent confirmation and interaction tracking

## Usage

### Single Work Item Processing

Process a specific work item through the complete workflow:

```bash
# HTTP POST to the step-by-step workflow function
curl -X POST "https://your-function-app.azurewebsites.net/api/step_by_step_workflow" \
  -H "Content-Type: application/json" \
  -d '{
    "work_item_id": "12345"
  }'
```

### Batch Processing

Process all work items created in the last N hours:

```bash
# Process work items from last 24 hours
curl -X POST "https://your-function-app.azurewebsites.net/api/step_by_step_workflow" \
  -H "Content-Type: application/json" \
  -d '{
    "hours_back": 24
  }'
```

## Response Format

### Single Work Item Response

```json
{
  "work_item_id": "12345",
  "success": true,
  "errors": [],
  "email_sent": true,
  "teams_sent": true,
  "step_timings": {
    "step_1_work_item_creation": 0.5,
    "step_2_historical_analysis": 2.1,
    "step_3_ai_triage_and_message_generation": 3.2,
    "step_4_email_notification": 1.0,
    "step_5_teams_message": 0.8
  },
  "total_time": 7.6,
  "triage_result": {
    "work_item_id": "12345",
    "assigned_to": "<EMAIL>",
    "priority": 2,
    "confidence_score": 0.85,
    "reasoning": "Based on similar issues and expertise analysis...",
    "duplicates": []
  },
  "workflow_steps": {
    "step_1": "Work Item Creation/Retrieval",
    "step_2": "Historical Analysis & Pattern Recognition",
    "step_3": "AI Triage & Message Generation",
    "step_4": "Email Notification Delivery",
    "step_5": "Teams Message Broadcasting"
  },
  "processing_mode": "single_work_item",
  "execution_time_seconds": 7.6,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Batch Processing Response

```json
{
  "total_items_processed": 5,
  "successful_items": 4,
  "failed_items": 1,
  "results": [
    {
      "work_item_id": "12345",
      "success": true,
      "email_sent": true,
      "teams_sent": true
    },
    {
      "work_item_id": "12346",
      "success": false,
      "errors": ["Failed to retrieve work item"],
      "email_sent": false,
      "teams_sent": false
    }
  ],
  "workflow_steps": {
    "step_1": "Work Item Creation/Retrieval",
    "step_2": "Historical Analysis & Pattern Recognition",
    "step_3": "AI Triage & Message Generation",
    "step_4": "Email Notification Delivery",
    "step_5": "Teams Message Broadcasting"
  },
  "processing_mode": "batch_processing",
  "hours_back": 24,
  "success": false,
  "execution_time_seconds": 45.2,
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Error Handling

The workflow includes comprehensive error handling:

- **Step-level isolation**: Errors in one step don't prevent other steps from executing
- **Detailed error tracking**: Each error is logged with context and step information
- **Graceful degradation**: Partial success is tracked and reported
- **Retry logic**: Transient failures are automatically retried
- **Audit trail**: All actions and errors are logged for troubleshooting

## Monitoring and Observability

### Logging

Each step logs structured information including:
- Step start/completion times
- Input parameters and outputs
- Success/failure status
- Performance metrics
- Error details with stack traces

### Metrics

Key metrics tracked:
- Step execution times
- Success/failure rates per step
- Email delivery rates
- Teams message delivery rates
- Overall workflow completion times

### Alerts

Automated alerts for:
- High failure rates in any step
- Slow execution times
- Email delivery failures
- Teams notification failures

## Integration Points

### Azure DevOps
- Work item retrieval and updates
- Historical data analysis
- Assignment tracking

### Azure AI Search
- Vector similarity search for duplicates
- Historical pattern analysis
- Knowledge base queries

### Logic Apps
- Email notification delivery
- Professional email formatting
- Attachment handling

### Microsoft Teams
- Adaptive card notifications
- Channel messaging
- User interaction tracking

## Configuration

The workflow can be configured through environment variables:

```bash
# AI Engine Settings
DUPLICATE_DETECTION_THRESHOLD=0.8
ASSIGNMENT_CONFIDENCE_THRESHOLD=0.7
PRIORITY_ANALYSIS_ENABLED=true

# Notification Settings
EMAIL_NOTIFICATIONS_ENABLED=true
TEAMS_NOTIFICATIONS_ENABLED=true
NOTIFICATION_RETRY_COUNT=3

# Performance Settings
WORKFLOW_TIMEOUT_SECONDS=300
STEP_TIMEOUT_SECONDS=60
PARALLEL_PROCESSING_ENABLED=true
```

## Best Practices

1. **Monitor step timings** to identify performance bottlenecks
2. **Review error patterns** to improve reliability
3. **Validate notification delivery** to ensure stakeholder awareness
4. **Tune AI confidence thresholds** based on feedback
5. **Regular testing** with both single items and batch processing
6. **Backup notification channels** for critical items

## Troubleshooting

### Common Issues

1. **Work item not found**: Check work item ID and permissions
2. **Email delivery failures**: Verify Logic App configuration and recipients
3. **Teams notification failures**: Check Teams webhook URLs and permissions
4. **Slow performance**: Review AI engine response times and database queries
5. **Memory issues**: Monitor function app memory usage during batch processing
