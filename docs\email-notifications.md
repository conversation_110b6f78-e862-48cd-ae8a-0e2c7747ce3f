# Email Notifications

The Auto Defect Triage system includes automated email notifications that send daily reports of new work items to stakeholders.

## Overview

The email notification system uses Azure Logic Apps to:
- Query Azure DevOps for work items created in the last 48 hours
- Generate professional HTML emails with work item details
- Send daily reports to configured recipients
- Handle zero-item scenarios with "All Clear" messages

## Architecture

```
Azure Logic Apps → Azure DevOps API → Email Generation → Office 365 Connector → Recipients
     ↑                                                                              ↓
Daily Trigger (9:00 AM GMT)                                            Professional Email
```

## Features

### Email Content
- **Professional branding** with Virgin Atlantic styling
- **Work item cards** with complete details (ID, Title, State, Priority, Severity, Assignee)
- **Direct links** to Azure DevOps work items
- **Color-coded badges** by work item type (Red=Bug, Orange=Defect, Green=Story)
- **Responsive design** that works on all devices
- **Summary statistics** and report period information

### Smart Logic
- **Conditional content**: Shows "All Clear" message if no new items
- **Dynamic subject lines**: Includes actual count of work items
- **Error handling**: Graceful handling of API failures
- **Timezone awareness**: Consistent GMT scheduling

## Configuration

### Required Parameters
- `ado_organization`: Azure DevOps organization name
- `ado_project`: Azure DevOps project name  
- `ado_pat_token`: Personal Access Token for Azure DevOps API access
- `email_recipients`: Comma-separated list of email addresses
- `email_sender`: Sender email address

### Schedule
- **Default**: Daily at 9:00 AM GMT
- **Customizable**: Edit Logic App trigger for different times/frequencies
- **Time period**: Last 48 hours (configurable in WIQL query)

## Deployment

### Prerequisites
- Azure subscription with Logic Apps enabled
- Office 365 account for email sending
- Azure DevOps PAT token with work item read permissions

### Steps

1. **Deploy Logic App**
   ```powershell
   cd infrastructure/logic-apps
   .\deploy-logic-app-email.ps1
   ```

2. **Authorize Office 365 Connection**
   - Go to Azure Portal
   - Navigate to the created API Connection
   - Click "Edit API connection" and authorize

3. **Test the Logic App**
   - Trigger a manual run from Azure Portal
   - Verify email delivery and formatting

### ARM Template Deployment
```bash
az deployment group create \
    --resource-group rg-autodefecttriage \
    --template-file logic-app-arm-template.json \
    --parameters \
        adoPatToken="your-token" \
        emailRecipients="<EMAIL>"
```

## Email Templates

### With Work Items
```
Subject: 📊 Daily Work Items Report - 5 items created in last 48 hours

Content:
- Header with Virgin Atlantic branding
- Summary section with statistics
- Individual work item cards with:
  - Clickable title linking to Azure DevOps
  - Work item type badge
  - Complete metadata grid
  - Creation details and area path
```

### No Work Items
```
Subject: ✅ Daily Work Items Report - No new items in last 48 hours

Content:
- "All Clear" message
- Report period confirmation
- Generation timestamp
```

## Customization

### Recipients
Update the `email_recipients` parameter:
```json
"email_recipients": "<EMAIL>,<EMAIL>,<EMAIL>"
```

### Schedule
Modify the Logic App trigger:
```json
"recurrence": {
    "frequency": "Day",
    "interval": 1,
    "schedule": {
        "hours": ["9", "17"],  // 9 AM and 5 PM
        "minutes": [0]
    }
}
```

### Time Period
Adjust the WIQL query date range:
```sql
-- Change from 48 hours to 24 hours
AND [System.CreatedDate] >= '@{formatDateTime(addDays(utcNow(), -1), 'yyyy-MM-dd')}'
```

### Work Item Types
Modify the WIQL query to include/exclude types:
```sql
WHERE [System.WorkItemType] IN ('Bug', 'Defect', 'Epic', 'Feature')
```

## Monitoring

### Logic App Runs
- View run history in Azure Portal
- Monitor success/failure rates
- Check execution duration and performance

### Email Delivery
- Verify emails are being received
- Check spam/junk folders if needed
- Monitor bounce rates and delivery issues

### Troubleshooting
- **No emails received**: Check Office 365 connection authorization
- **Empty emails**: Verify WIQL query and date ranges
- **API errors**: Check PAT token permissions and expiration
- **Formatting issues**: Review HTML template and CSS

## Integration

### With Auto Defect Triage System
- Uses same Azure DevOps configuration
- Complements AI triage workflow
- Provides visibility into items being processed
- Shares PAT token and project settings

### With Teams Notifications
- Can run alongside Teams notifications
- Provides different communication channel
- Suitable for stakeholders without Teams access
- Professional format for management reporting

## Security

### Data Protection
- PAT tokens stored securely in Logic App parameters
- No sensitive data in email content
- HTTPS encryption for all API calls
- Office 365 authentication for email sending

### Access Control
- Logic App managed identity for Azure resources
- RBAC for Logic App management
- Email recipient list controlled via parameters
- Audit trail in Azure Activity Log

## Cost Optimization

### Logic Apps Pricing
- Consumption plan: Pay per execution
- Typical cost: <$5/month for daily emails
- No idle costs when not running
- Scales automatically with usage

### Optimization Tips
- Use single Logic App for multiple projects
- Batch multiple recipients in one email
- Optimize WIQL queries for performance
- Monitor and adjust trigger frequency as needed

## Files

- `infrastructure/logic-apps/logic-app-arm-template.json` - ARM deployment template
- `infrastructure/logic-apps/logic-app-email-notification.json` - Logic App definition
- `infrastructure/logic-apps/deploy-logic-app-email.ps1` - Deployment script
- `docs/email-notifications.md` - This documentation

---

**The email notification system provides professional, automated communication about new work items to keep your team and stakeholders informed.**
