# Setup Teams Notifications for AutoDefectTriage
# ==============================================
#
# This script helps you configure Teams notifications quickly.
#
# Usage:
#   .\Setup-TeamsNotifications.ps1

param(
    [string]$WebhookUrl = "",
    [switch]$TestOnly
)

function Write-StepHeader {
    param([string]$Title, [string]$Icon = "🔄")
    
    Write-Host ""
    Write-Host "$Icon $Title" -ForegroundColor Cyan
    Write-Host ("-" * 60) -ForegroundColor Gray
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Write-Warning {
    param([string]$Message)
    Write-Host "⚠️  $Message" -ForegroundColor Yellow
}

function Show-TeamsWebhookInstructions {
    Write-StepHeader "TEAMS WEBHOOK SETUP INSTRUCTIONS" "📋"
    
    Write-Host "To create a Teams Incoming Webhook:" -ForegroundColor Yellow
    Write-Host "1. Open Microsoft Teams" -ForegroundColor White
    Write-Host "2. Go to the channel where you want notifications" -ForegroundColor White
    Write-Host "3. Click the three dots (...) next to the channel name" -ForegroundColor White
    Write-Host "4. Select 'Connectors'" -ForegroundColor White
    Write-Host "5. Find 'Incoming Webhook' and click 'Configure'" -ForegroundColor White
    Write-Host "6. Give it a name (e.g., 'AutoDefectTriage Notifications')" -ForegroundColor White
    Write-Host "7. Click 'Create' and copy the webhook URL" -ForegroundColor White
    Write-Host ""
    Write-Host "The webhook URL should look like:" -ForegroundColor Yellow
    Write-Host "https://outlook.office.com/webhook/YOUR-WEBHOOK-ID/..." -ForegroundColor Gray
}

function Get-WebhookUrl {
    if ($WebhookUrl) {
        Write-Info "Using provided webhook URL: $($WebhookUrl.Substring(0, [Math]::Min(50, $WebhookUrl.Length)))..."
        return $WebhookUrl
    }
    
    Write-Host ""
    Write-Host "🔗 Please enter your Teams webhook URL:" -ForegroundColor Cyan
    
    do {
        $url = Read-Host "Webhook URL"
        
        if (-not $url) {
            Write-Error "Please enter a webhook URL"
            continue
        }
        
        if (-not $url.StartsWith("https://outlook.office.com/webhook/")) {
            Write-Warning "This doesn't look like a Teams webhook URL"
            $confirm = Read-Host "Continue anyway? (y/n)"
            if ($confirm -ne "y") {
                continue
            }
        }
        
        Write-Success "Webhook URL accepted"
        return $url
        
    } while ($true)
}

function Update-ConfigFile {
    param([string]$WebhookUrl)
    
    Write-StepHeader "UPDATING CONFIGURATION" "📝"
    
    $configPath = "functions\local.settings.json"
    
    # Create functions directory if it doesn't exist
    if (-not (Test-Path "functions")) {
        New-Item -ItemType Directory -Path "functions" -Force | Out-Null
        Write-Info "Created functions directory"
    }
    
    # Load existing config or create new one
    $config = @{}
    if (Test-Path $configPath) {
        try {
            $configContent = Get-Content $configPath -Raw | ConvertFrom-Json
            $config = @{}
            $configContent.PSObject.Properties | ForEach-Object {
                $config[$_.Name] = $_.Value
            }
            Write-Info "Loaded existing configuration"
        }
        catch {
            Write-Warning "Could not read existing config, creating new one"
            $config = @{}
        }
    }
    else {
        Write-Info "Creating new configuration file"
    }
    
    # Ensure Values section exists
    if (-not $config.Values) {
        $config.Values = @{}
    }
    
    # Add Teams configuration
    $config.Values.TEAMS_WEBHOOK_URL = $WebhookUrl
    $config.Values.TEAMS_NOTIFICATIONS_ENABLED = "true"
    $config.Values.TEAMS_NOTIFICATION_CHANNEL = "AutoDefectTriage Alerts"
    
    # Add other required settings if missing
    if (-not $config.Values.AzureWebJobsStorage) {
        $config.Values.AzureWebJobsStorage = "UseDevelopmentStorage=true"
    }
    if (-not $config.Values.FUNCTIONS_WORKER_RUNTIME) {
        $config.Values.FUNCTIONS_WORKER_RUNTIME = "python"
    }
    
    # Set IsEncrypted
    $config.IsEncrypted = $false
    
    # Write updated config
    try {
        $config | ConvertTo-Json -Depth 10 | Set-Content $configPath -Encoding UTF8
        Write-Success "Configuration updated: $configPath"
        return $true
    }
    catch {
        Write-Error "Failed to update config file: $($_.Exception.Message)"
        return $false
    }
}

function Test-TeamsWebhook {
    param([string]$WebhookUrl)
    
    Write-StepHeader "TESTING TEAMS WEBHOOK" "🧪"
    
    $testMessage = @{
        "@type" = "MessageCard"
        "@context" = "http://schema.org/extensions"
        "themeColor" = "0076D7"
        "summary" = "AutoDefectTriage Test Notification"
        "sections" = @(
            @{
                "activityTitle" = "🧪 Test Notification"
                "activitySubtitle" = "AutoDefectTriage System Setup"
                "facts" = @(
                    @{
                        "name" = "Status:"
                        "value" = "Configuration Test"
                    },
                    @{
                        "name" = "Work Item ID:"
                        "value" = "TEST-12345"
                    },
                    @{
                        "name" = "Priority:"
                        "value" = "1 (Critical)"
                    },
                    @{
                        "name" = "Assigned To:"
                        "value" = "auth-team"
                    }
                )
                "markdown" = $true
            }
        )
        "potentialAction" = @(
            @{
                "@type" = "OpenUri"
                "name" = "View Setup Guide"
                "targets" = @(
                    @{
                        "os" = "default"
                        "uri" = "https://github.com/your-org/AutoDefectTriage"
                    }
                )
            }
        )
    }
    
    try {
        Write-Info "Sending test message to Teams..."
        
        $body = $testMessage | ConvertTo-Json -Depth 10
        $response = Invoke-RestMethod -Uri $WebhookUrl -Method POST -Body $body -ContentType "application/json" -TimeoutSec 10
        
        Write-Success "Test notification sent successfully!"
        Write-Info "📱 Check your Teams channel for the test message."
        return $true
    }
    catch {
        Write-Error "Test failed: $($_.Exception.Message)"
        
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Error "HTTP Status: $statusCode"
        }
        
        return $false
    }
}

function Test-WorkflowNotification {
    Write-StepHeader "TESTING WORKFLOW NOTIFICATION" "🔄"
    
    Write-Info "Running workflow test with Teams notifications..."
    
    try {
        $result = python "scripts\execute_function_step_by_step.py" --local-test
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Workflow test completed!"
            Write-Info "📱 Check your Teams channel for the workflow notification."
        }
        else {
            Write-Warning "Workflow test completed with issues. Check the output above."
        }
    }
    catch {
        Write-Error "Failed to run workflow test: $($_.Exception.Message)"
        Write-Info "You can run it manually later with:"
        Write-Info "python scripts\execute_function_step_by_step.py --local-test"
    }
}

function Show-CompletionMessage {
    param([string]$WebhookUrl)
    
    Write-StepHeader "SETUP COMPLETE" "🎉"
    
    Write-Success "Teams Notification Setup Complete!"
    Write-Host ""
    Write-Host "✅ Configuration saved to: functions\local.settings.json" -ForegroundColor Green
    Write-Host "✅ Teams webhook tested successfully" -ForegroundColor Green
    Write-Host ""
    Write-Host "🚀 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Run the full workflow:" -ForegroundColor White
    Write-Host "   python scripts\execute_function_step_by_step.py --local-test" -ForegroundColor Gray
    Write-Host ""
    Write-Host "2. Check your Teams channel for notifications" -ForegroundColor White
    Write-Host ""
    Write-Host "3. For production deployment, add these environment variables:" -ForegroundColor White
    Write-Host "   TEAMS_WEBHOOK_URL=$WebhookUrl" -ForegroundColor Gray
    Write-Host "   TEAMS_NOTIFICATIONS_ENABLED=true" -ForegroundColor Gray
    Write-Host ""
    Write-Host "📚 For more configuration options, see:" -ForegroundColor Yellow
    Write-Host "   docs\teams-notification-setup.md" -ForegroundColor Gray
}

# Main execution
Write-Host "🚀 AutoDefectTriage Teams Notification Setup" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray
Write-Host ""

try {
    if ($TestOnly) {
        # Just test existing configuration
        Write-Info "Testing existing Teams configuration..."
        
        $configPath = "functions\local.settings.json"
        if (Test-Path $configPath) {
            $config = Get-Content $configPath | ConvertFrom-Json
            $webhookUrl = $config.Values.TEAMS_WEBHOOK_URL
            
            if ($webhookUrl) {
                $testResult = Test-TeamsWebhook -WebhookUrl $webhookUrl
                if ($testResult) {
                    Test-WorkflowNotification
                }
            }
            else {
                Write-Error "No Teams webhook URL found in configuration"
            }
        }
        else {
            Write-Error "Configuration file not found: $configPath"
        }
    }
    else {
        # Full setup process
        Show-TeamsWebhookInstructions
        
        $webhookUrl = Get-WebhookUrl
        
        $configUpdated = Update-ConfigFile -WebhookUrl $webhookUrl
        
        if ($configUpdated) {
            $testResult = Test-TeamsWebhook -WebhookUrl $webhookUrl
            
            if ($testResult) {
                Test-WorkflowNotification
                Show-CompletionMessage -WebhookUrl $webhookUrl
            }
            else {
                Write-Warning "Webhook test failed, but configuration was saved."
                Write-Info "You can test manually later or check the webhook URL."
            }
        }
    }
}
catch {
    Write-Error "Setup failed: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "✨ Setup script completed!" -ForegroundColor Green
