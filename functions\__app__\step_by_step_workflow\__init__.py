"""
Step-by-Step Workflow Azure Function
===================================

Azure Function that executes the complete step-by-step workflow:
1. Work Item Creation Trigger
2. Historical Analysis & Pattern Recognition  
3. AI-Powered Notification Message Generation
4. Email Notification Delivery
5. Teams Message Broadcasting

This function can be triggered manually or by webhooks to process work items
through the complete defect triage workflow.
"""

import azure.functions as func
import json
import logging
from datetime import datetime
from typing import Dict, Any

from ..workflow_orchestrator import execute_workflow_for_work_item, execute_workflow_for_recent_items
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


async def main(req: func.HttpRequest) -> func.HttpResponse:
    """
    Azure Function entry point for step-by-step workflow execution.
    
    Supports two modes:
    1. Single work item processing: POST with work_item_id
    2. Batch processing: POST with hours_back parameter
    
    Request body examples:
    
    Single work item:
    {
        "work_item_id": "12345"
    }
    
    Batch processing:
    {
        "hours_back": 24
    }
    
    Returns:
        HTTP response with workflow execution results
    """
    
    start_time = datetime.utcnow()
    
    try:
        log_structured(
            logger,
            "info",
            "Step-by-step workflow function triggered",
            extra={
                "method": req.method,
                "url": req.url,
                "timestamp": start_time.isoformat()
            }
        )
        
        # Parse request body
        try:
            req_body = req.get_json()
        except ValueError:
            return func.HttpResponse(
                json.dumps({
                    "error": "Invalid JSON in request body",
                    "timestamp": datetime.utcnow().isoformat()
                }),
                status_code=400,
                mimetype="application/json"
            )
        
        if not req_body:
            return func.HttpResponse(
                json.dumps({
                    "error": "Request body is required",
                    "expected_format": {
                        "single_item": {"work_item_id": "12345"},
                        "batch_processing": {"hours_back": 24}
                    },
                    "timestamp": datetime.utcnow().isoformat()
                }),
                status_code=400,
                mimetype="application/json"
            )
        
        # Determine processing mode
        work_item_id = req_body.get('work_item_id')
        hours_back = req_body.get('hours_back')
        
        if work_item_id:
            # Single work item processing
            result = await process_single_work_item(work_item_id)
        elif hours_back:
            # Batch processing
            result = await process_recent_work_items(hours_back)
        else:
            return func.HttpResponse(
                json.dumps({
                    "error": "Either 'work_item_id' or 'hours_back' must be provided",
                    "timestamp": datetime.utcnow().isoformat()
                }),
                status_code=400,
                mimetype="application/json"
            )
        
        # Calculate total execution time
        total_time = (datetime.utcnow() - start_time).total_seconds()
        result["execution_time_seconds"] = total_time
        result["timestamp"] = datetime.utcnow().isoformat()
        
        # Log completion
        log_structured(
            logger,
            "info",
            "Step-by-step workflow function completed",
            extra={
                "processing_mode": "single" if work_item_id else "batch",
                "work_item_id": work_item_id,
                "hours_back": hours_back,
                "success": result.get("success", False),
                "execution_time_seconds": total_time
            }
        )
        
        return func.HttpResponse(
            json.dumps(result, indent=2),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        error_message = f"Step-by-step workflow function failed: {str(e)}"
        
        log_structured(
            logger,
            "error",
            error_message,
            exc_info=True
        )
        
        return func.HttpResponse(
            json.dumps({
                "error": error_message,
                "timestamp": datetime.utcnow().isoformat()
            }),
            status_code=500,
            mimetype="application/json"
        )


async def process_single_work_item(work_item_id: str) -> Dict[str, Any]:
    """
    Process a single work item through the complete workflow.
    
    Args:
        work_item_id: ID of the work item to process
        
    Returns:
        Dictionary with processing results
    """
    try:
        log_structured(
            logger,
            "info",
            f"Starting single work item workflow for: {work_item_id}",
            extra={"work_item_id": work_item_id}
        )
        
        # Execute the complete workflow
        result = await execute_workflow_for_work_item(work_item_id)
        
        # Add workflow step details
        result["workflow_steps"] = {
            "step_1": "Work Item Creation/Retrieval",
            "step_2": "Historical Analysis & Pattern Recognition",
            "step_3": "AI Triage & Message Generation", 
            "step_4": "Email Notification Delivery",
            "step_5": "Teams Message Broadcasting"
        }
        
        result["processing_mode"] = "single_work_item"
        
        log_structured(
            logger,
            "info",
            f"Single work item workflow completed for: {work_item_id}",
            extra={
                "work_item_id": work_item_id,
                "success": result["success"],
                "email_sent": result["email_sent"],
                "teams_sent": result["teams_sent"],
                "total_time": result["total_time"]
            }
        )
        
        return result
        
    except Exception as e:
        error_message = f"Failed to process work item {work_item_id}: {str(e)}"
        log_structured(
            logger,
            "error",
            error_message,
            extra={"work_item_id": work_item_id},
            exc_info=True
        )
        
        return {
            "work_item_id": work_item_id,
            "success": False,
            "error": error_message,
            "processing_mode": "single_work_item"
        }


async def process_recent_work_items(hours_back: int) -> Dict[str, Any]:
    """
    Process all work items created in the last N hours through the workflow.
    
    Args:
        hours_back: Number of hours to look back for work items
        
    Returns:
        Dictionary with batch processing results
    """
    try:
        log_structured(
            logger,
            "info",
            f"Starting batch workflow for work items from last {hours_back} hours",
            extra={"hours_back": hours_back}
        )
        
        # Execute batch workflow
        result = await execute_workflow_for_recent_items(hours_back)
        
        # Add workflow step details
        result["workflow_steps"] = {
            "step_1": "Work Item Creation/Retrieval",
            "step_2": "Historical Analysis & Pattern Recognition", 
            "step_3": "AI Triage & Message Generation",
            "step_4": "Email Notification Delivery",
            "step_5": "Teams Message Broadcasting"
        }
        
        result["processing_mode"] = "batch_processing"
        result["hours_back"] = hours_back
        result["success"] = result["failed_items"] == 0
        
        log_structured(
            logger,
            "info",
            f"Batch workflow completed for {hours_back} hours",
            extra={
                "hours_back": hours_back,
                "total_items_processed": result["total_items_processed"],
                "successful_items": result["successful_items"],
                "failed_items": result["failed_items"]
            }
        )
        
        return result
        
    except Exception as e:
        error_message = f"Failed to process recent work items (last {hours_back} hours): {str(e)}"
        log_structured(
            logger,
            "error",
            error_message,
            extra={"hours_back": hours_back},
            exc_info=True
        )
        
        return {
            "hours_back": hours_back,
            "success": False,
            "error": error_message,
            "processing_mode": "batch_processing"
        }
