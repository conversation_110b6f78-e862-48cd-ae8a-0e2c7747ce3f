@description('Monitoring and alerting for Intelligent Notifications & Reminders service')

// Parameters
@description('Environment name (dev, staging, prod)')
param environment string = 'dev'

@description('Location for all resources')
param location string = resourceGroup().location

@description('Application name prefix')
param appName string = 'qa-ai-triage'

@description('Function App resource ID')
param functionAppResourceId string

@description('Application Insights resource ID')
param appInsightsResourceId string

@description('Log Analytics workspace resource ID')
param logAnalyticsWorkspaceId string

@description('Email addresses for alert notifications')
param alertEmailAddresses array = []

@description('Teams webhook URL for alert notifications')
param alertTeamsWebhookUrl string = ''

@description('Tags to apply to all resources')
param tags object = {
  Environment: environment
  Application: 'QA-AI-Triage'
  Component: 'Notifications-Monitoring'
  ManagedBy: 'Bicep'
}

// Variables
var resourcePrefix = '${appName}-notifications-${environment}'
var actionGroupName = '${resourcePrefix}-alerts'

// Action Group for notifications
resource alertActionGroup 'Microsoft.Insights/actionGroups@2023-01-01' = {
  name: actionGroupName
  location: 'Global'
  tags: tags
  properties: {
    groupShortName: 'NotifAlerts'
    enabled: true
    emailReceivers: [for email in alertEmailAddresses: {
      name: replace(email, '@', '-at-')
      emailAddress: email
      useCommonAlertSchema: true
    }]
    webhookReceivers: !empty(alertTeamsWebhookUrl) ? [
      {
        name: 'teams-webhook'
        serviceUri: alertTeamsWebhookUrl
        useCommonAlertSchema: true
      }
    ] : []
  }
}

// SLA Compliance Alert - P50 Latency > 5 seconds
resource p50LatencyAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = {
  name: '${resourcePrefix}-p50-latency-alert'
  location: 'Global'
  tags: tags
  properties: {
    description: 'Alert when P50 notification processing time exceeds 5 seconds'
    severity: 2
    enabled: true
    scopes: [
      appInsightsResourceId
    ]
    evaluationFrequency: 'PT5M'
    windowSize: 'PT15M'
    criteria: {
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
      allOf: [
        {
          name: 'P50LatencyThreshold'
          metricName: 'customMetrics/notification_p50_processing_time'
          operator: 'GreaterThan'
          threshold: 5000
          timeAggregation: 'Average'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
    }
    actions: [
      {
        actionGroupId: alertActionGroup.id
      }
    ]
  }
}

// Error Rate Alert - Error rate > 1%
resource errorRateAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = {
  name: '${resourcePrefix}-error-rate-alert'
  location: 'Global'
  tags: tags
  properties: {
    description: 'Alert when notification error rate exceeds 1%'
    severity: 1
    enabled: true
    scopes: [
      appInsightsResourceId
    ]
    evaluationFrequency: 'PT5M'
    windowSize: 'PT15M'
    criteria: {
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
      allOf: [
        {
          name: 'ErrorRateThreshold'
          metricName: 'customMetrics/notification_error_rate'
          operator: 'GreaterThan'
          threshold: 1.0
          timeAggregation: 'Average'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
    }
    actions: [
      {
        actionGroupId: alertActionGroup.id
      }
    ]
  }
}

// Function App Health Alert
resource functionHealthAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = {
  name: '${resourcePrefix}-function-health-alert'
  location: 'Global'
  tags: tags
  properties: {
    description: 'Alert when Function App is unhealthy'
    severity: 0
    enabled: true
    scopes: [
      functionAppResourceId
    ]
    evaluationFrequency: 'PT1M'
    windowSize: 'PT5M'
    criteria: {
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
      allOf: [
        {
          name: 'FunctionErrors'
          metricName: 'FunctionExecutionCount'
          operator: 'LessThan'
          threshold: 1
          timeAggregation: 'Total'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
    }
    actions: [
      {
        actionGroupId: alertActionGroup.id
      }
    ]
  }
}

// High Memory Usage Alert
resource memoryUsageAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = {
  name: '${resourcePrefix}-memory-usage-alert'
  location: 'Global'
  tags: tags
  properties: {
    description: 'Alert when Function App memory usage is high'
    severity: 2
    enabled: true
    scopes: [
      functionAppResourceId
    ]
    evaluationFrequency: 'PT5M'
    windowSize: 'PT15M'
    criteria: {
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
      allOf: [
        {
          name: 'MemoryUsageThreshold'
          metricName: 'MemoryWorkingSet'
          operator: 'GreaterThan'
          threshold: 1073741824 // 1GB in bytes
          timeAggregation: 'Average'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
    }
    actions: [
      {
        actionGroupId: alertActionGroup.id
      }
    ]
  }
}

// Rate Limiting Alert - High rate limiting activity
resource rateLimitingAlert 'Microsoft.Insights/metricAlerts@2018-03-01' = {
  name: '${resourcePrefix}-rate-limiting-alert'
  location: 'Global'
  tags: tags
  properties: {
    description: 'Alert when rate limiting is frequently triggered'
    severity: 2
    enabled: true
    scopes: [
      appInsightsResourceId
    ]
    evaluationFrequency: 'PT5M'
    windowSize: 'PT15M'
    criteria: {
      'odata.type': 'Microsoft.Azure.Monitor.SingleResourceMultipleMetricCriteria'
      allOf: [
        {
          name: 'RateLimitingThreshold'
          metricName: 'customMetrics/rate_limited_notifications'
          operator: 'GreaterThan'
          threshold: 10
          timeAggregation: 'Total'
          criterionType: 'StaticThresholdCriterion'
        }
      ]
    }
    actions: [
      {
        actionGroupId: alertActionGroup.id
      }
    ]
  }
}

// Workbook for notification monitoring dashboard
resource notificationWorkbook 'Microsoft.Insights/workbooks@2023-06-01' = {
  name: guid('${resourcePrefix}-workbook')
  location: location
  tags: tags
  kind: 'shared'
  properties: {
    displayName: 'Notification Service Dashboard'
    serializedData: string({
      version: 'Notebook/1.0'
      items: [
        {
          type: 1
          content: {
            json: '# Intelligent Notifications & Reminders Dashboard\n\nThis dashboard provides insights into the notification service performance and SLA compliance.'
          }
        }
        {
          type: 3
          content: {
            version: 'KqlItem/1.0'
            query: 'customMetrics\n| where name == "notification_p50_processing_time"\n| summarize P50_Latency_ms = avg(value) by bin(timestamp, 5m)\n| render timechart'
            size: 0
            title: 'P50 Processing Time (SLA: < 5000ms)'
            timeContext: {
              durationMs: 3600000
            }
            queryType: 0
            resourceType: 'microsoft.insights/components'
          }
        }
        {
          type: 3
          content: {
            version: 'KqlItem/1.0'
            query: 'customMetrics\n| where name == "notification_error_rate"\n| summarize Error_Rate_Percent = avg(value) by bin(timestamp, 5m)\n| render timechart'
            size: 0
            title: 'Error Rate (SLA: < 1%)'
            timeContext: {
              durationMs: 3600000
            }
            queryType: 0
            resourceType: 'microsoft.insights/components'
          }
        }
        {
          type: 3
          content: {
            version: 'KqlItem/1.0'
            query: 'customEvents\n| where name == "notification_created"\n| summarize Notifications_Created = count() by bin(timestamp, 5m)\n| render timechart'
            size: 0
            title: 'Notifications Created Over Time'
            timeContext: {
              durationMs: 3600000
            }
            queryType: 0
            resourceType: 'microsoft.insights/components'
          }
        }
        {
          type: 3
          content: {
            version: 'KqlItem/1.0'
            query: 'customEvents\n| where name == "notification_status_changed"\n| extend status = tostring(customDimensions.new_status)\n| summarize count() by status\n| render piechart'
            size: 0
            title: 'Notification Status Distribution'
            timeContext: {
              durationMs: 3600000
            }
            queryType: 0
            resourceType: 'microsoft.insights/components'
          }
        }
        {
          type: 3
          content: {
            version: 'KqlItem/1.0'
            query: 'customEvents\n| where name == "notification_created"\n| extend trigger_type = tostring(customDimensions.trigger_type)\n| summarize count() by trigger_type\n| render barchart'
            size: 0
            title: 'Notifications by Trigger Type'
            timeContext: {
              durationMs: 3600000
            }
            queryType: 0
            resourceType: 'microsoft.insights/components'
          }
        }
      ]
      isLocked: false
      fallbackResourceIds: [
        appInsightsResourceId
      ]
    })
    category: 'workbook'
    sourceId: appInsightsResourceId
  }
}

// Scheduled Query Rules for complex alerting
resource slaComplianceAlert 'Microsoft.Insights/scheduledQueryRules@2023-03-15-preview' = {
  name: '${resourcePrefix}-sla-compliance-alert'
  location: location
  tags: tags
  properties: {
    displayName: 'SLA Compliance Alert'
    description: 'Alert when overall SLA compliance drops below threshold'
    severity: 1
    enabled: true
    evaluationFrequency: 'PT15M'
    windowSize: 'PT1H'
    scopes: [
      appInsightsResourceId
    ]
    criteria: {
      allOf: [
        {
          query: '''
            let p50_threshold = 5000;
            let error_rate_threshold = 0.01;
            let p50_metrics = customMetrics
                | where name == "notification_p50_processing_time"
                | where timestamp > ago(1h)
                | summarize avg_p50 = avg(value);
            let error_metrics = customMetrics
                | where name == "notification_error_rate"
                | where timestamp > ago(1h)
                | summarize avg_error_rate = avg(value);
            p50_metrics
            | extend error_rate = toscalar(error_metrics | project avg_error_rate)
            | extend sla_compliant = (avg_p50 <= p50_threshold and error_rate <= error_rate_threshold)
            | where sla_compliant == false
          '''
          timeAggregation: 'Count'
          operator: 'GreaterThan'
          threshold: 0
          failingPeriods: {
            numberOfEvaluationPeriods: 1
            minFailingPeriodsToAlert: 1
          }
        }
      ]
    }
    actions: {
      actionGroups: [
        alertActionGroup.id
      ]
    }
  }
}

// Outputs
output actionGroupId string = alertActionGroup.id
output workbookId string = notificationWorkbook.id
output alertIds object = {
  p50Latency: p50LatencyAlert.id
  errorRate: errorRateAlert.id
  functionHealth: functionHealthAlert.id
  memoryUsage: memoryUsageAlert.id
  rateLimiting: rateLimitingAlert.id
  slaCompliance: slaComplianceAlert.id
}
