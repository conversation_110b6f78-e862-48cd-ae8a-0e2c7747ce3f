{"746986": {"id": 746986, "title": "Unable to assign Economy Classic standard window seat during booking flow", "description": "<div>This applies to all seat types.&nbsp; I won't raise a defect for each type.&nbsp; Also the seat cost displayed as £30.00 for 10th May LHR-MIA but spreadsheet with seat costs advised £29.00 for 10th May&nbsp;&nbsp; </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/58224e24-c475-45e1-bcd0-dbc7275464b9?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/4ea0a9e3-1e67-4d27-9bbd-c4444b880051?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/d66862e1-97f3-4119-b46f-e3f613255229?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON>, <PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-30T08:12:24.487Z", "changed_date": "2025-09-30T08:25:36.95Z", "priority": 2, "tags": "", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">30/09/2025  09:07 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;Economy Classic standard seat during booking flow&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Agent is signed into Axis </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">2. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Enter into iNotes flight routing, dates and number of passengers and click open shopping icon<div style=\"padding-top:10px;\">Expected Result </div><div>Shopping options for outbound flight displayed </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">3. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Select outbound flight<div style=\"padding-top:10px;\">Expected Result </div><div>Return flight options are displayed </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">4. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Select return flight and click Sell and Store<div style=\"padding-top:10px;\">Expected Result </div><div>Psgr tab is displayed </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">5. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Enter passenger detials including phone number and email and click SUBMIT<div style=\"padding-top:10px;\">Expected Result </div><div>Psgr summary is displayed </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">6. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Click End &amp; Redisplay<div style=\"padding-top:10px;\">Expected Result </div><div>Summary screen is displayed </div><div style=\"padding-top:10px;\"><div>Comments: PNR GXVL52 created </div> </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">7. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:green;\">Passed </td><td style=\"vertical-align:top;padding:2px 7px;\">Select Seats tab<div style=\"padding-top:10px;\">Expected Result </div><div>Seat map for outbound flight is displayed </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">8. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:red;\">Failed </td><td style=\"vertical-align:top;padding:2px 7px;\">Click on desired seat and click assign<div style=\"padding-top:10px;\">Expected Result </div><div>Seat cost is added to check out box total </div><div style=\"padding-top:10px;\"><div>Comments: Error is displayed - Undefined product code STW.  Seat cannot be assigned because one or more conditions are not fulfilled like adjacent seating, specific SSR etc </div> </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">9. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">Click checkout<div style=\"padding-top:10px;\">Expected Result </div><div>Checkout summary displays </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">10. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">Click Next<div style=\"padding-top:10px;\">Expected Result </div><div>Payment screen displays </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">11. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">Enter card details and billing address info and click Pay Now<div style=\"padding-top:10px;\">Expected Result </div><div>Processing summary displays progress of payment for flight ticket and seat </div> </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">12. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">Click on Close on the checkout box<div style=\"padding-top:10px;\">Expected Result </div><div>PNR summary displays with seat assigned and efee present </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:12.950995"}, "746737": {"id": 746737, "title": "APP - Android FC Enrolment failing", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-29T12:23:39.623Z", "changed_date": "2025-09-29T13:13:28.517Z", "priority": 1, "tags": "FC Enrollment; UAT", "repro_steps": "<div style=\"box-sizing:border-box;\">Enrol a new FC member </div><div style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\">Expected: Enrolment is successful </div><div style=\"box-sizing:border-box;\">Actual: User is observing an error message on the front end (&quot;We are currently unable to process your request. Please try again later. (CSV0994)&quot;) and we are observing a 500 internal server error on the enrol service call. </div><div style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\">Emails used: <EMAIL> </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Please note: This is only an issue on Android and is working as expected on iOS </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Below are the screenshots.&nbsp; </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c04c0af3-cc89-4f1f-9279-9c1c951350a0?fileName=image.png\" alt=Image><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/9292bcc1-afad-436e-a9a7-511a1b0272b1?fileName=image.png\" alt=Image><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/69f6efe7-b83c-436b-9fd9-164a526f9421?fileName=image.png\" alt=Image><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c789ab89-a25e-466d-a380-bb4725a9871d?fileName=image.png\" alt=Image><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/9d16d539-53c8-4781-9af9-18dd18db32ed?fileName=image.png\" alt=Image><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/a2359ce2-b5fa-481a-9c09-855489ed4775?fileName=image.png\" alt=Image><br> </div><br>", "system_info": "", "updated_at": "2025-09-30T11:53:13.003290"}, "746733": {"id": 746733, "title": "*Query*APP - Android UI has dark mode dock even on light mode", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-29T12:13:41.953Z", "changed_date": "2025-09-29T12:59:40.05Z", "priority": 4, "tags": "UAT", "repro_steps": "<div>Launch the app </div><div><br> </div><div>Expected: Dock at the bottom reflects the colour as per device mode (light or dark) </div><div>Actual: Dock is shown in dark mode colours even when device is set to light mode </div><div><br> </div><div>Build: Android 7.5b23695 </div><div><br> </div><div>Kindly confirm if this is as expected </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/fb2f2d7a-4c5e-476f-8cac-64de2869f666?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.050210"}, "745828": {"id": 745828, "title": "********* - IROPS scenario not working [\"errorCode\": \"IRP30001\",]", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-26T11:57:57.947Z", "changed_date": "2025-09-30T10:13:26.16Z", "priority": 2, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">We\nare trying to test the IROPS scenarios on the stg website and when clicking on\nAccept flight we are getting the below error </span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">{</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;errorCode&quot;: &quot;IRP30001&quot;,</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;errorMessage&quot;: &quot;Did not find IropTrip matching the selected\nindex&quot;,</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;mealsList&quot;: [],</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;reason&quot;: &quot;Did not find IropTrip matching the selected\nindex&quot;,</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;status&quot;: &quot;FAIL&quot;,</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;totalTrips&quot;: 0,</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;&nbsp;&nbsp;\n&quot;tripsList&quot;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><u><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">PNR\ndetails: </span></u> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">VS\nRECORD LOCATOR <b>COYAKR</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ETKT\nPRESENT-SEE ETR* AND *TI</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">1.\n1DHAWAN/VINAY</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">1\nVS 155Y 28SEP7 LHRLAS HK1&nbsp;&nbsp; 435P&nbsp;\n715P&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">**IROP**&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;2\nVS 155Y 29SEP1 LHRLAS HK1&nbsp;&nbsp; 435P&nbsp;\n715P&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">**IROP\nPROTECT**&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">HA\nFAX- ** SSRS PRESENT **</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;\n1.OSI TYPE A</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">FONE-LON12</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">TKT-TK/TE/TL30/1100P/26SEP/26SEP1134/LHR</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">¥&gt;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Kindly\nassist</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"></span><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/04faf0a3-545b-40e0-a121-7229b096f4ea?fileName=image.png\" alt=Image></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">What is the BP? <i><u>High</u></i> – ( it is blocker for us for\nreplicating the IROPS scenarios for our MobileApps test scoping)</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">What is the Severity? <i><u>High</u></i></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">How many instances was this scenario executed?&nbsp; <u>For\nmultiple PNRs from today morning.</u></span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">How many testers observed this error vs how many times was it\nsuccessful? – <i><u>Trips Test team and has stopped working completely from\ntoday</u></i> </span><span lang=EN-US style=\"font-size:11.0pt;color:black;\"></span> </p><br><p> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.191295"}, "745351": {"id": 745351, "title": "B-1266450 - Unable to check in from SNAPP", "description": "", "work_item_type": "Bug", "state": "Resolved", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-24T13:03:23.01Z", "changed_date": "2025-09-30T10:21:47.33Z", "priority": 1, "tags": "SNAPP NG", "repro_steps": "<div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">We are unable to complete the Check-in form SNAPP\napplication.<br>\nWe are getting the below error: </p><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"> </div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp;<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/4262943d-278a-47c5-97e1-13b56b8f22dc?fileName=image.png\" alt=Image> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Also, we have noticed that the SNAPP version in TVTH\ncurrently is <b>25.9.2.0</b>. Could you please confirm if there was new build\ndeployed? </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Please find the additional details below: </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Priority: 1 </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Severity: 1 </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Instances executed: Tried with multiple PNRs and different\nflights </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Testers vs Success : 2 testers with zero success. </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Impact: Unable to share test data for Retro credit Activity. </p><br> </div> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.257263"}, "745171": {"id": 745171, "title": "********* - Unable to complete the reshop scenarios from DF", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-24T07:20:35.7Z", "changed_date": "2025-09-26T12:08:10.01Z", "priority": 1, "tags": "Environment-DL; Reshop", "repro_steps": "<p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">We are unable to complete the reshop\nscenarios - ADD collect and Even exchange from DF due to the below error.</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">Please find the below PNR’s and Could you\nplease investigate the issues.</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">CM4ZOE</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">CM43GN</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/f36e8416-cb94-4360-872b-ff2c9896b661?fileName=image.png\" alt=Image><span style=\"font-size:11pt;\">What is the BP?</span></span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">BP is 1</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">What is the Severity?</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">Critical</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">How many instances was this scenario executed?&nbsp;</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">Two</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">How many testers observed this\n     error vs how many times was it successful?</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:Calibri, sans-serif;font-size:11pt;\">Two\ntester and zero successful attempts</span> </p>", "system_info": "", "updated_at": "2025-09-30T11:53:13.336395"}, "745034": {"id": 745034, "title": "********* Df | Staging/ TVTH/AWS / Offer Selected offer / No selected offer in OfferPrice", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-23T12:52:08.787Z", "changed_date": "2025-09-24T09:24:46.903Z", "priority": 1, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">We are experiencing an Offer issue in the\nnon-production environment. Today, Search selected offer sent null from Delta,\nand it's impacted the&nbsp;Search and book Summary page for the Digital team.</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">Environment: Staging/ TVTH/AWS / Offer\nSelected offer / No selected offer in OfferPrice</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">Issue: No selected offer in OfferPrice<br>\nError Message: No selected offer in OfferPrice</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">URL: <a href=\"https://protect.checkpoint.com/v2/r02/___https:/offer-api.vs-pax-ofr-si.aws.vs.air4.com/xndwr-tkkjw-lvq___.YzJlOnZpcmdpbmF0bGFudGljYWlybGluZXM6YzpvOjgzZmU2YmVkYTU2ZDhmMGIyYzQ0ZjZlODJkNmNjMTBmOjc6YzkwZToxZmExNjc1YjgwNmIxOTcyNGUxMGNjNTE4NGI2OWE4NzE0ODZjMGQ5ZmQ0NGI5YTJlODlhYWQ4ODJiNzk0ZmMyOmg6VDpU\" title=\"Protected by Check Point: https://offer-api.vs-pax-ofr-si.aws.vs.air4.com/...\">https://offer-api.vs-pax-ofr-si.aws.vs.air4.com/si/rm-offer-gql</a></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">Channel: DF / WEB<br>\nPriority: High<br>\nImpacted: Intermittent issue ( percent wise its 80% failure ), but it\nhas&nbsp;majorly affected all journeys. </span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">BP: High<br>\nHow many times was it successful? zero success</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">PaymentReferenceId:\n&quot;3779BFFC17E6460B9BD02402F469C3C3&quot;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">Please find\nthe attached request and response.</span> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.418824"}, "744919": {"id": 744919, "title": "Unable to view upsell EMD in vsterm TVTH", "description": "<span><p>We are trying to check upsell EMD in vsterm below are the findings. </p><p><span>In TVTH for upsell EMD&nbsp;we are unable to view CUR and Total under TAX/FEE charges but in PROD we are able to&nbsp;view Taxes, CUR and Total under EMD details and attached are the details of PROD&nbsp;PNR with upsell EMD.</span> </p><p><span>&nbsp;TVTH</span> </p><p><span><img alt=undefined src=\"data:image/jpeg;base64,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\"></span> </p><p><br> </p><p><span>PROD</span> </p><p><span><img alt=undefined src=\"data:image/jpeg;base64,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\"></span> </p><p>Actual- In TVTH currency details, Total amount not displaying for EMD upsell </p><p>Expectation-&nbsp;<span style=\"display:inline !important;\">In TVTH currency details, Total amount should display.</span> </p></span><br>", "work_item_type": "Bug", "state": "Active", "area_path": "Air4 Channels Testing", "assigned_to": "Purohit, Swetalayan", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-23T09:28:10.723Z", "changed_date": "2025-09-30T10:25:43.097Z", "priority": 3, "tags": "EMD; Environment-DL", "repro_steps": "<div>Create a booking from any channel and try upsell on the same booking. </div><div>Now check the EMD details in vsterm </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.507481"}, "744823": {"id": 744823, "title": "PN Master data not loading to TRN enviroment", "description": "<div>When trying to carry out a component change in the A/C window The list of parts in the removal field and in the Install field when parts are issued is not available. No data is displayed. </div><div><br> </div><div>This is the same for both QuickTurn and TaskControl. </div><div><br> </div><div>A sync of the Pn Master does not provide any data. </div><div><br> </div><div>screen shot attached </div>", "work_item_type": "Bug", "state": "New", "area_path": "Engineering Transformation Programme\\Product", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-22T15:09:12.23Z", "changed_date": "2025-09-22T15:16:28.217Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:13.584729"}, "744311": {"id": 744311, "title": "DF | Staging/ TVTH/AWS / Order failure / unable to process - System Error", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-22T08:16:47.777Z", "changed_date": "2025-09-24T14:07:28.933Z", "priority": 1, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">We are experiencing an order issue in\nthe non-production environment. Today, none of the bookings are working, which\nhas impacted the entire Digital team.</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Could you please check on this issue?</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Environment: Staging/ TVTH/AWS / Order\nfailure / unable to process - System Error</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Issue: Unable to process - System\nError<br>\nError Message: Unable to process - System Error</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Channel: DC / WEB<br>\nPriority: Critical<br>\nImpacted: All Revenue, Redemption, PaywithMiles journey testing impacted<br>\nBP: Critical<br>\nHow many times was it successful? zero success</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;\">PaymentReferenceId:\n&quot;3779BFFC17E6460B9BD02402F469C3C3&quot;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span><span style=\"font-size:11.0pt;\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\">Please find the attached request and\nresponse.</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:10pt;font-family:Aptos, sans-serif;margin:0cm;\"><span style=\"font-size:11.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"><b>Impact: 2 days(complete blocker)</b></span> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.702315"}, "743712": {"id": 743712, "title": "RM Ancillary - OCI Unable to complete extra bags purchase", "description": "", "work_item_type": "Bug", "state": "Resolved", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-18T11:00:19.353Z", "changed_date": "2025-09-30T09:42:18.02Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Retrieve a booking within check-in window on Online Check-in </div><div>Purchase extra bags </div><div><br> </div><div>Expected: Bags purchase is completed </div><div>Actual: User is shown an error message and purchase is not completed </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/9850d86f-2ad2-4735-a1e6-d053e02afc85?fileName=image.png\" alt=Image><br> </div><div><span><br></span> </div><div><span>&quot;paymentReferenceId&quot;: &quot;3850B387B62241C0B342AFD7589A7A42&quot;,<br></span><span></span><br> </div><div>Cards used (the same cards work for seat purchases/upgrades): </div><div>****************&nbsp;<br> </div><div>****************&nbsp;<br> </div><div>**************** </div><div>*************** </div><div><font face=\"WordVisi_MSFontService, Calibri, sans-serif\"><span style=\"font-size:14.6667px;\"><br></span></font> </div><div>PNRs: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/58842e20-ff2b-43e8-bfaa-ee37b40f46f6?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/3c6924da-7005-4774-9c31-8c717d047af0?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/a6b7f6c0-7316-4bf2-a079-6947508d66c8?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.779542"}, "743689": {"id": 743689, "title": "********* - (Seat Availbility V8) - Seat Map Response - Attribute - availability : FREE is not getting updated to OCCUPIED for DL Cabins", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-18T10:43:36.64Z", "changed_date": "2025-09-29T10:33:23.163Z", "priority": 2, "tags": "DF-OCI; Environment-DL", "repro_steps": "<div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">We would like to report the below issue. </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">When replicating the select free seat scenarios for DL.....\nonce the seat is selected and confirmed , we retrieve the seat details in the\ntrips API which is expected. </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">When retrieving the seat map with new reservationID, the\nseat availability status is not getting updated from FREE to OCCUPIED. </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><ol style=\"margin-bottom:0cm;margin-top:0cm;\" start=1 type=1>\n <li style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"\">What is the BP? 2</span> </li>\n <li style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"\">What is the Severity?\n     High</span> </li>\n <li style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"\">How many instances was\n     this scenario executed?&nbsp;</span> </li> </ol><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Three to four scenarios were executed </p><ol style=\"margin-bottom:0cm;margin-top:0cm;\" start=4 type=1>\n <li style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"\">How many testers\n     observed this error vs how many times was it successful</span> </li> </ol><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">One tester and zero successful attempts </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:13.858221"}, "743268": {"id": 743268, "title": "summary data file", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Engineering Transformation Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON> .", "created_date": "2025-09-17T10:51:27.673Z", "changed_date": "2025-09-17T10:55:22.647Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:13.902365"}, "743218": {"id": 743218, "title": "Large Number of Taskcards in a workorder can be processed easily without exception", "description": "", "work_item_type": "Feature", "state": "Closed", "area_path": "Engineering Transformation Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-17T09:47:29.327Z", "changed_date": "2025-09-18T07:39:08.28Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:13.957165"}, "743042": {"id": 743042, "title": "TC Print from Prod Control gives 504 Gate way timed out error", "description": "<div>In Production Control TRN, When trying to see the task card print from the Task Card item window the print does not appear and the below error code is displayed<h1 style=\"font-family:&quot;Times New Roman&quot;;text-align:-webkit-center;\">504 Gateway Time-out </h1><div>This seams to happen a lot of times, it gets fixed then does not work again??? </div><div>This was carried out on WO 21059 in the TRN environment and TC&nbsp;A350-ZL-417-00Z01/RH. </div><div><br> </div><div>This WO is now status complete, due to testing requirements,&nbsp;<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e68f61d5-3b46-4932-9b84-f9b10d6045ef?fileName=image.png\" alt=Image><br> </div> </div>", "work_item_type": "Bug", "state": "New", "area_path": "Engineering Transformation Programme\\Product", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-16T12:18:08.577Z", "changed_date": "2025-09-16T12:18:08.577Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:14.035152"}, "742707": {"id": 742707, "title": "D-192568 DotCom/Reshop - Unable to access Reshop flow", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-15T12:12:15.993Z", "changed_date": "2025-09-16T13:02:05.847Z", "priority": 1, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Retrieve a reshop eligible booking on My Booking </div><div style=\"box-sizing:border-box;\">Click on Change Flight </div><div style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\">Expected: User is able to navigate to the Reshop flow </div><div style=\"box-sizing:border-box;\">Actual: User is not able to navigate to Reshop flow and is shown an error message </div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/d0dd4d4d-4e3f-4bcd-9f61-c3472572add2?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>PNR: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7d1299b3-b8ad-491c-9416-8f0886df2e2e?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.096887"}, "742568": {"id": 742568, "title": "********* - DF | Staging / TVTH / AWS / Order Failure / PNR creations are failing in the TVTH environment for DF and Axis", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-15T07:50:01.11Z", "changed_date": "2025-09-17T11:01:46.407Z", "priority": 1, "tags": "Environment-DL", "repro_steps": "<p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">We are unable to create any PNRs in the TVTH environment\ntoday.<br> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">\nCould you please check the issue? </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b>&nbsp; </b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b>Digital First:&nbsp;</b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b>Below e</b><b>rror code was returned in GQL response.</b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">POST:https://testresources.virginatlantic.com/SIT/GraphQL/Book/public\nHeaders: Authorization: api-channel:Web Monitoring,Azure-Pipeline\napi-diagnostics:true User-Agent:TestAutomationHttpClient/v1.0\nAccept-Encoding:gzip,deflate,br StatusCode: OK Request: {&quot;pqId&quot;:&quot;bookOrderCreate&quot;,&quot;variables&quot;:{&quot;request&quot;:{&quot;basketId&quot;:&quot;26f1feb9-0db5-4048-92f3-6d20b44b0824&quot;,&quot;paymentStatus&quot;:&quot;Authorized&quot;,&quot;deviceInfo&quot;:null,&quot;ipAddress&quot;:&quot;***********&quot;,&quot;browserInfo&quot;:&quot;PostmanRuntime/7.29.2&quot;,&quot;ccCheckConsent&quot;:false}}}\nResponse: {&quot;errors&quot;:[{&quot;message&quot;:&quot;Request\nfailed.&quot;,&quot;extensions&quot;:{&quot;code&quot;:&quot;500&quot;,&quot;reason&quot;:&quot;Internal\nServer\nError&quot;,&quot;response&quot;:&quot;{&quot;IsFailure&quot;:true,&quot;IsSuccess&quot;:false,&quot;Error&quot;:&quot;[{\\&quot;code\\&quot;:\\&quot;G051\\&quot;,\\&quot;message\\&quot;:\\&quot;{\\\\\\&quot;code\\\\\\&quot;:\\\\\\&quot;ROR4013\\\\\\&quot;,\\\\\\&quot;message\\\\\\&quot;:\\\\\\&quot;Unable\nto create Fight\nOrder\\\\\\&quot;,\\\\\\&quot;developerMessage\\\\\\&quot;:\\\\\\&quot;OrderApiService.createOrder-channelId:DFWEB,applicationId:DC,OFFER-IDs:[252523ae-46a9-4d1c-86c0-32305325354b-FL-1,\nb9c2aba2-0aae-410e-a1e9-601933187145_FA],FlightOrderComposerWorkflowStepImpl.invokeFlightOrderComposer-channelId:DFWEB,applicationId:DC,transactionId:aabaa0e1-abb6-47c1-a278-924d73105db3FE5038:Unable\nto Ticket - Account number check digit failed:ACCT NO FAILED CK DIGIT\nTEST/5/FOPCA51010Y1MHKPQ6034/12-28/C\\\\\\&quot;,\\\\\\&quot;moreInfo\\\\\\&quot;:[{\\\\\\&quot;code\\\\\\&quot;:\\\\\\&quot;4042\\\\\\&quot;,\\\\\\&quot;message\\\\\\&quot;:\\\\\\&quot;ACCT\nNO FAILED CK DIGIT\nTEST/5/FOPCA51010Y1MHKPQ6034/12-28/C\\\\\\&quot;}]}\\&quot;,\\&quot;developerMessage\\&quot;:\\&quot;{\\\\\\&quot;code\\\\\\&quot;:\\\\\\&quot;ROR4013\\\\\\&quot;,\\\\\\&quot;message\\\\\\&quot;:\\\\\\&quot;Unable\nto create Fight\nOrder\\\\\\&quot;,\\\\\\&quot;developerMessage\\\\\\&quot;:\\\\\\&quot;OrderApiService.createOrder-channelId:DFWEB,applicationId:DC,OFFER-IDs:[252523ae-46a9-4d1c-86c0-32305325354b-FL-1,\nb9c2aba2-0aae-410e-a1e9-601933187145_FA],FlightOrderComposerWorkflowStepImpl.invokeFlightOrderComposer-channelId:DFWEB,applicationId:DC,transactionId:aabaa0e1-abb6-47c1-a278-924d73105db3FE5038:Unable\nto Ticket - Account number check digit failed:ACCT NO FAILED CK DIGIT\nTEST/5/FOPCA51010Y1MHKPQ6034/12-28/C\\\\\\&quot;,\\\\\\&quot;moreInfo\\\\\\&quot;:[{\\\\\\&quot;code\\\\\\&quot;:\\\\\\&quot;4042\\\\\\&quot;,\\\\\\&quot;message\\\\\\&quot;:\\\\\\&quot;ACCT\nNO FAILED CK DIGIT TEST/5/FOPCA51010Y1MHKPQ6034/12-28/C\\\\\\&quot;}]} | ErrorID:\naabaa0e1-abb6-47c1-a278-924d73105db3 | TimeStamp: 9/15/2025 3:22:47\nAM\\&quot;,\\&quot;errorSource\\&quot;:\\&quot;DeltaServices\\&quot;}]&quot;}&quot;}}],&quot;data&quot;:{&quot;bookCreateOrder&quot;:null}} </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Also please find the below additional details: </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">&nbsp; </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Priority : Critical<br>\nImpacted : All Booking journey testing impacted<br>\nBP : Critical<br>\nHow many times was it successful? zero success </p><div><br> </div><div><b>Axis:&nbsp;</b> </div><div><b><br></b> </div><div><b><div style=\"font-weight:400;font-size:12pt;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;margin:0px;color:black;\">We are also unable to complete the booking from Axis due to CK digit error. We have tried with another card as well but still getting the same error. </div><div style=\"font-weight:400;font-size:12pt;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;margin:0px;color:black;\"><br> </div><div style=\"font-weight:400;font-size:12pt;font-family:Aptos, Aptos_EmbeddedFont, Aptos_MSFontService, Calibri, Helvetica, sans-serif;margin:0px;color:black;\"><img tabindex=0 style=\"margin:0px;max-width:100%;cursor:pointer;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/2bfd86cd-25fb-4f46-89c0-038f110ac436?fileName=image.png\" alt=Image> </div><br></b> </div><div><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\">Priority : Critical</span><br style=\"box-sizing:border-box;font-family:Aptos, sans-serif;font-size:14.6667px;\"><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\">Impacted : All Booking journey testing impacted</span><br style=\"box-sizing:border-box;font-family:Aptos, sans-serif;font-size:14.6667px;\"><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\">BP : Critical</span><br style=\"box-sizing:border-box;font-family:Aptos, sans-serif;font-size:14.6667px;\"><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\">How many times was it successful? zero success</span><br> </div><div><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\"><br></span> </div><div><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\"><br></span> </div><div><span style=\"font-family:Aptos, sans-serif;font-size:14.6667px;display:inline !important;\">Impacted Items: This issue is blocking IDP release,Retro credit activity test data creation,Groundspan testing ,&nbsp;</span> </div><br> ", "system_info": "", "updated_at": "2025-09-30T11:53:14.221163"}, "742417": {"id": 742417, "title": "DotCom - Delight seat purchase failing post booking(D-192394)", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T15:23:08.17Z", "changed_date": "2025-09-15T10:54:00.26Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Copy of defect <a href=\"https://virginatlantic.visualstudio.com/Air4%20Channels%20Testing/_workitems/edit/738729/\" data-vss-mention=\"version:1.0\" target=_blank rel=\"noopener noreferrer\">#738729</a> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.278271"}, "742352": {"id": 742352, "title": "D-192387- DotCom - Saved Email Address wraps to next line", "description": "", "work_item_type": "Bug", "state": "Deferred", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T12:06:02.333Z", "changed_date": "2025-09-15T13:11:46.183Z", "priority": 3, "tags": "UAT", "repro_steps": "<div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Contact information section&nbsp; </div><div style=\"box-sizing:border-box;\">Navigate to Email section </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Expected: Saved email should be displayed in one line </div><div style=\"box-sizing:border-box;\">Actual: Saved email wraps to the next line </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/4d88569c-fb09-4e59-9c07-67a803103b1d?fileName=image.png\" alt=Image><br> </div><br>", "system_info": "", "updated_at": "2025-09-30T11:53:14.350767"}, "742341": {"id": 742341, "title": "DotCom - Contact Information incorrect font for \"Set as Primary\" message/Emergency Contact", "description": "", "work_item_type": "Bug", "state": "Deferred", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T11:33:56.38Z", "changed_date": "2025-09-16T13:04:36.877Z", "priority": 3, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Contact information section&nbsp; </div><div style=\"box-sizing:border-box;\">Click on Edit Phone Number </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Expected: &quot;Set as Primary&quot; message is in the right font </div><div style=\"box-sizing:border-box;\">Actual: Incorrect font is used for the &quot;Set as Primary&quot; message </div> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/6071e7dd-a44d-4c30-8fde-c05b67ad096a?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/603d4477-508e-4d1d-8ea3-403a3419212b?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.412768"}, "742339": {"id": 742339, "title": "D-192330- DotCom - Accessibility - Passport Details Gender field not selectable from arrows", "description": "", "work_item_type": "Bug", "state": "Deferred", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T11:28:08.473Z", "changed_date": "2025-09-16T13:03:48.207Z", "priority": 3, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Passport Details section&nbsp; </div><div style=\"box-sizing:border-box;\">Tab to the Gender field </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Expected: User is able to select a gender by tapping the arrow buttons </div>Actual: User is unable to select gender by tapping arrow buttons </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/11c58ef0-4336-4341-a9c8-4d74ae2fd1c2?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.491710"}, "742331": {"id": 742331, "title": "DotCom - Secure Flight Info getting \"successfully saved\" prompt but information not saved", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T10:54:39.957Z", "changed_date": "2025-09-18T07:01:14.827Z", "priority": 1, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Secure Flight Info section&nbsp; </div> </div><div>Click on Edit </div><div>Enter a Known Traveller Number/ Redress Number or Canadian Travel Number </div><div>Click on Save </div><div><br> </div><div>Expected: Updated details are Saved </div><div>Actual: User sees the &quot;Your information has been saved successfully&quot; prompt but information isn't actually saved&nbsp; </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7b61a135-c261-4a46-954d-fa304eeeaa5c?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><div style=\"box-sizing:border-box;\">FC accounts used: </div><div style=\"box-sizing:border-box;\"><table width=87 style=\"box-sizing:border-box;border-collapse:collapse;margin:0px;width:65pt;\"><colgroup style=\"box-sizing:border-box;\"><col width=87 style=\"box-sizing:border-box;width:65pt;\"></colgroup><tbody style=\"box-sizing:border-box;\"><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right width=87 style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);width:65pt;\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">1500527559 </td></tr></tbody></table> </div><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.569957"}, "742330": {"id": 742330, "title": "DotCom - Passport Details Country/Region of Issuance has no dropdown for selection", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T10:48:15.273Z", "changed_date": "2025-09-15T09:50:02.877Z", "priority": 2, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Passport Details section&nbsp; </div><div><br> </div>Expected: Country/Region of Issuance has a dropdown </div><div>Actual: No dropdown is present </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/58683c40-f124-48e2-a32f-af1a1d40bb54?fileName=image.png\" alt=Image> </div><div><br> </div><div><div style=\"box-sizing:border-box;\">FC accounts used: </div><div style=\"box-sizing:border-box;\"><table width=87 style=\"box-sizing:border-box;border-collapse:collapse;margin:0px;width:65pt;\"><colgroup style=\"box-sizing:border-box;\"><col width=87 style=\"box-sizing:border-box;width:65pt;\"></colgroup><tbody style=\"box-sizing:border-box;\"><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right width=87 style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);width:65pt;\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">1500527559 </td></tr></tbody></table> </div><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.641644"}, "742321": {"id": 742321, "title": "DotCom - Secure Flight Info has \"TSA PRE\" misspelled", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T10:29:25.453Z", "changed_date": "2025-09-15T14:07:30.323Z", "priority": 2, "tags": "UAT", "repro_steps": "<div><div style=\"box-sizing:border-box;\">Login to Flying Club </div><div style=\"box-sizing:border-box;\">Navigate to Profile section </div><div style=\"box-sizing:border-box;\">Navigate to Personal Details and Secure Flight Info section&nbsp; </div><br>Expected: Program names have the right spelling </div><div>Actual: TSA Pre is mentioned as &quot;TTSA Pre&quot;&nbsp; </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/6a5b1ec5-b9da-4b21-94bc-8c19ceb2b083?fileName=image.png\" alt=Image><br> </div><div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">FC accounts used: </div><div style=\"box-sizing:border-box;\"><table width=87 style=\"box-sizing:border-box;border-collapse:collapse;margin:0px;width:65pt;\"><colgroup style=\"box-sizing:border-box;\"><col width=87 style=\"box-sizing:border-box;width:65pt;\"></colgroup><tbody style=\"box-sizing:border-box;\"><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right width=87 style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);width:65pt;\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">********** </td></tr><tr height=21 style=\"box-sizing:border-box;\"><td height=21 align=right style=\"box-sizing:border-box;padding-top:1px;padding-right:1px;padding-left:1px;color:black;vertical-align:bottom;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">1500527559 </td></tr></tbody></table> </div> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.709924"}, "742315": {"id": 742315, "title": "DotCom - Profile Personal Details inconsistent spacing between sections", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-12T10:19:33.567Z", "changed_date": "2025-09-15T09:49:37.543Z", "priority": 3, "tags": "UAT", "repro_steps": "<div>Login to Flying Club </div><div>Navigate to Profile section </div><div>Navigate to Personal Details section&nbsp; </div><div><br> </div><div>Expected: All the sections have consistent spacing </div><div>Actual: Spacing between Personal Details and Secure Flight Info section is inconsistent </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e823bad1-f342-481e-b6e7-7d85bd0c4a22?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>FC accounts used: </div><div><table width=87 style=\"border-collapse:collapse;width:65pt;\">\n\n <colgroup><col width=87 style=\"width:65pt;\">\n </colgroup><tbody><tr height=21>\n  <td height=21 align=right width=87 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:12pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);width:65pt;\">********** </td>\n </tr>\n <tr height=21>\n  <td height=21 align=right style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:12pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">********** </td>\n </tr>\n <tr height=21>\n  <td height=21 align=right style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:12pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-size:11pt;font-family:&quot;Century Gothic&quot;, serif;border:1px solid rgb(212, 212, 212);\">1500527559 </td>\n </tr></tbody></table><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.789166"}, "742311": {"id": 742311, "title": "SNAPP 25.7.1 - Application is very much unstable", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON>, <PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-12T10:07:50.74Z", "changed_date": "2025-09-15T10:57:59.047Z", "priority": 2, "tags": "SNAPP NG", "repro_steps": "<div>SNAPP application is very much unstable.<br> </div><div><br>We are unable to search flights<br>unable to manage flights<br>Unable to add docs. </div><div><br> </div><div><img src=\"data:image/png;base64,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\" alt=\"\"><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/b6f1f7f9-75d2-48fa-943e-35c78c884e31?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/dcd7d98d-0093-4bf6-b767-82e34108405e?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:14.862856"}, "742309": {"id": 742309, "title": "INTFIN004 Urgent Reconciliation Required", "description": "<div>We can see that circa 1500 invoices have been marked as 'Received' in Tradeshift and only circa 170 of these are available in Fusion. I'm not sure how many of these have landed in eMRO. I've attached a list of invoices and credit notes out of Tradeshift Prod and need help reconciling what has/hasn't imported successfully.&nbsp; </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-12T09:54:25.5Z", "changed_date": "2025-09-15T12:29:28.72Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:14.939925"}, "742292": {"id": 742292, "title": "B-1257719 - Unable to view the data in PNR PDI, Booking and Ticketing Feeds", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-12T09:04:00.563Z", "changed_date": "2025-09-25T12:18:07.227Z", "priority": 1, "tags": "Booking Feed; PNR PDI Viewer; Ticketing feed", "repro_steps": "<div><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-US style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">We are\nunable to view the data in PNR PDI, Booking and Ticketing Feeds for the below\nPNR's created today. Could you please investigate the issue.</span><span lang=EN-US style=\"font-size:11.0pt;\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-US style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">DTBZOY</span><span lang=EN-US style=\"font-size:11.0pt;\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-US style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">DUDYTN</span><span lang=EN-US style=\"font-size:11.0pt;\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;\"><span lang=EN-US style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;\">DUD2O6</span><span lang=EN-US style=\"font-size:11.0pt;\"></span> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:15.011532"}, "742268": {"id": 742268, "title": "Cannot view Omnidox Converted URL Links in Fusion", "description": "<div><PERSON> sent an email on this on 11/09 - I cannot tag an integration on this bug as it is not integration related. </div><div>When clicking on the URL link in Fusion (<a href=\"https://portal.virgin-atlantic.com/imagearchive/Archive/202503/Image_1210055_25039578.pdf\" title=\"https://portal.virgin-atlantic.com/imagearchive/Archive/202503/Image_1210055_25039578.pdf\" style=\"font-size:14.6667px;font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;color:rgb(70, 120, 134);text-decoration:underline;\">https://portal.virgin-atlantic.com/imagearchive/Archive/202503/Image_1210055_25039578.pdf</a>) we get an error message... </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/5a11459f-eec2-4d40-a675-6448228a4165?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><span style=\"font-size:11pt;font-family:&quot;Century Gothic&quot;, sans-serif;\">his example link is not working in EBS or Fusion and is linked to an Omnidox token number as this transaction has an image number. Please can you investigate as the links that are failing to open seem to be consistent with this format:</span><br> </div><div><span style=\"font-size:11pt;font-family:&quot;Century Gothic&quot;, sans-serif;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/8d1d3239-c8db-4166-b278-c327a3c83558?fileName=image.png\" alt=Image><br></span> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-12T08:22:33.62Z", "changed_date": "2025-09-18T12:32:26.8Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:15.114960"}, "742104": {"id": 742104, "title": "DotCom - Economy Light Tooltip shows DL Basic Economy Messaging", "description": "", "work_item_type": "Bug", "state": "Resolved", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-11T14:52:47.597Z", "changed_date": "2025-09-15T13:14:08.24Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Create an Economy Light booking </div><div>Retrieve the booking on My Booking </div><div>Hover over View Seats tooltip </div><div><br> </div><div>Expected: No Tooltip is shown </div><div>Actual: <PERSON>ltip shows DL Basic Economy message </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c0cdda93-1a0c-49d3-90a7-1346082467c8?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>PNR: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e7a13e61-cb7f-4d49-9b34-f4ee15312a9b?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:15.190993"}, "742026": {"id": 742026, "title": "********* - Unable to perform Reshop scenarios", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "Purohit, Swetalayan", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-11T10:37:38.033Z", "changed_date": "2025-09-16T08:45:15.253Z", "priority": 1, "tags": "Environment-DL; Reshop", "repro_steps": "<p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">We are again facing issue while performing\nReshop scenario.&nbsp;</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Below is the screenshot of the error.</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/a608b564-8984-4ce6-8817-3769b6993ec7?fileName=image.png\" alt=Image><br></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\"><br></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Could you please check this issue?</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"\">&nbsp;</span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Please find the additional details below</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Severity - Critical</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Priority - 1</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">Zero instances passed</span><span lang=EN-US style=\"\"></span> </p><p style=\"margin-right:0cm;margin-left:0cm;font-size:12pt;font-family:Aptos, sans-serif;margin:0cm;background:white;\"><span lang=EN-US style=\"font-size:11.0pt;color:black;\">2 testers vs 0 success.</span><span lang=EN-US style=\"\"></span> </p><br>", "system_info": "", "updated_at": "2025-09-30T11:53:15.297088"}, "741915": {"id": 741915, "title": "ESIP_VAA_SP5_Weblog 37200_YIELDCHECK switch does not work in either position", "description": "", "work_item_type": "Bug", "state": "New", "area_path": "Engineering Transformation Programme\\Product", "assigned_to": "", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-11T08:03:59.393Z", "changed_date": "2025-09-15T15:45:22.55Z", "priority": 2, "tags": "", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">11/09/2025  08:57 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">B<PERSON> filed on &quot;TRAX_372000_<PERSON>_<PERSON> switch YIELDCHECK for Planning Control&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:15.383619"}, "741689": {"id": 741689, "title": "B-1256165 : DF | SI / TVTH |AWS | Order | | Error FAILED TO RETRIEVE INFORMATION FROM PAYMENT ENGINE API", "description": "<div><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">We are experiencing problem in payments. Please check and let us know.</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">&nbsp;</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Backend logs:</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">&nbsp;</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Below are the required details –</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Environment - staging /test / TVTH/AWS</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Endpoint -<span>&nbsp;</span><a rel=\"noopener noreferrer\" target=_blank href=\"https://order-api.vs-pax-odr-si.aws.vs.air4.com/si/orders\" title=\"https://order-api.vs-pax-odr-si.aws.vs.air4.com/si/orders\" style=\"margin:0px;\">https://order-api.vs-pax-odr-si.aws.vs.air4.com/si/orders</a></span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Channel – DFWEB</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Priority: Critical</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Impact: Regression test scenarios are failing</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Error- [{&quot;code&quot;:&quot;7522&quot;,&quot; #7522&quot;,&quot;developerMessage&quot;:&quot;Error Message: FAILED TO RETRIEVE INFORMATION FROM PAYMENTENGINE API --&nbsp; | | TimeStamp: 9/10/2025 8:22:40 AM\\\\\\&quot;,\\\\\\&quot;errorSource\\\\\\&quot;:\\\\\\&quot;DeltaServices\\\\\\&quot;}]\\&quot;}&quot;</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">&nbsp;</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">Front End error screenshot:</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">&nbsp;</span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\"><img src=\"data:image/png;base64,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\" tabindex=0 style=\"margin:0px;width:524.99pt;cursor:pointer;\"></span><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\"></span> </p><p style=\"color:rgb(36, 36, 36);font-size:11pt;font-family:Calibri, sans-serif;margin:0px;\"><span style=\"font-family:&quot;Century Gothic&quot;, sans-serif;margin:0px;\">&nbsp;</span> </p><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-10T11:07:16.103Z", "changed_date": "2025-09-15T06:35:50.21Z", "priority": 1, "tags": "DF; Environment-DL", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:15.498265"}, "741618": {"id": 741618, "title": "Position Attribute1, Attribute2 and Attribute3 were missing in the Productio.", "description": "<div>As we found that while checking the Position files then we get to know that Attribute1, Attribute 2 and Attribute 3 are not loaded in the Productions. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Data Migration", "assigned_to": "Mobeen Afzal", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-10T08:09:05.473Z", "changed_date": "2025-09-22T13:34:25.443Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "<div><div style=\"box-sizing:border-box;\">Action : 1. We will load the Position data with the DFF in dev3.<br style=\"box-sizing:border-box;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2. Then Generate the PCR from dev3 </div><div style=\"box-sizing:border-box;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3.&nbsp; Will send the email to <PERSON><PERSON><PERSON> for the confirmations on the PCR and ask them to review.<br style=\"box-sizing:border-box;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;4. Once we get the review from Mobeen then we can load into the productions . </div><br> </div>", "updated_at": "2025-09-30T11:53:15.578117"}, "741460": {"id": 741460, "title": "INTFIN043 - 4 Invoices in Fusion with Line Variance from eMRO Interface (missing 2nd VAT line)", "description": "<div><span><p>4 invoices in Fusion which are on hold due to line variance. &nbsp;This means the net + VAT does not equal to Gross in Fusion for these invoices. &nbsp;But the invoices balance in eMRO. </p><p>&nbsp; </p><p><span><img alt=image src=\"data:image/jpeg;base64,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\"></span> </p><p>&nbsp; </p><p>2 of these invoices (top 2) came up as an issue on Sunday, where the decimalisation was more than 2dps, which was fixed in the interface.&nbsp; Upon trying to identify where the difference is, on these invoices there is a second VAT value in eMRO, which is not accumulating and pushing into Fusion </p></span><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T14:55:27.913Z", "changed_date": "2025-09-10T10:30:52.12Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:15.802680"}, "741458": {"id": 741458, "title": "Time card error when trying to administer sickness", "description": "<div>When trying to add override certificate for sickness, the following error is returned. </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/cdd13afd-9457-443b-af78-a25c4eebd974?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>User&nbsp;<span style=\"font-family:-apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, &quot;Helvetica Neue&quot;, Arial, sans-serif;font-size:20px;font-weight:300;text-align:left;display:inline !important;\">572584</span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T14:46:03.967Z", "changed_date": "2025-09-17T13:27:28.91Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:15.888371"}, "741444": {"id": 741444, "title": "INTHCM016- Fidelity inbound file i.e Source file format is not correct.", "description": "<div><span><p>The file that we receive is a comma delimited file. The commas are missing in the new file as per the error we got. We wont be able to tell the exact places where its missing as we receive an encrypted file. But please find the sample screenshot of what is missing: The commas are not present in the file shared in prod. The file shared in UAT had correct format with commas.&nbsp; </p><p><span><img alt=image src=\"data:image/jpeg;base64,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***********************************/WH6D+VZFpcyWi6hLFEJHN7sCs+0fMVXrg+vpWE/41P1f5G9P+FP5fmNsNVnuH0kyX8ifajMWikjVWYKxCAjGQe3HXFXdcvLuzjs2tZMO9yFZCAfMXaxKjPQ8cH1xT01qAzw2826OeWRodgYMA4JGPXBI4OPyqNdfgXT0vLiC4t1kkMSI4XLkE9OcdFJ5xXfoc5mR+JbmHTYZJjE8jxLIrShkLho2cEgDj5l2/j60z/hJrxrq3V2tYDucNl3MbLhsE8ZHKjH+8K6J78HS3v7fMqeSZkGdu/Azj2rJsvF1pdWtxdEFbeEqpdXyS2CWGGx029s5BBFDAfDr9zLePDJZ/Z1EZfMr4K/Ju3EZztzx0qxYays9jZz3csEb3rhIUhZmyxXcV6dRhs9uKnk1EpNcIInZYbYXAwOZBz07dqj+3iWDT7p7ORWmkVV85RmPeOuRnGen88UdRFC41PUBc6gttcpI0UUrRIE3KhXGN4wGB69yG7YxT7nxF9mSzO6KczEb2i3BMbguVJPbOT16dquf23bedLEUuVaJmQbo+HYMFIX1OWX8/rVqzvVvYmkRJo9sjRssq7WDKcHj60IZiTapeJHr0639vutdyQR84TCjBI7nJI+vFbVizGwiL3PnErkzBshvcVnf8JGkUtwtzF5aQStEGRy3zBgoByAATnI57HNWY9Zt53txDHcS/aELqyR5AAYK2TnjBI/pmhbAYmla/qLiM3kyuoji8yQx7U+cvhwQPYAjsQfUUHxVdSIcpbpE0YzIjtuQlV9RjIL9/7preTUwVkLxS71uWgSNVy7kDP06c9cYqFtcR5bVbZJJklnWJ5NpCxlgTgn+97e/WgDMj8S3g06GUwRzMypvmViEUlMlW3Yw2Rj8fwrRttWmuNVa1P2eNEhjlZWkPmDcCSMdOMDn0NW9RvZbK3EscDTEtg4JwowTk4BOOMdO9Q2+qLdyTwvbSCKOJHaUlWiYMu7AOefTOMGhgUtR1aWePSZdKu/3V852t8o3oRkMN6n644zUkms3EAuEYRs0EchJYlSCg4Zx0CuemPXvU0WtWZtIXt4Z3jeU26RwxgkMFztwDjGBxg4qxaX5urm5hFtcRi3fZ5jgbW+UHjnP8VAGW+v3caWbC2hlW4Xfuil+U8gbATj5hz+XT0B4ilX7R5ywjaH8oxszZ2y+X8wOMZ4PXitC/1GWzmAW3EsYheaRvM2kKmM4GOTg+o6VFPr9vFHdmJJZ5LZQzRx4ywJ25HPr64NDAy18WXDtCy28HlMitIC7b1OWBHTH8PH1q7Z+It0F1cahGLSGAKSxbJUltoUgZO7OO3OelXJtbtbe8itZzLHPIFOxgMpuOF3c9yO2alv5tiwReQs8sswSNHOFDAFsk4OMbc9M9KPMCje391FrMdvDcAr8oMCrliSCfmyM7enKnjnIqsfEshtzIDaqwkgQ+ZIwA8wfNk+qnP5djV6112G5cxGC5RlZUkJTKK5JAG7POSCPy6Zqzpt+2o2Md19nntw4yFlxn8ME0AZx12eG/khuFg8qM7S0TOzFvK8zIGOnUevSqP/AAld06p5MFsX3srB3YdCmCMZ4+f17VrT619meV3t3NrFKYWlV8tvC7jhPTtnPXtjmnyayqzRRR2t1MXmMJ2KPlOzeDyeQQRQBUsfEMl1dOJoBBbrE0pkZ+VUKDuPPQ89u1Goas26zltL4pHcWzzRIYwfOPylBgjPOTwMGr+rXJtdJup/s32rYnMBbHmZ428gjnPeqtrr8EsUrmN1jjZuY/m2xqB87dNuM4I5xg0AU5vEN1L/AGjDaxqk9vCZI2kVjgq21gRnJx17fSp5ddnS7tYY4Y545QCZY3wrksVITJ7Yyeuc1cj1VpYb51tLlfsruo3AYkK+mDTZtTnE2nRQWzSG8Xe0n8KKACffvQgJNUuZbaGIpMYUaZUln27vLXB5546gDJ4GazLfUry41PT4JLyONHieRlwUebD4U47Arzj61pLqSgTl4pA0dx9nRFXLuSARgdOc564xQNXt/tFvbus0ctx92ORQGB54K5yDwe2PejS4EGvXtxZwWpguRC0k+wlmVQRtY9WBA5A7VFZaw813bxz3EaPJYiUoflw/Gcjr05HPSp49ZaTRxqAs7iIEgbZAOhbGeCeKnuNVgt5JUcTEREqzquV3hd23/exz/WjQDKTW76bTbG+8yxiiml2ync7qg2E4J4wcgfmBU1rrDrqVxBPNG1uZZfLcsQyBEVjnjGOT78Vp2V/HfxtLGsybSAVlXawyAw49wQaqSan++ngurdPsokaDeZM7v3e/5lIAAIyOpoAr3+rE/YprO/2RXFvJNEPLB84gKUGCM856DBqCXxDdS/2hBbRKlxBA0sZdWOGUgMpGcn9Kvxa1DK1olrbXEqXDOmYwuIynUHn+WaltNZtL68ktYJGaSPd24O07Tj6HjnFG+wFKbXZ47m1jjijuI5QCZY3wrndtITJ7DnvWpe3KWsG+WR40LBDKoz5eeNxzxgVTvdWmttVgsY7YSmZN4YyFe5yPukDgZ5Iz0qGPxNZSbfLgvtzMqqvkYLFsY7+6/n9aLgMstWvFS0iuf3ksioW3rtkk3HqoAAwo65pi65Mbc+TNazTNfPBl5DsVcEgjAzjj/wCvWiuol7m3CqBbzRyszNkOjJjII6ev5Un9tW37ssJ0EhQKXTA2tna554U4xn1xxR8wM+PxHcSzoEsWaN4RIoD/ADNmPdleeRn5enX8qs6NqE19c6gJJ4pFjePZ5JO0AoCcZGeufXn8g628Q2V3cxW8ZmEsoBVWXByV3BSM9SOfTkc1bsb9NRt1uI450jYAqZk27gfSmgM+XUZ4rm4LXDGSJn2WSx5LoBw27GRnrnp2xmqd14lu4ZQkUVpMuCTIHfa+COV46c4+orYW/laSVhbj7JC7o8plAIKjk7SOmeOvvUMeuxzvai2guZ1uHePKBfkKDnPP8s0vmBSh8SSvDPLIkW1DIAIizFdrYUvnHDZ/+v6MTxNM628nlQLGybpQzsGGJRH8vbvnn/69W7XV9KglntrXeGErEqgz5km7a23nruOOcVOmuWzqz7LlAhPmb48eWAxXLc8DII/D0oAgTW3hkvjqAjt7ewU/aZfmwCT8m31BTB+pAqK91O4h8SRWv20RWxjjZlIUncWIxjG45wOh461LHc6bBHdKthOsQuQs7PFlDJuVckk84O057D6Vo297Hc2f2pPMEXzH5hgkAkE49Djj1oWwGHH4luDp5nkWzWX9wwXzW27ZGKnJxnIwT9KvjVZheR28ixHJUNsZssCuTIuf4B05qSLU5jHFLNaMqzgG3WKTe8mQTjGBghRk8496SPW7Wa4EEaXDbsbXEfyElSwGfXCn8RQBlf8ACQzw2sAt5oLtmmmRmmc5AVwFPyj0YH6CnL4nuXWZxZqqpA0q75MZwm7PrtJyvT/CtOPWg+m217JaXiC4ZUSPYGbLDjoenbNJeatJbR3jLA2be1FyBICN3XI4z6fXNHzApHW7vdATJZxItw8Nwx3sv3Ay4PUZzj3IrQ1q6ltNGubiCby5EUFGOOuR6gj9Ka+uW8fmb4rpXjRndGj+ZQu0njPowP0zUkOotLJeqtncD7K5TdgYkIAPy8+9AGXFrF+IbRUaK6e5cxB2zhZcglSUABUKWO7vtoHiG8cTD7JHEySIgEsmNu6QJhsEnODu6D+taS6vCLeGWQP+8gWZyikiND3bPbP48U+z1SK9lkijSdGTP+tTaGAYqSPUZBFAFC21m5nvbPfJbR288TAqdxLSLJtIU/rg9qTV9ZI01bjTbtdwu/s7EkKCy5DLuYEDBHXHartzf3sOpQ20VkJI5ekpn2njG7Ax2B7kZqvFrtighit7W6/eEBY4oQNrMX+UjPByjE/n3o8gIbfUJp9XsYV1KN7c2vmnK7HmbdjOOmMDOBjuaqwa7exaYt20kV2807qxVsRwgZwOcYzxjmtWPX7Ca8S0SVzI5AwR0YjO0988emPerV9dSWlt56IHw6KwLEcFgP60WAzjq93JO0MSQRSbCVilLmTPl7gwAGCuePwPPaoxrbjTNPuJru2DyTRpMYyVHONwGfTPP9K1dRupLHTrm5jiM7QoWWLdt3kds9qyYfF1m/mPMksKBysZb7zqFUs5BxgAtjHJOOlHUB1xrOoQXN5Etmkv2VTIdpblG2+WQehPLE+mw+taOmXsl9ZCeRAjFivytkMAeowT1+tZ9x4nt445hFBcGSE9HiIVsNg7SM5PDY9cVPN4hsIJhFM0scuSGR1w0fIHzDPuOmaaAoprc8PnyG6+0qskqMNgxDiTamSMcEHue2c1Laa3cXU+nyFoIba5hYlHDFzIH27VPr7GtS3v1ubmaFIrhTA5jZ3TC7hjgHPPBBqK91GW0u4Yhblo5SoMrOVUEtjGcEZ74JGeAOaVgKVl4i329zcahGLSGELli2SpZtoQgZO7OO3fpS3mo3Nvq80MU7SNGsRSzCjMm4tvwcZyAAeuB+NOfX9OktIpp4pgkhV4UmiAL88EAnHBGecYx9KtQ6vBcGIW6XEySBSXSMlU3dN3p/SgDJHiW8NkJvsSg+YFLeYNqfKThueDkY5Pfp2pD4nvFuZFNlG6qSBGkjGQnaCF6YzkgVozavNHpjXUdpJJL9o+zrF3B37Mt/8AWqZ9T8iSVboGJIbX7RI5BxjPOPYfnQBkXHiG7bSJpQkUEmyTZMXJUkKCAuN3zZPQ5+6fpWpqGoNa+H5L0SN5nkAoVxkuQNvXjkkVC/iO3axmntUmmmjR28oLkjaASTg9ORyM9auXl81ppUt8Iw5ji83YWxnjJ5wf5UdGBj2+vXa2iRTmIXcTPHNJcAqHdSAAAmfmYEHAz/hZGq3Kx6qs9xapJbs/lDDKVXjaWz6549TUya9bi1hmnOxZZDEpRtynDbcjOCVyfT8KcdbijhupriC4gjtpzBucDDtkAbee5PfFL5gVzrF0W05bdIpkvY18uQlj84xvDEcDC5PuQRUukatcalJMs1sYNgBA35K8kbWGeox7VftLyK+tVuIGJjfI565BwQfxFZ2oa29rDqEkcAkWzeNDlj8xYBjwPQFfzPpVbANu9Rnh1mSFLonasJW1CAlwzMHOcZ4UZ6/zqqviO5urZ5bcwRCK5iWR5Q23ynGcnB49Dz+Va0OpLNeQQKp8ue2NxG2euCAQR/wJTUer6x/ZH2Z3gkljlZldkPKYXIOO+Tge2c0mgIYtZnl1kWX2ceSQMSbsFht3bwCfu546Uy71gS21rc2l95VnPG8iXIiLB2GNqgEd+T6nHHWmxeKbKS2Xz45UmMO+WBcPtOzeU7ZIHtili8QQCSCG2s7jysOpRYcMmwDGF6bcZH4cUAQxa5qM8V1H5Nul1GsgWNS7MjKm4Mw9M/Lgd619Ou2vLGOZpopGI+Zoc7c+2ao2ur6Ss1zHZ7ixLzv5YyJGAyxHPXH0rRtbpb23WdI5kRgCvmptJBGQcenNNAZNxq01tc6oUu45zbmIRwSYVV3EA5KjPGfetTTrtr21EjlQ4keN9jErlWKkjPbjNU5ddNtczR3FrKkcZwu3Jd8sqghcAEEsOQTjvinPr9ilzHbyNIkzEKyMozGS20BufX0z+VJAY+ma9qLkfaZlkAiDea0e2LDSMA5IHYDaR6jPGa0oNWupNSt0laCG3nh3KrBt7vuIO0+hAB57GrEmsD7JcXEVvcusURlVzGQkgHGQf8nHNPnv501O1s4bdnWaMyySn7qqCB+fNAGXPrMraLdyw31us6Xfk+ZuyqqXAyP+A5xnPQ9at/2kYNRFsbgTkWRkWNHy0jjB9OpHT+VSPrsFvbGe5Dxn7Q8CJ/ESufUgdBnrR/bUcs9olskksM8ojaXaQi5QuOfXGOD6+tAFC48SyW1g1wRbzNn5RDvKkBdxUk4w3bjP07Vdh1OY6leQzXFqkKoskAYMG2lAdxPQrnOTWtuPqaMn1p2YGBDrsn9l2cylJWeIs7Mx+dxj92uB98544qxYavNdajJbSrCq5lEex2Lfu2CndnjnPb0Na+T6mjJ9aLO4gyfU/nRk+p/OkoqgFyfU/nRk+p/OkooAkX7n41Hc/wDHpP8A9c2/kakT7n41Hc/8ek//AFzb+Rrmn8TKK8v+sP0H8qxI2s0tdVk1BWa1jui77QxIxtIPy88HHStuX/WH6D+VYtvZrfC9hkmMUYv/ADHxxvC7TtzkYyQORzxXNP8AjU/n+RvT/hT+X5l1INDlmt3U2vnAI8Q83DeqHGck8kjPPJqQW2k7ZAPs+GkO7bL0cZJxz8p6nAx3rPTwpEm3/TXYJgR5iXKKDkDPU49fari6IqhCtywki2CJvLX5VXOAR/EfmPJrv1OcekOl/b3mE8LzPaiDYZgcQjLcDPQ5yT3GKhs9M8P3lgpsra1ktWwR5JwD8gQZwf7gAx6AUWugLaywSC6LmIxdYVBYIjIASPUMalj066trS3tra/KxxzIWYxLnyl6oOOp4GT2zQAIulx6lhI3+0BfIL4kKABc7Cfu9Ox/nTTa6J9ngjL24h37oR9pIBYYHB3c44HtUsumrKbiH7U6QT7nkgULklhgnPXHfHr+VVrjw+tzuaS6JkdZFZvIXq6quQO2Ao+tGoD/K0WaS7VJLcTQlxMTJkxs2CxIJ46KfqKl08WdhBBaQXCzedK+1lYHc5yzdP89KJNLZxfKt0VS7Uh18pThioUnP0HT3pDpzKNPRbxIzbPuUCBR5hCkYx2+Unp6UK4hLVtJubWURGPy5pS0kbyEMJCxHIJyrEg9MUJ/Y/wDaFqqTQm6ERMAE5JKZ5xz83P16e1V7bQYlhUpfGXDF4ZFjTCkyCQnj73zAde351YttINq9uyXbFolZWJiX51Z9+PbnuO1GoxIrbSfs9y6qiw+cfNd5TjeO4YtwecZGPSnmz0h7uFdtsbiLa8SLJhhgcEKD6HrjpTI9Lb96ftwd/tPnqRCuI327SCO/HY/WmRaDHBcxzR3DDy3jYAxqSdgYYz1wd5+nahATatLp8aQ/2gjsCx8sIjsc9D9zkdcfjTGXRZk89zahXjER3vsG0rwpUkYO09CMgVa1C3+1WvlfaRb5dW3lQfukNjn3ArL1HR5r67v5Vij2S2jRxAycPKyld5GOMKdvfihgWRZaIIo2UwCMzb0YXBwZcYyDu5bHFXHt7a3mlvvJPnbfmZASW4x90dTwB0zWZJ4ciuo0e4mbzipV9owu0qAVwuB0Uc//AKq154XktHhhneB2XasqAFk9xnvTEVlhstVMdy0BZ4y0YEmVKEH5lIzjqB1z0rPtpvDc8sgh8ndPDIZN25RsEmH3Z4X5+3HPIq7a2c8P2Fbe+Q2sBcyhIx+9yMKO+ADkk5ySPrVAeFbXC/6Qd5GXdECmRgwZWOD22gYFIZfVNIWWFVkt/NUAxqJ/mYE5HfLZPTOags00aTSpGSWNreSY3MrTSgMsjnOWOcqew6YxioD4Xh3K63RQoBtPlKcEbjnJ56sT1pYfDVubSFftry+Wq+TKqqoABJyduN2dx/zzQBYhsNNttU84TQCR1jEMG5RsCghSozznnrn2qyRY6RFuWMxiV8BY1ZyzcnAUZPTJwPeobfR0tbu3mjlGyGAQCMxA8Z3ZDdRzVjULQXtuI2mWNActviV1PbkN+eRT6ARImk3V0Zl+zvcOmWBb5sFcElT0O04yRnHFRfZNEWzL5thbPIG8zz/lLgbfvbuuOOvSq0fh/wAyW8juXP2WVl2bWBd8RCPLNjIPB45zmpo9ASKMCO5IlyxZ2jDhgyhTlWyOgHP9OKQFyd7G6hubWco0McatMC2FC9Rkg8dM/lVK2/sS7ggmht02XUzNH28xj8zMQDyDjJz3xkVYGnsLu5db0fvbdYfL8pcoBnaffqfaoksd1rp8cWqRkWkg2usSHeyggDrwcZ6UdRE72+lu92zGHcwxc4mxj/ewfl6D0zimWVtpt3FbXFtEStsWjgO9vlwcEDnkZHvxioE8O28UskkczhmbdHkbthL7+5ww3DpgVd0y1WytDGlwJwZXk3hVAyzEkYXjgk00MpW39iTxXDouxHIllM5dMnOA/wAxGORjI9KkW20RdRhiQW4vI1WWJBIQ2BnawGef4ueTyagg8PxxWzRW9+6l2zK6KPnIYsM4PHUjAIzwasW+ii2S3RLtysVuLZ8ouZEGcc/wnk8ilqA9NO0q5tpYoo4pIXfLrHKSNw5xweOvSi5g02OdUnh3SyxPx8xJRVwxPvggZ684pdL0mLSkdYnZ9wVctnO1RgDqc9akmtQ2qW93vUBYpIXRv4lbaePcFfyJptaAQWFzpi28M9oCiXcgjBOeWC4APJxwuB68UX9tpNnbXd9ewqImBa4dtzDBwCcDOO3IHaq9vosENtbw21yjW5vBddudvIVcdeQMn0Bq1c2Fzd/bkmvQtvMYxCgjB8oLgtnPUsf0xQBBAmhTK0MRh2xXTJzIwzNgbgCT8xORnGQaS9/sV7O8Vp49lwvlTm3myVDkKTgH5eTycd6i/wCEVsgxCSFIwxAiVcKqHb8mAf8AZByajl8LwYJa/MZdwS3kp8x37gOffH+TSA0JbPSLl4DJ5DF4xHCFmIDoDkAAHDAH61WfRtIu9kVq8KPC6uwik3thSODzxyo568U4+HLd7hZ3mdpN26TjAc7t3ABwvP1q7Y2H2J7ljKshnmaUnyghBY5IyOvbr6U7AV5U0m3v7dHjIniUCPbvIjDE/exwASDy3XHNQ3VlpV1pU8MV3bQ2spVZ3V1cEdQmSSAPYfhVq804XN5HcNOAUAMcexQcjsH+8AeMis628NZ020jupitxBGiL5YAVNoIx8uN33jzxSA1pE0/7arO0KXMi/KPN2lhjGQM88d8VmzjRLfSrOViY7OK6WSBBJkPICQuMnkZ5AzjgGrcGjpbSo0ExWNdh2GJW5VdoIJ5Ax2H58mof7N2aciNqiBIrk3AmMKbQdxODzjG4n+VDAne30y7uZhLBG0giDS7zwFYEcjOAcDr1x3qCePQ7TTfOcQm2DtMpWUks2PmKnOWJHUZ5qS40tL24uTLebori3ETRIoBC5JB3A56k/wAqrTeHoBYzRm8MXmo6zSsikENjJwxIB4HNAF68tbGG1uriSPYrIWkZVZsd9wUd+/A7VTsYdEWztVFxBJuHmxmVwjPuYtypxxuPQjr71f1CyF9p7Wsk5jR9okYD7wBBI9s4x+NZR8KxM7n7a5QljGDEreWCXOAxyTzITzT6iLk9jYSWl7aW9ylu10xSdg4c7mzkYYkBjzT4YdMF/azR3EJnjtvIt0Eq48s4JKrnvtHPoKYNEQBcXL7o8eSdg+TDh+f73I71HD4fWF4XF2WaIx4JhXPyOz4z7liKWoya0t9GvoHksxBNGzctHITtIJ4BByvfgY60hi0qLUEjEBE6umCiOVRgpCZI+UHaT17Gl/s26gs2t7O/MQMqsrGJcxpv3OBxySMjJ6VNNYB5pgt08UVxkywqBl/l28E8gYx09KNQK7Wuhi2VWa2WES7l/wBI2jePQ7u3p2olh0W7vbyOVoZLgReXco0x+4RnBGcYxTJNASXBe5JcAjPkrjHlsg46AgMee/0qePTHheRo7sjzIVjcGJTkqu0N+XajXsBE9rokkEcHlwTRySmMBJN2WYcgkHJGAPwx7U+I6TcSXEgMay5MUwaQowwdnIzxkgDPfimDSDDYWNol6kYtpEKv5CDeV+6MevHbrUa6DDKJ2N4ZFkklkUrGg2O7ZPI+9ggDB9PyOoEoj0OF7S13WwbBECGXOQpGRyeQDjg9+1S20emwSzzQqkZg3RySFzgZO9hknHU5PvTI9IaN45Bet5qyyuX8lfmEhDMMduRwaP7MMk10xvdzSTJKFES/u3UDbn14A4PXrQBO/wDZ817byNLC10qkwfvucMOwzznHp2rLvtPsrG2D3lzKfNvftTskGTI+0jGEGQAB1HPvVkaCgnWYXJ3goxJiXJKyGQ/QEseB0FXdRt/tdhLbmfyBINpkwDjn34p26gV4W0jEd1G8MWxVA3P5ZXrgMpI564yM1JdNpl9af6RPbyW28DPn4XcOQMg9faql7pkt5qZudkbRC2Zc+ZgTS4IQkY42hnwcn73tUMfhtZLG2W5mIuIo1TKABVAXbjC4zwTzxS1A0ZNKs3lvJpYmlNzEkUilicomcKMc9ST7k1HbQabqHm3CW7eYZcSFywbeABzz6Y9qupEIbVYY22LHGEVv7oAwDzWfa2ciiFrPUI2ia6M9y0aLiUbcbFA4XJ2knrwfWn1EU9nhi7nNs0cTyO8oZH3jmM5k3Z6AF+/Bzxmrwj0VfJXzbVT9+P8A0jBbJ65zlgT65qpJ4YtZpGkef55GcyOqANJklgMg/wAJz9e9JJ4Vgkxm5YHBB/dA5J3ZOTk/xevalqMu2EulhLi5tnSMuxa43y4KtkglgT8vI9qkmXTHuop5ZLfz2CmJmlHzDPykDOG5PHXms+Pw5BJahWvmmUMzxyIirhjJvJyv3vmHQn9eatQaKkE9pIky7bdHTY0IO7ewZjknI5ApgMi0zTtJ0xIZwZRlUD7Tvds/KAF6Hn+HHepYU0i5mhdPJM+0GNGcq+AMjKE5yM9xkVZvrf7VbGMyrGu4MxeNXUgdiG4x/hWVBoBF3OJJCbRkiUHILybARy2MryexPHpQBa+x6KLFxuh+y+bvZvtJ2+ZnOd27rnnrUgh02W/ZwqyT+QNz+YWXyzxg84wevT3qtB4ejto1ENwyyIwKuyB+ApUAhiQeCeeKsxWDQ36ypeD5LcQiHyV+72PGO/tjtSApxNoeo6em2IS2skvkpuYnzC3Bwd2SuO3p2rSlSxmt5LGRomiRAJIjJyqj15yBVBNOH9m29nHqkZFtMuJBEhO5SCFIzgHI+tK3h62a5nm818ylmA67WYgk8nGMgcY9qEA+Oz0jV/MmWBJtspRn3HDMME8g8jhTn1560RtpFzLdOY9rNuMzSq0YbawDMM4HBAyR3xVjTbQWkdwBci4Ms7SuwRVCscZGF+n1qmmhRj7QYb51lndjK8YGTl94HXjHI4xkH2oAvR3dhBJDZx3MKvIm+JPMyXXOMgk88mkjt7KaW9aN0l8/CXCK4IyBjkDocYH4Cq9po32NbQRXb7rdXTc0akurPux7c8Zp+maNBpTu0Ls25Qi7skqoJIGc89famr9QHiGysb21ZQVkaM28EYGQFGXbA/Dk+wqjp/8AYyafayQeYY452SOSQtvEkjHcWx/eLdT69q0bm1Ml/Y3QkVTAzqVb+NXXBA98gH8DWcmiWy2s0Vrcp5FzdpKwyMAK24qpHU5XFLqBZudP0my0+aSa3CWsUJEgUufkA54ByePxqpHB4auppHRbctCwRnMjKNzruwCSASQ3b1wfSr91Z3N1JerJe+XazQLFGioD5bc7nJPUnIAHTiqDeFbMs+yTy1JJEaLhVDDDcA87uvPT6UAWbh9GhS5kZoWwpWZIpckDgN8oPGO561bt3sbW2hignhWEnZFiYMGPopJ5+lY114Wt3SZnvjEspIZvKTgNu4yf9/HPtVmbw3BcSebNO7SsT5hC7VYHHG0EAfdHNCuA+40zTLxZ7WGWKOZ23SbHDsMOGI2k8ZYDOMVJMul2lxBG8REoG5NiuxALdWI7bj1bvU9pYfZbu7uDKshuJPMI8oKVOAPvDk8KOtNvLBbq6glkn2LGQVUIu7IOflbqpPAOO1PoBD9m0SWC4INq0LDExWf5QCc4JB+UE9uAal8jTI7m1kLxLOE2W5M5yVPYDPzfr2rNtPDjNp0MV5JsmiOYxDjanzFuoxu698f1q7Bokdu0XkzFUQIGQxK27aSRgnJXknp+nWgCFxolppk9wyhbYTHc3mMS0mduVO7qSccEZqwkGmXF5C4gH2iONZ0JJ+QfdBODjODjPp3qJtMP2C5ifU02vcee0nkoAjBgxB5x1A69KklsFvLrzHvVeOW1aFo0RRvRsZYMDnr6cUgLUuoWcVm121zEYVB+dZAQSOw55PtU8brLEkifddQy5HYjIrG/4R6CK1kUXRjLKyvKyBhtKhTkMSOgHP8ASta3jENrDEH3iONUD4+9gYzVIRLRRRTAKKKKACiiigCRPufjUdz/AMek/wD1zb+RqRPufjUdz/x6T/8AXNv5GuafxMory/6w/QfyrANlLqNrqNnGkTJNfbZvMAIEeVLcHqcDGOOtb8v+sP0H8q595LhLTUUtGmW4mvvKjeHqpJXJyQQABnrxXNP+NT+f5G9P+FP5fmJBpWvRtEfOZfJCIoW7O1wvAJTGBkYyKvLZamoVi7MUZdyfaW/enDBmz/CDkHb2x2qhBrmrlog1vlUCLMrW0m4sOGw3TnGQQMVfXUr7Cs8YCBlErC3f90SGyuM5baQvzDg5/Gu/Q5yO0sNVS5gkuJJCAIklH2slSojYOcY6ltpz14zxU1jFfaXptrapZ+a4lSJma5ZwE/ikyeQABwvqR0qG01XUJ7mBZI1SOQRKwNtIGVnjYk5JwAGAGPfBNTWGqNBplsdSlkkuZJUtzi2ZCZG7Y74wSSOABQIdLp1x9ouHihtzNI7SR3jn548rgKBjPHTrjB9arXGn6rIoMLyxhVkMcYvW+Vtq7NzfxDcGPPTOOanlnuo7q4bddPcIzGG2RP3TxhflJOPX3znioLjVtRjUNCqSIqyNvNrJ+9CqpAC5ypJLLznOMijQZM2n3cbaqYUbddBnjcXbDaxRQAB0XBGcjtTxa3sf9mssMjvDIXnMl3uPKkHqPm5IPtzimNe3sLaqWlLGMNJbIbRyNuxSvIPzZORjr1pft8wOlyTTZE0p8wRW8ijaVPXrgBuOevB4oVhFay0zVLaHaDs2SGTy1uiVk/e7wBxhBtyD2OfbNTWunXsd3Z3M6l5EjeJ/9LfCjzNyn/b+Xjn0qvYalqK2wDq8io58xpLeQOiebgZJ++Sh3Agcbe+asWt9fS3lm8zskEsbq0YtH+aQSYHOcrleeeKNNBsfHa6gIrhDCUVroyhBdHdIhH3d4GVOef0pkNlqsd/FNJM8iq0Yf/SDsK7WD/JjBOSnPfGeKWLUZ/KudrOSt2VMrW8hEcZGQwXqRkY4PvSQ6pqDX8STQqkTNGjJ5D7gWViTu6YG1e3G7BoVtALWtWAv7VALSK4ljkUoJWACjcC3UHqBisrUILlJr6Cyku47aC0Msax+YNsuwosaEdV6NgZ5xWprnmraRyxXVzBtkUEW65LgsAc8E4A3GqV9qOp2D3FtG0crW9sbkTSRk7kVCMNgj5i47ds+lDBDZLDV54ImgupYUOSIzOzOCQMMWOCeQTtPr07VuXEc0tnJFFN5UzxlVl252nHXHFYk+raqkUbQWyTKSf3xhdAxwCF2k5HJI3c9K2L57lNMuJLSIPdCFmijJ6vjgfnT01EjP0eG+0u006wNlDs3ymZo3+SFASVx6k5A/M1k2/hrULVllt3EMpjdSPNyIg0gdkTABAbBJPYnFbWnagkaWdtcGUzXMjrGSrneFG5mO7lV7c98Y6isS2v9eiZJGeS5jMb+WjREHHmD5nG0cquQBnkDNToPoWzpmuLNHKtzK3lqvBuyAxBY4I6f3Rnvjmm22kaxDp0duk3kbAoaNbhnEmCc7TxsGCBgYzj81Otass0e63j8rapkC28hOPmyQwPcAHocZ78U201XV00uLELTMFQSSSQOrRcnO4McucAHIx1p6AaEFlfrqFq1xJNJFHbqruLnaDICTkoODxx71Y1ize9to40hEu19xBkC44I6MpDdehH61Xt9QvptQtYJBFCHt1lkXyHb58nKh+AOOeenvU+syzR2ieTNJExflkRjkYPGVVivbnB9O9PoBl21pqZnu4IZngaPaiSln8pR5IGEjIxjfkgg8YqZNP1VbRUkmll+Zsxi6MbDKgA7xk4BycH19sUyHVdT3zxLB58sICrA0RDn90Hyzg7c7jggAde1PTVNSa0VpIkiJZgJvs7uOFBAKA5GSSM57eppaAWfs14Lm9zATHNbLGrfaTkuAw9MrnI59s1DBYXdrY6TFFaYa1cGVTd5GAu08n72eo9KkF9KLy+QyOGW1R0/cPtRwG3DHQnoeOeQO1V7W6lgsNIaW6nlaWUGctDLuOVOc9cAHseD2xR1EPTT9XWW5Z7xpFZshRIVDrvzgf3Dt+XIx/Wr2i2s1lYmK4QK/nSOAJPM4Zywy2Bk4NUE1TVTNcrJZpGqNjOxmMY34DYH3xt+bjFXdDlmm0/dcPIz+fKNzoyErvO3huQMYx7U0MyrTR9Ss7ORIkhWSXAYLJs8sB2OAVxkkHqeeCKtW2n6glzbXM533H2RYJpBcthXBPzbej5z6VUstR1GCykkmFzIz4ERkjLn75BY8LtGNvH481bttQv5rm1mkDRxz2isLb7M3+tydy7v4e3UVKtYCzo1pf2kUi31w0zHbjL7uQOWGeRn0/QU66tXm1e1kZN9v9nmibjOxm2849wGH/66bo17fXkUpvbdYmXbjCkYJHzKQfQ988067lmOsWtssjRxyW8zAg43SDaAPwBJ/D2qnsBm2mk3tpZWdqx3mLUFlSQNnZEMkk+mRkY/2qt6pDdapZ6np7WKGAiNImeXb52cM/b5QOgPrmqVjd6klhZJctILldQWA7s/vkOd2c8nAyc/7NXdW1DfZalbWU80V5bCMGSOItsZyCMcHccckeh96XQOpnQ6FqtrJILe8cK87SNIZBvlYhf3jYwP4SNvIwabNo+tyJIvmO6u4OyS9YhQJAwIyDg4Axj1p0Opa9BJIssKzO07Z+VvLThcRocA45b5sHkVHcaxq7RuvlkfvF8torWUHiQdeTkbQcjjNCsBf/s/WDcQOLx0iVsiPzSxjG7OCT9/5eOQf61d022uYZrp7nzRvlYxBrkyKEJyAB2x/WqP9q6r9ogVbRHhZsCUxsvmjdjIHVPl55zn6Ve0y7uLqa7810KRSsiKIHQlQflbLHByPSmrAQahp8s+px3SwptjCkyFwxAGScLjIbngqR156Vn2tlrFxo9sy3E8TFEMqSzuZJGAO45OCnJHH+z+d/UJrpdXhSGacIAuY0QhepySdpVhjGRkEY461Ti1rVZdMguEgjlMio0kiwOqxFlJK7SctyF5B/ipaAXorTUUuonmkecIE+cXJQcLhgUAwxLc59+2KrSabezaTb20tplorvzdgvCMpuLfeHU4IHPfmrMWoXr3USTRrACELx+Q77gVyxDjAGDxgjt7iqUt7cS6JBsupopDeeXLILeUsU3nkD7wG3BznpxQwLtxa6g93e/Zh5CzWyxxzGbdtcZ529uDjj61RutK1afSpbZpGlMiSKsbXJTYTjBLc7h14Oev5XJr64gvL2O3V7mSO1VoYyjhS4zkbjwTjB9e1UrzU9UfR5mEZikKSeXJFDIzORjaAByp688jigEa+rW9xeaRLawJH5kyiNt5GFUkbj7kDOKw00bWoQsUMpSK3BSHy7oorIC5XKDgYygxzwtberzyxaNM9v5pndQsXlj5gzEAHocAZycjtWIms6xEFhMRdoAUmMls7M7KX5DLhTkKvQdWoe4uhpCz1QHeZCWRlZl+0HExDgnHZBtyMdOfbNQ29jq8c9tJI8h8vywy/bCVIDtvyCOTsKjn0/GpRqd9ncYcIrKZR5D5iBcAj/aO07sj06c1Db6vqDz23mRqI28sSKbWQNlnZSc54AAVuc9fxo0GTWkF9pOmtDBaG4l84Km66Zgys/LHP3QFOcd8VLPp8rX00qQW8jyMHiuZT80BC4GBjnB5645Oais9Te0015dUmkeVJhEQtqyncz7VUDncTkdO1SXEtwmoTZe63qwNvBEmY5VC5O44OMtkE5GOMUaWArTafqkiKFklRQWOxb1s58thkt1IL7Tjtj8KmhsbuK7up2jLtcQruIu2AVwm0gL06/xDGKhm1TUgi+UqHJbMjWsmDiNmK7c5BDALk9c+tTQ3t2bu6aWVliaBZYI/srnb8mT83fDZGDg0tAGJZ6hHYaeDHJLcxSpJMZLzdnAw3JHOeeMcVFHpmqI12Y38nfNLKu25JDbn3Ku3GFPXJ9+9SLqVydP065nkw8s6GRIraVdox8wPfAJ78GoYdR1KP7WqI8yxzTFRJBJlYw/yncfvAjIAHTA60+oiddPvjdW11LGWeKSYbPtjDajsGXn+LAGMH+VSfZtQ33wWPy45Z1kT/SeXUABlzjKZxx6dOKiXUL2S6tpDIyWxkmjYCzc78MNh9VyueenWpBqE6y6gqF3KTqFZoX2pGQAzAfxbTn7vXrRoBGLHVVu4pvNkYJ5fy/ajtA8xiwIx8x2EDJ64/Grmt2H9oabJEtvHPMOYlkIAB6Z5z2JqmNU1D7XEGiUQny9w+zybmDSMu4H+EbQrYOSM81b10zJpcksNxcQvH8w8hdzP2A6E989O1PoPqZ95byx35trM3EFotsZnji8wCNlB2ohHHzFskD+571HFY6xcaZbtHcTREqpeN52Lu23BbccFecHb7fhVm5vNQs7hLOKVZw0BnSeWPJ2IDv34I5JKY4HU+lQDWdVawhnjt0l3hWeXyHVUJXO3aTk84G73pKwG60Lvp7QSESSNCUYk43Erg9Bxn6Vn2S6hYRWtuLbejz7Tul3fZ4QnUsANx3DAGP4h6VpJuntVEgMTyINwRiCpI5wfb1rOsroWUUKXT3Be8vHjtkk3O4XnGc8gbULEnpmm9xdDJ/4Ry+S8a6t2EMrPcMv70FYfMbJZcDOXAAPpjI61LJpWuMUZLqYFUOP9LKgHLEAgZzjK8nPAxUP23XYbxnR5LiAPceTEYiC3zcB/lHyqBlefmz7VNJrWsKUK28RGwsR9mkbdy2DkHjIUDHPWloNjoNK1iOzeOOX7MxZ2KrcFw4Mm4KvA2YXIyPX8atxWOoi4sfPknkjjjYSst0V+YsCuQPvYAI6855zVK21TVUsW8uF7gqz8ywurRL5uFJLffyhzgY6Vci1G/kuLGJxFCZo2aTMEjDIYBcHgLuGTz0x3oAvapbNd2giSLzT5isV8wJjHORkEH6EYNY1nZ6jHfS20bvF5McIV1Zlhj+U7gseNrZ46dD6VsatJJHZZileJjIoLKjNx3B2glR74OKy7XVNS8825jMrxxxERSRHzH3KSdzjCjGM9B16UMB8Gn6vHa7JriSf5wWjFyULDaQSH5Iy2Dj27Vat7e8h1BXeEvF9kETObksS45zyM+2ePWqsGq6m9pvuIEg/eBWlEDv5fykkFByfmAGQe9Wbe9lOqCOZ2G6zDECCQKr5yevGcc469qNAKttpt5aaNY28Vp+/hmSRwbskHBBJyeuRnj8akaw1f7ZdSC9by5NxRd+BgkYX/AGSACNwHfPNVrS7uIdFsp57ueWaS5QzZglDYyNwxyVABz6dsVZbU9UF5dRiyXyo92z5WJ2gjDcfeyMnHB4xQBb0a0ns4blZ02GS5eVB5xlIUgYyxGc8f/XrOh0rULY3UkMcCvO0gChwmxWk3ZyoBYkHueCOuDV/RJp5oLozyPIRdOEZ42jynGMBuQOtZ1vf38Bu5H+1SR7pFh3IXyRJgHGF2gKR3ORznijTQCe107UEm0+5uv308UTxSkXTAYLgq2OjfKOhqxpFnqNrJMb66acMABlsgtk5YDtn06e1V7XUL+efT5p90EUsbrLD9lc5kDgDnquRzzx161Y0i+1C7kmW9tVh2gEAAgq2TlfQ8Y5/ShWAmvYJJNT02YIXhiaUOP7pZMK34cj/gVZMWjXlpp8top3g30UluwbPlqHBLH04GP0rWvZpU1PTYVdkhleTeR/EwTKr+PJ/4DWPb3WqW+myx3DyG5gvoo42bP74M4BXJ6jBPT6+wOodDT1NLq/i1KwWyRrd7XajyPgSu2QVxjgAYyfesiPw/qdq84tbqRVkcM0hl/eSEIFUkjHCYwByD1rX1S/XyNSs7aWZbyC183fFGWKbshMcEFsqTisiPUdetpLgSotw5defLby0GwbQvAPzHJJ5weKNAC70bW5kulWZnWbcoWS8O0AlsHGCBj5DwOMcVbm07WZGRoruSGMOxEXnl2jzjBLn73RuDn71Ur7V9Y8q7RYiANwiMNtKHDAtt57/dA6c7u1XZtW1VWTybVJoy7BZjE6CTpgbTyvVuTn7tCsBfsra5TUbya480RvITCDcl1CkDjZ25BP41HqWnSXmoW8qQxMI9uZJGBAw4Y5Qg+nBUg5qSyu7i41G8id08mGQqi+Q6sRgHO48HkkcelQ6nLcrqFrHBLOFO3McSH5suOd20qeM5U445zT6AZ1la6vc6UjJPcRMT84nmcvLhz64KcYHv+tX4rLUlltvOmkmCKmXFyU2kEltygYfIwOfT8aqQa1qk2nidII5mJG9kgdRENxHc/PwM8HircWo37y2yyxLCXVCVEDyCTJO7DDATAAPPrS0Ahm0+9uNIuLSS05e7EqL9sPKbwxBbr26e/tVqS2vftzNbR+TG1mYVlabd5b8FTt74/WqU99cSaLd+VczJMLvYJRbyZ8suOVHUDbnkHjHarTXc0F/5MJkuCLImNCjgPIMEAscjJHryKAKjaXqzabJBJM828Ovlm5KYJUAHfySM5OD6/hW7aI8VlbxyAB0iVWAOQCAAawX1PVZNLlLxCFyrhZUhkY7towu0HIJJIzk9PWt6zZnsbZ33b2iQtuGDnAzn3poRNRRRVAFFFFABRRRQBIn3PxqO5/49J/8Arm38jUifc/Go7n/j0n/65t/I1zT+JlFeX/WH6D+VYa3smn2mq3MKxtKLsqiyZ2szFQBxz3rcl/1h+g/lWLDLbW8Wo3V1btOkF4ZFVIw7BuACo9ee3PNc0/41P5/kb0/4U/l+ZLF4ohfyN1rcbZERjIpXapPBGCd3B46VbTWUd4VFvKGm2mPJXlWDFW68Z2kY6iq0cnhszIirYJNPtkCFFDMW5BIx1Oash9GaOSMfZCkknzqFGGcAn8TwT+Br0DnGwa4lxNFGlpcBZVQq5K7cuhdV65zgHnpU2n3xvdOgu7mFbYyBSFeRW69MEeucetQQvoIuI44PsQmCrKiooyAFJUj/AICTj2zii0h0fVLSG4tYYmgEy3K7U2DzB0JHcjOee9Ah76lLG1xM6wrZ28jRvuY+YSq5yB0P09OaZProtnKSWVx5ibjKqsh8sKFYknPPDA8Z9KkaaxfUHZrQtImY3ujCCi4GSC3bj8O1QvJ4fWGFZDYiNw6RqygA5xuGMfTP4Uhkw1SUf2iz2biOzZsMsikyBVDcDPBwe9PF/Mz2ObWSJLmQofMKkgbCw6Hvj3xg1AZtFmlvg6W5eBTHdF4uigDIJxyMYoH9jTNYxxw20wLsluVVSEIBJx/9b19KEIjs9eaWGH7RbOskkhRWQja2JfLJAySMEr19amt9XkubyGJLNxFJC0jSNIvyEPsIIzzz6VDYz6FPBIIYbSLzW8uWIooO7eVwccH5v196kin0Rrq1toltjJtM1uFi4A3YJU44O7+VGoyVNSkxIHgYzfamt4olIBJC7hyTjpk54+lMg1yKe6jhS3n8tyoEx27dzBiBjOf4GHT09abGNGFrdOLe2jgMxjlJRcO49h1PP1p6voz36RKbM3Z2OigDdwDsI+gzj2zTVwJNUuby0hSS0igfLhXMzEYywUYx15P6VnXWo2lq10t3pStdGLdcqgQ+ZGELFsnG5AQV571f1e4tIYI1vbOS6jdxhEiDgHIwTngckYqlctpE32q5ur94vPg+yuJCFMKld5Xp8pxyc5/SkwJJfEFvZBIXtJ0kC58lACVQAHPBweGHArRvLk22nzXKAMUj3KG4z6VSjfREsYUD2hg3/ISq4LY5PTr056VpSY8p8x+Yu05QAHcPSmJFewuvtMKPPsSYyPEARtJIPoTkHAzjJrEg8VT+YPtenmKMK2XUk+Y+8KgTI5BDAk9jxVx59MRdNvPsjYiiaWHaoAgR8KzEZ9wOMnk+9T3qaVpVsktxaRJCGEAKwghA7c/Rc8n86WoyD/hJYjNGn2O6Cvj5yFAUkkHIJzxtP14xUcHiVfsNvLdW0izzKpRUwRJkkEjk4GQevNPtp/D99Esiw2wEhaNBJEAWCkrx7dceueKat/4citwtt9ia3ldEk8lV2jIO0kenHGPX3oAuw6t9ongihtLhlmhE28gKEUnacgnOQe2Kl1G5uLWBXtoRLIWwQcnAwSTgcnp2+vaoozpLXkMkf2U3JjAiZQN23BwAfTrx9aesmmazHhTb3iRnPQMFPI/PqPzpgZ8Ot2cKXFy1n5aDaZriIKVkcxhxj+I/KeCQKsRa6k0YMVpcNMS37r5VOFUMSCSARgjv1qEPpVjq927q3nEb5pXRdkK7B36hdoHqO2anMmhPZoD9ia3L/KCoK7sdcfTv6UATrfSma6UQErFAk0a5GX3Bu+cfw4/xqC01C+e002W5gVHvJFDBcYQMpYD7x9Ov6U5/7GW9mRltPtTxkSDaNzKBkg+ox2qpPNosNnprNYxMs7D7LEI1+UkbiR2HHoaBE8XiG2mkeOOGdirbI/lwJG37MAngc+vbmrWl3ct9aGWZBHIJpIyo7bXK+p549ajL6az35kjgAjIS4dlGGJAOD3/PqemaY1/pel2sQgMKRyuNkcIA3FmC5xx3PPfr3oQytZ+IHkt5pbmJFaPAATcu4lmAUb+v3c5HH5VYg1n7RcJ5ds32R7Vbnzy4BAOeCuc9u1OvINOsbaNX09Hj8wRJHFCpwWbPQ4AGaWA6S/2Jo4rdTsJtgYwpUc5C8cdDwKWoDtM1aHVY2eKOVAoVh5i4yGGQQf6dqdc3bpqMNrFGjM0Es5LZ424AAx6lvyqGy1HSPsX2u1khjgkflgm3LfTH1q8YYZpoLnAZ4wfLdT2YDP1BwPyFPdCMey12S5sLS5lgUSG7W2mUrgqWyAVGSRyV69iav6nePZafcz2scU1zEFYQs+3cSQBnHIz2PtVOe90qAQyNblY1vmQMqhU80AgseRnGSPqD6VZjttNuL3UYzbo80hia48wbhINv7sj2GDj3BoGZ8HilSzi4sp4iZG8tAh3iIBfmYHo2W+6O1LP4n2pII7OZZEdR+8KkOPMCkDDcHnIJ4qXVL/RdKmYXtuA7I11lYN28ghTj1fpx3A9qdKPDsaP58enqsbDcHjXCsT9OuT+ZpAObxFbpdLbPBcCXdtlULu8s7tvOOvPp2q7aXrXcs6m1miWGRoi7lcMynBxg5qjFf6I9xb28f2XCoHt3AXacsRhfQ5H6ip4rjSLNLiaGS1hUNmd1woyTjLH655poBt9fz295HAII2tn2q7uScliRt46Hpjdwc9RVWLxBaW9jalLGWBZkVreBQvKMCQflOF+6eK01gsrwreRxxPIy4ScKNwHI4OO3NZNhcaDBpi24VUtsqivcRqPOYA46dWAHTAI9KANGHVluZI1ht5mjYoGc7V2Fl3AEE56Y6DvVeXVL3+y4Z4LYPcy3PkbSQAnzlc/e56etWM6VJdpxaNcCP5DgZCY7H6Z/CqmdANqI4obOWHz0Xy0RcCRuhweM4Oc9+2aBFmfUzZvdG6QiG2tkmLKuS2SQcc+oxj9aq3PiLGnXE1tbSmeKN5GSQABQuOTkjI5HQ1N52mf27NAttGbtYd082wcKQcAnqeB0GaM6LNp9qZobVbeYboo5UUjnr6j+lAy1qV41hpdxdqFLRpuUNnBPQDjnknFZcXilfKgMtnMWZf3hiI2owLBh8xDcbGPTpj1q+91p9zfJpbLHM6qZNhAIQoV7eoJGPpUF0mj2uoQwzafGZr1j+8FuGBb/AGz2zuxk9296BEw1mNniUQy4mYCJsj5lLhN3Xj5iODzzUcOupNNAgtLgLKE/eZXClmZQDznqp6e1JFeaDcR3BhlsZEYBptoBzzxn15p6LogZAkdmGJTYFQdSSUx+O4j3zSAk0+/a8sRc3UC2wDHG+VWHDFc5zxyKSW/mSe4O2BbW2YLKZGIc/LuJUYx0PA781SuLjw5NZtKzW0lvJcxFzEuQ8m7cm7HUZGeeOK0rn7IkTalLAsphiMgcRhn2gZ498Zo1sMqza6IcA2Vw0h5KKyZC7C4JOcdFP41LBqcs11dRraN5UKq6SeYv7zcm4DGcg1SttQ8PXcbrHHAIoip3PCAu6RMkD328N6dDTri/0CETt/oks1vCyPGgXeEUcrz2Azx6UagWk1SWW2srhbSWKO5ljX97tJ2sMg4Dfh7elV014oJhc27/ALueWJXTGH2Pg8ZyCAQeevOO1P8A+JA1rAuLE26uREu0bVbjOB27c/SpYV0c3lxbQx2n2lgyzxqg3HPJ3cd8g+9MQ0atI97DBHZuVczLI5kUbDGwU9+RzmpP7SZHulkhbdHOkMSLjLllBXnOOc98YqOdNNtWjtxpwlKHzgkNuG8vJxux6kjtycUoGiobshbMEnFwQo+bnv68/rSAauuI1ykItp8MVV5MrtRi7Jg85PzKRxn1qfVbq6s7Jri1jhcpy/msQAuO2OpziqtrPot6lvIsVsrSfLErKucI5C4xxjcCRVjWLmytrHOoQ+dbyOEKlVK56jO4gY4p9BlKe+htrlkvNMRr94gr+WFPmphi2CeSo2cg+opR4gtbW1t1FlNF5iK0MChfuFSwPynA4B4pzHTbi8WSW5lhuHtzbpbyYVolcZOFxkMQnqfu02xk0H+xYfKaBrM4AMkYBZgOpGBzjvikrga3nF7Tz4ULlo96IflLcZAOelU9M1A3kUhuVEcsUvlZaMx7mKg4UMc98e+Klk1LToneCS7gVkTcyFuiYzn6YB/Kq+7RpYbO4V7fyLaYtbspwiSYK544zhjyfXNMRnnxRPDdvHc2BSCN5g8ykkMFbbHt45JOd393HvVl/E8MbLmyuypBLMNo24Jz1IJ4UnIq3FBYveTW/wBgjR7f5wTGuD5mckfXBznFV706TZOyzaaJDHB5hKQK2E3Yx+bdPc0hkUfiNY7QSXkEiuXdIymNspWTy+BklecdauRax572yw2dy/noz5+Vdm1grZyR6jp1qOM6FB9phiWxj2DbOiKowM4wcdef196cZNFSe1cm0ExX/RjgbtucfL7ZP+NMC1fTzW9vvt4hLIXChfY9Tjqceg5rLtdZtt8k72YRtkZuLmEAqdwO0c4c9CORxWlqbW0dhJLeWxuIYvnZBGHIx3APpWVdzaZpd6l2Ywb24aGKOBwuYgflGAOnGeATRZt2QItW+vxXcamC2uGldgI4yAu4FS2QScdAe9WoL2Se8EXksqNbCdQSCSScYyCRx0/rT206yMTQm0gMTNuKGMYJ9cUf2fZCfzxaQiXbs3hBnbjGM+mO1GoijaanfS6ba3dxbrGbmdF2rg7EY4H8XJ9/0pT4gtRczw+VOfKZk3BeGYEAj25OATVo6TpxgWA2Nv5KNuWPyxtU+oHrUxtLYu7m3iLOu1yUGWHoaLMCDTbya8juTPF5TxXLw7MgkAY64JGeapW+uyGS7W5jjUQFgCu5c4kKAZbgk+3Tp6Vq29rb2iFLaCOFSdxEahQT68d6j/s6y2bPskG3JbHljGTyT+JA/KizGVLbWTeXFqsFqzQXELSmVnVSmHCkEZ55PapNM1iDVS4hjmUKodS64DKSQD9eOnWrA0+yVYlFpABExaMCMYQk5JHpzzUkdvDCztFEiM5yxVQNx96EmIguboxX9laqisZzIxLfwqi5OPfJA/Osu216abTXuJ7dVmt7pIpk2kYVnC5UE5zz39D+G1JbxTSwyuuXhYtGwOCCQQfwwaY9jav1hQZlWZtoxudeQTjrzzzRZ3GM1C6NnZXc0Aie5ihaRY3bG7A4zjnHbNZEXiohpRdWE8OGxHGAd5AUFyQfc4AHUc962G06zkuLieS3SSS4jWKUuNwZFyQuD2yxOPelOn2bFibWElyCxKDkgYGfoOKLMDIu/FAiiufLtJleEMVaTbtbbnsGyMhW64x3qzP4it7afyJbe4WZSfMjADFANvPBIP3hwKtvpOnSZ32Fq247juiU5PJz09z+dSCwsxGkYtYQkbbkXyxhT6iizENt71ri7uIPs0qrA5jMrFdpOARjnPRh2qDUL+4tLiFI4ozC23zJHJwuWC445HXgkYzxxVuK0toJZJYYI45JTmR1UAuff1oe1t5bhJ5IInmj+5IyAsv0NOzsMyINds7awhZLCS2jkP7iJVQbssVP3ThefWrkWsJcGPybadlYIZC21PK3EgZDEE9D0zT7PSLKys2tkiWRG++0iqWfnPJAGanNjaF4nNrCXiGI2MYyg9vSlqBRn1O7TS7i4itg1xHc+QsZIAHzhQTzz1HQjPtUz38lrKy3SbY47RriRlXqVI3YAJ6Z6frTxpOnCF4RY2/luwdk8sYZh0JHrUgsLQXC3AtofOVdqybBuAxjGfTFFmIzW8QrJZSyW9rMbhY3cRuABhVDbskgEcjgGtW3kM1rDMwAMkauQPcA1G2nWLQ+S1nA0W7dsMYIz64qdESKNY41CooCqqjAAHYU0A6iiimAUUUUAFFFFAEifc/Go7n/AI9J/wDrm38jUifc/Go7n/j0n/65t/I1zT+JlDXgy2d3UA9Pasa00qLUTfQzu3lpf+YVAGH24IBz2zj8qKK5qn8an8/yN6f8Kfy/Mmj8I2ka7BdXWwfdTK4UZztHy9KtDQIQFxc3AePYInyMxqudqjjGOT1yfeiiuy5zjbfw7DbPCyXdw3lNGyhtvOxSozx6Mc07+xZI7e3t4dSuUjimRznBJjXP7sEYwDxk8kj60UUXAfLosMzShp5hDMSZIVYBGYjBPr79cZ5xUU3h2KfeZLy5Mkiuskny5feoU54xwFAGP1ooouBNJoyuLpReXCpcqVdVK4yVClhkdcD6e1DaOWWzBv7kfZWDKQE+YgEDd8voSOMUUUXYEEXhuCKMJ9rum2kmIsVzES4kJHH94DrnpipYNDS3MJjvLnMQZSSV+dWbeQ3Hr6YNFFFwEXQwpdvt1yZGm89XOzMb425Hy46cYOabH4eghlR47idQjo4TKkZQMPTPO8k8/lRRRcC1eacL238kzyRDcrbowM/KQR1B7gVz2pwC41HWnJw0FgYlxnBLAguRnrg4+maKKd2wL48OWmoW0NxcySyTPGC0jbSSpUDaRjGMAds1rta5iMaSFPl2hlAyv0zxRRQ2wRQXw9ALa3t3uJ3WFPLy23LpkHa2B0yB0weKV9ENys63d7LMsl156oVG1FAAEeDn5eDn1JNFFK4EC+FLFJVkDybg2eVTH3iyjGOApJximr4Tt0eF0vrxXhZWVgyZyAAM/L7Ciii7Ami8M2UN1HcoX8xCGJYA7mGcMcjrz2xVux0wWFqLdbmWVQSQZAuRk5PQDuTRRRdgZ+taQj2d5dtPKzLDJsRsbUJQrxxuxgnjOMnNNj8O2uo2dvPcySyTPGCZG2k7SoG3GMYwB2zRRTvoBej0cQyM0N3cRKxZhGpXarEYLAEfoePamR6EIobOJL+6AtCShwmW4xhvl9CfTrRRSuwEm8Pw3LXRnuZ5FuGRihC4Qr90rx2981GfDNt5Hkx3E8KkqZPL2jzCr7xnj+8T0xnNFFF2Bcv9Ki1GFYZ2PliRZCu0ENtOcEEEEVTHhq3W8trkXNzutmUxIWBUbcgDp0wcfl3ooouAsnhu2lt0gkuJ3WJy8O4qfKznIHHI5PXNaEFmlvBHDGdscahVGOgFFFCbQGc/hy3nsprKaRmia4aZCqgNHuO4gE57k/n7VONLZJr64juSs08axIxXPlhVOD15OWJoop3Agl8Nw3EMSXN1PM8dsIPMcKzE/wDPTJBw/Xn3pg8KWSsxSWVCzbsqFyPmDHnGcEjJoopXAIvCttFIzC8uyrosbxll2soOcH5c9R60o8LWSQTwxSyxLMoUlAuVUNu2gkdM+uaKKLsDVFu2wBpWdsYLkDJ9+K5+/wBJFlc6e0V3OZpbpA0x27+EYDoME/OeSCTRRTu7gXF8Lacs3mBW5XDKcHcdu3OcZBx6EVNJoZktvIbUbzG5G3EoT8mCo+7jGQO3NFFK7AmXTGS8muVvZw0qKhXC7V25wRx15J9OapN4WtHggilmml8hWRXcISUJyVPy4xkemaKKLsCzFokUN5HcJcz4jEgSEkbF3kFu2eoz1qK/8OW2pXP2iaWUSBFSMrt/dYYNuXI4OQPyFFFF2BHF4Xt4XR1u7nzI+UYlcg/Lg9Ovyj2qX/hHoA8bpc3CFGV2wR87K5cFsj1Y9MdaKKLgJF4dhhiEa3dzwkSq2VyvlklSOOuSc54p8mjyTyziXULgwyWotxHxwTndJ6FiCB04x70UUXArP4TsJHZizgnGAqoADghjjbjLZJOe/pSTeEbSdJEN1dKsmRhCowCCMD5eOCRRRRdgST+F7O5bzLiWaWY5DSvtJZSANpGMYwo7dqu2+mC3ubicXMreewZkYLtUgBRjAz0AHJNFFF2AT6Ys0wmW4mhk2hWaIgFlByAcg988jB5NV/8AhH4AUKzzAxHMHI/cc7vl49fXNFFF2BDD4Vsopo5jJNJIjbi0m0lzuLjPHYsemKe2gm5sRBd300zfaGuN5RSOScJtYEbQDjHtRRRcB8egpHeQXZvbqSaFdqs5UnbzlTx0Y4J+gxjFRyeGraa2igluJ5FhJ8kvtJjBGCo4wRj1yfeiii4En9gx/wCkBbqdY57cW5jUJtVQCBj5c5GT+dQXXha2vI3jlu7ra7FjhlHVVX0x0Ufr60UUXAvRaX5V/Ld/apnaVVUxsF2gDOMYGe5796S70eG9dXkkkXC7HCYAdMg7T7ZA6YNFFF2BWl8NWs5mM0s0jSndk7cqd24Hgc4PTdnjipINCitpbeSG4lQQI6BAqBWDEFs8dSQOmKKKLsCxe6cL6zltmmkjWVdrNGBnHfqCKztV0ON7bzmurjKSwysoKgSOjKAzcenYYFFFXBvnXqhx3RsmD5j83f0o8j/a/Siip5mIPI/2v0o8j/a/SiijmYB5H+1+lHkf7X6UUUczAPI/2v0o8j/a/SiijmYB5H+1+lHkf7X6UUUczAPI/wBr9KPI/wBr9KKKOZgHkf7X6UeR/tfpRRRzMA8j/a/SjyP9r9KKKOZgHkf7X6UeR/tfpRRRzMA8j/a/SjyP9r9KKKOZgHkf7X6UeR/tfpRRRzMA8j/a/SjyP9r9KKKOZgHkf7X6UeR/tfpRRRzMA8j/AGv0o8j/AGv0ooo5mBJHBlPvd/SmXVvizn+b/lm3b2NFFZS3Gf/Z\"></span> </p></span><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-09T14:27:20.68Z", "changed_date": "2025-09-15T09:15:43.677Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:15.983938"}, "741395": {"id": 741395, "title": "People Operations - Delete salary approval", "description": "<div>People operations should be able to approve and delete salaries and these should auto approve. </div><div><br> </div><div>Currently we can add but when we delete this goes to approval to the Reward team </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T13:35:08.03Z", "changed_date": "2025-09-09T13:35:08.03Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.066872"}, "741309": {"id": 741309, "title": "INTFIN019 Fuel Invoices are not auto approving", "description": "<div>Fuel invoices interfaced during the controlled start up are not auto approving in Fusion. <PERSON><PERSON> has advised that this is because the invoice source was VA_Synthesis and not VA_SYNTHESIS. Vinit to advise on how to fix these invoices. </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/46184d54-c99b-48d6-9c4d-0de258ff88ad?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T11:04:43.99Z", "changed_date": "2025-09-09T13:49:30.537Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.224996"}, "741308": {"id": 741308, "title": "INC0894068 - Task Control Slowness", "description": "", "work_item_type": "User Story", "state": "New", "area_path": "PIR Action Tracker", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-09T10:55:17.497Z", "changed_date": "2025-09-09T10:56:19.77Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.312151"}, "741019": {"id": 741019, "title": "Benefit Band is missing for all colleagues", "description": "<div>Attribute21 (Benefit Band) isn't being populated in the HCM Extract. And AIRORTBASE attribute which is mapped form Assignment location attribute 1 is missing.<br> </div><div><br> </div><div> </div><div>iTravel won't work without this value and Carmen Feeds also needs it. </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T10:26:58.247Z", "changed_date": "2025-09-09T11:10:52.85Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.400816"}, "741011": {"id": 741011, "title": "In correct F grade leave entitlement and adjustments", "description": "<div>Incorrect leave and adjustments showing for F grade users. </div><div>Example 1 - user&nbsp;485290<u>&nbsp;</u>- Entitlement should be 255hrs plus 37.5hrs HP total 292.5hrs but showing 290hrs plus 212.5hrs adjustments<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/61cd60c8-4c0a-4da1-afb5-30db0f916996?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>user&nbsp;402470 - Should have 255hrs entitlement only but showing 255hrs entitlement plus 180hrs adjustments<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e77904ff-543b-4b70-b093-1600b2b3dee2?fileName=image.png\" alt=Image> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T10:00:18.853Z", "changed_date": "2025-09-15T08:58:11.253Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.519010"}, "741005": {"id": 741005, "title": "Incorrect yearly entitlement", "description": "<div>User 574774 - entitlement should be 240hrs but Fusion calculated 234hrs. </div><div>Calculations based on DOJ 13Jan25 C grade. </div><div>247.5/365 x 353 </div><div>full quota divided by number of days in the year multiplied by the days employed for this year. </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/116df276-f35b-4b1f-92d3-38d5fe2a4151?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T09:50:36.137Z", "changed_date": "2025-09-11T14:36:15.587Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.639154"}, "740961": {"id": 740961, "title": "INTFIN043 Zero value invoices migrating to Fusion", "description": "<div>Zero value invoices entered into eMRO are trying to interface to Fusion. Zero value invoices must not interface to Fusion as per the EBS process as these invoices do not require payment. Currently these invoices are failing: </div><div><br> </div><div>Example invoices:&nbsp; </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7dd892d0-4886-4c22-8a9e-4530798893dd?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T09:13:45.683Z", "changed_date": "2025-09-18T12:29:46.823Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.765345"}, "740879": {"id": 740879, "title": "Future Resource exceptions added by DM need to be written with same key as used by the integrations", "description": "<div>The future resource exceptions written by DM can't be deleted by the integrations. </div><div>Please could these be rolled back and written with the same key as the integrations? </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-09T08:32:26.067Z", "changed_date": "2025-09-10T06:01:40.09Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.873919"}, "740853": {"id": 740853, "title": "Seniority date not reflecting correctly in holiday entitlement", "description": "<div>example - user 037771. who is a rejoiner. </div><div>Has a seniority date of 07Oct 2000 however holiday entitlement is only reflecting 247.5hrs when it should be 255hrs. </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/912ed97f-1c61-4095-b35f-6b50845d9de2?fileName=image.png\" alt=Image><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/9d8b3b4c-b14f-4855-aee4-f3cb71394f66?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-09T07:35:04.787Z", "changed_date": "2025-09-23T09:59:54.457Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:16.972585"}, "740844": {"id": 740844, "title": "Prod: Error when creating cases in Helpdesk", "description": "", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-09T07:15:52.317Z", "changed_date": "2025-09-09T16:48:04.77Z", "priority": 2, "tags": "", "repro_steps": "<div>ER Specialist (<PERSON>) is creating a HR case via Helpdesk, and is getting the following error: </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/44d49a4b-f056-4944-bc73-702e5dda8f45?fileName=image.png\" alt=Image style=\"width:694px;height:297px;\" width=694 height=297><br> </div><div><br> </div><div><br> </div><div>She is also not able to assign the case to a Case Leader - as not all the Case Leaders are showing in the picklist to choose. </div>", "system_info": "", "updated_at": "2025-09-30T11:53:17.076369"}, "740843": {"id": 740843, "title": "Schedule Exception update from OFF PERIOD to WORK PERIOD", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-09T07:15:35.19Z", "changed_date": "2025-09-09T12:36:27.903Z", "priority": 2, "tags": "", "repro_steps": "<div>Schedule Exception update from OFF PERIOD to WORK PERIOD. The reference sheet is provided in the attachments.<br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:17.261675"}, "740815": {"id": 740815, "title": "Leave spelt incorrectly in current timecards", "description": "<div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e95095c9-5f64-4ef6-ae18-321868c0f976?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "Su<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-09T05:54:36.64Z", "changed_date": "2025-09-18T09:40:38.77Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:17.366993"}, "740814": {"id": 740814, "title": "INTHCM062- Showing Invalid Hours", "description": "<div><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span style=\"color:black;\">The data we are receiving from AIMS is taking the duration over\n24hrs which is causing an issue with the work schedules as they need to be\nloaded as single days.</span> </p><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/866948d7-1372-402f-9440-44d1113a2267?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-09T04:34:52.693Z", "changed_date": "2025-09-15T09:20:40.1Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:17.471316"}, "740806": {"id": 740806, "title": "INTFIN004 - Update HSN Lookup", "description": "<div>Invoices from Tradeshift are failing to import as the HSN lookup for India invoices is restricted to a subset of HSN numbers provided by Anil. This lookup needs to be extended to all HSN numbers to avoid import errors: </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/eb5bc6a4-882b-4c08-a6e4-18341aef595e?fileName=image.png\" alt=Image style=\"width:489px;height:422px;\" width=489 height=422><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/*************-4973-9bee-b7a69d7b345a?fileName=image.png\" alt=Image style=\"width:494px;height:403px;\" width=494 height=403><br> </div><div><br> </div><div>Here is the link Anil provided to obtain a full list of HSN numbers:&nbsp;&nbsp;<a href=\"https://protect.checkpoint.com/v2/r02/___https:/services.gst.gov.in/xjwAnhjxdxjfwhmmxsxfh___.YzJlOnZpcmdpbmF0bGFudGljYWlybGluZXM6YzpvOjQ1YjViYTAyYTk4ZGJmMWJmOGQ3NTVjNjVlMTEzNGQ5Ojc6YzEyNDphMjE0NjM1NWUzOGEzNTRhMzE2OGY5ZjI0Mjg5Zjg2YTFlZmE5MzIzNjIxYTdjOGM2ZTc3ZWZkMDE0ZWQwYzk0OnQ6VDpU\">https://protect.checkpoint.com/v2/r02/___https:/services.gst.gov.in/xjwAnhjxdxjfwhmmxsxfh___.YzJlOnZpcmdpbmF0bGFudGljYWlybGluZXM6YzpvOjQ1YjViYTAyYTk4ZGJmMWJmOGQ3NTVjNjVlMTEzNGQ5Ojc6YzEyNDphMjE0NjM1NWUzOGEzNTRhMzE2OGY5ZjI0Mjg5Zjg2YTFlZmE5MzIzNjIxYTdjOGM2ZTc3ZWZkMDE0ZWQwYzk0OnQ6VDpU</a> </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T19:49:39.52Z", "changed_date": "2025-09-18T12:46:21.113Z", "priority": 2, "tags": "HyperCare", "repro_steps": "<div>In analysis. </div>", "system_info": "<div><b>Action Plan:</b><br> </div><div>1. Confirm options from Mel and Anil - <b>Complete - handful to setup</b> </div><div>2. Update handful for immediate need - <b>Complete</b> </div>", "updated_at": "2025-09-30T11:53:17.628363"}, "740800": {"id": 740800, "title": "Pensions Auto Enrolment/Re-enrolment discrepancies", "description": "<div><table width=860 style=\"border-collapse:collapse;width:648pt;\">\n <tbody><tr height=19>\n\n  <td colspan=4 height=19 width=860 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:648pt;box-sizing:border-box;padding-bottom:0cm;padding-top:.75pt;\">Colleagues I had\n  for manual Triennial enrolment, missing from Fusion lists. </td></tr></tbody></table><table width=\"\" style=\"border-collapse:collapse;\"><tr height=19><td height=19 width=215 style=\"padding:0.75pt 1px 0cm;color:black;font-size:11pt;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:438px;\">18968 </td></tr><tr height=19><td height=19 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);padding-bottom:0cm;padding-top:.75pt;\">27090 </td></tr><tr height=19><td height=19 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);padding-bottom:0cm;padding-top:.75pt;\">18983 </td></tr><tr height=19><td height=19 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);padding-bottom:0cm;padding-top:.75pt;\">27514 </td></tr></table><table width=\"\" style=\"border-collapse:collapse;\"><tr height=19><td height=19 width=215 style=\"padding:0.75pt 1px 0cm;color:black;font-size:11pt;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:438px;\">On manual enrolment list. Not on either\n  PAE joiners, or Not Enrolled list&nbsp;- <font style=\"color:rgb(200, 38, 19);\">Avinash to\n  investigate</font> </td></tr></table><br> </div><div><table width=645 style=\"border-collapse:collapse;width:486pt;\">\n <tbody><tr height=19>\n\n  <td colspan=3 height=19 width=645 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:486pt;padding-bottom:0cm;padding-top:.75pt;\">Auto enrollees with query on enrolment </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">578606 </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Hired June, but didn't turn 22 until\n  July. Therefore should enrol October, not September. Is this a system error?\n  - <font style=\"color:rgb(200, 38, 19);\">Avinash to investigate</font> </td></tr></tbody></table><br> </div><div><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Postponement letters </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">There are 70 colleagues the\n  postponement letters file shows as getting postponement comms, which I can't\n  explain. I think there might be a DM issue with who the system thinks has\n  previously had postponement comms </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n\n <colgroup><col width=215 style=\"width:162pt;\">\n </colgroup><tbody><tr height=19>\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;color:rgb(200, 38, 19);font-family:&quot;Century Gothic&quot;, sans-serif;text-align:left;vertical-align:middle;padding-left:12px;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Josh to check for EBS errors </td>\n </tr>\n <tr height=19>\n  <td height=19 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;color:rgb(200, 38, 19);font-family:&quot;Century Gothic&quot;, sans-serif;text-align:left;vertical-align:middle;padding-left:12px;border:1px solid rgb(212, 212, 212);\">Avinash to look into the New\n  Business errors </td>\n </tr></tbody></table><br> </div><div><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;\">Not\n  enrolled letters </td></tr></tbody></table><br> </div><div><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:left;vertical-align:middle;padding-left:12px;border:1px solid rgb(212, 212, 212);width:162pt;\">There\n  are 229 colleagues who are too young to be enrolled (age 18-21), and who were\n  hired in June 2025 or earlier (so therefore have already passed their 3\n  month's postponement), who are being sent the Not Enrolled letter. This looks\n  like a DM issue that the system is not reading that they have already been\n  sent comms </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;color:rgb(200, 38, 19);font-family:&quot;Century Gothic&quot;, sans-serif;text-align:left;vertical-align:middle;padding-left:24px;border:1px solid rgb(212, 212, 212);width:162pt;\">Avinash\n  to investigate the New Business situation to understand why it is assessing\n  people who have already been assessed, and potentially an update to the\n  records of whether letters have already been sent </td></tr></tbody></table><br> </div><div><table width=860 style=\"border-collapse:collapse;width:648pt;\">\n <tbody><tr height=19>\n\n  <td colspan=4 height=19 width=860 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:648pt;padding-bottom:0cm;padding-top:.75pt;\">System error on not enrolled letters </td></tr></tbody></table><br> </div><div><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">566154 </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Date of birth is 3/10/2023. So not 22\n  until next month </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;color:rgb(200, 38, 19);font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Avinash to re-check Oracle's response\n  to previous SR about age calculation&nbsp; </td></tr></tbody></table><br> </div><div><table width=430 style=\"border-collapse:collapse;width:324pt;\">\n <tbody><tr height=19>\n\n  <td colspan=2 height=19 width=430 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-weight:700;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:324pt;padding-bottom:0cm;padding-top:.75pt;\">System query on Triennial </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;text-align:right;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">18768 </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;font-family:&quot;Century Gothic&quot;, sans-serif;vertical-align:middle;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Should not be enroled as opted out less\n  than a year ago. Is not being enroled. But does not show on the &quot;not\n  enroled&quot; list </td></tr></tbody></table><table width=215 style=\"border-collapse:collapse;width:162pt;\">\n <tbody><tr height=19>\n\n  <td height=19 width=215 style=\"padding-top:1px;padding-right:1px;padding-left:1px;color:black;font-size:11pt;font-family:&quot;Aptos Narrow&quot;, sans-serif;vertical-align:bottom;border:none;color:rgb(200, 38, 19);font-family:&quot;Century Gothic&quot;, sans-serif;border:1px solid rgb(212, 212, 212);width:162pt;padding-bottom:0cm;padding-top:.75pt;\">Aviansh to investigate - how does this\n  show in production </td></tr></tbody></table><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-08T18:19:14.37Z", "changed_date": "2025-09-10T15:02:05.753Z", "priority": 2, "tags": "HyperCare; Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:17.829615"}, "740795": {"id": 740795, "title": "Personal Detail Page is unavailable in Fusion Prod Env", "description": "<div>Personal Detail Page is unavailable in Fusion Prod Env for <PERSON><PERSON> when login with SSO </div><div><br> </div><div>User - <PERSON><PERSON>,&nbsp;<PERSON><PERSON> </div><div>Env - Prod </div><div>Sign In - SSO </div><div><br> </div><div>Evidence enclosed </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-08T16:42:03.997Z", "changed_date": "2025-09-16T13:48:30.74Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:17.951583"}, "740789": {"id": 740789, "title": "Incorrect People Leader - Organisation hierarchy", "description": "<div>Identified incorrect reporting lines in the organisational chart:<br><br>1) <PERSON> currently reporting into <PERSON> should be reporting into Arbi Rai&nbsp; </div><div>2) <PERSON> additional direct reports linked to Customer Care to be reassigned:<br><ul><li>Ines Tenera Mafra Tenera<br> </li><li><PERSON><PERSON><br> </li><li><PERSON><br> </li><li><PERSON><br> </li><li><PERSON><PERSON><PERSON><br> </li> </ul> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T16:07:58.283Z", "changed_date": "2025-09-23T09:55:39.173Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.067712"}, "740766": {"id": 740766, "title": "My Team absence schedule - Not my team", "description": "<div>When I open Absence - Team schedule I see completely different people to who are in my org chart </div><div><br> </div><div><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7fd63d0d-c262-4300-9354-895699bb86ca?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/9c4e5698-090f-48b0-a5bf-1d7e702adf27?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-08T15:22:31.023Z", "changed_date": "2025-09-18T12:50:20.46Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.192556"}, "740759": {"id": 740759, "title": "Mobile numbers visible on organisational chart", "description": "", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "Su<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-08T15:06:11.467Z", "changed_date": "2025-09-18T09:39:58.693Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.355778"}, "740739": {"id": 740739, "title": "INTFIN004 - Add Tax Sub type : VAT-A-GB", "description": "<div>Tax sub type : VA-A-GB needs to be added in the lookup </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-08T14:54:34.387Z", "changed_date": "2025-09-08T15:32:21.543Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.476883"}, "740702": {"id": 740702, "title": "AIMS SK Code Appears as 0 Hours", "description": "<div><div style=\"box-sizing:border-box;\">500147 Crew member sick down route on 27, 28 and 29 Aug whilst on Work Period 26/08 to 29/08.&nbsp; </div><div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/03b1263c-d62f-4ed6-a564-3bf2ae821e23?fileName=image.png\" alt=Image><br> </div><div style=\"box-sizing:border-box;\">&nbsp; </div><div style=\"box-sizing:border-box;\">30/08 is next Off Period and SK code is received showing as hours.&nbsp; This data entry should have errored during the integration but it hasn't. </div><div style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/b05015dd-a34b-4030-8eee-218f77be5c9f?fileName=image.png\" alt=Image style=\"box-sizing:border-box;max-width:100%;align-self:center;\"><br style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">Why has this item been added to the absence record and why didn't it error? </div> </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-08T14:29:35.433Z", "changed_date": "2025-09-22T13:38:25.617Z", "priority": 2, "tags": "HyperCare", "repro_steps": "<div>Payroll ID 500147 - AIMS sickness absence code SK shows on Off Period 30 Aug with 0 hours directly after Work Period with AIMS sick code SKD (sick down route) </div>", "system_info": "", "updated_at": "2025-09-30T11:53:18.637323"}, "740673": {"id": 740673, "title": "Document of Records - Employee reference", "description": "<div>All colleagues should only see the below reference options highlighted in yellow. </div><div><br> </div><div>Can we remove/hide other options please </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/b128b444-d0c3-415c-83a3-26279b71eee1?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-08T13:40:51.477Z", "changed_date": "2025-09-18T12:51:34.503Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.761907"}, "740671": {"id": 740671, "title": "Disbursement of Leave - Error", "description": "<div>Disbursement of leave added and quick pay nothing has pulled through </div><div>See email attached with error </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T13:34:33.237Z", "changed_date": "2025-09-08T13:34:33.237Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:18.880191"}, "740654": {"id": 740654, "title": "INTFIN004 error handling notification when PO line number does not exist in oracle fusion", "description": "<div>INTFIN004 error handling notification when PO line number does not exist in oracle fusion &nbsp;<br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON> S Sandhya", "created_by": "Kishore S", "created_date": "2025-09-08T13:02:58.05Z", "changed_date": "2025-09-08T13:07:56.98Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:19.002988"}, "740652": {"id": 740652, "title": "Not all search functions working correctly", "description": "<div>Trying to add shift pay for 564043.&nbsp; The search function for Administer Compensation will not find an employee that is in Person Management </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c1d53625-8a00-4c92-9e68-c29eba0b9906?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/1d0d92ca-c51c-4a56-b800-6c2e511d2bfa?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T12:56:45.02Z", "changed_date": "2025-09-17T14:23:12.537Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:19.121601"}, "740641": {"id": 740641, "title": "Extending probation - FTE", "description": "<div>When trying to extend a probation period I noticed that it wouldn't allow to and the error message was due to the FTE of the position. </div><div><br> </div><div>Should this error occur on FTE for the above? </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-08T12:49:54.967Z", "changed_date": "2025-09-18T11:28:38.07Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:19.256126"}, "740591": {"id": 740591, "title": "INTHCM004: Resource Exception HDL failed when processing annual leave request", "description": "<span><p>Annual leave request for Employee No. 038662 for 13-Sept/ 30-Sept </p><p>failed with the below error </p><p>Resource Exception HDL failed </p></span><br><br>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T12:16:16.07Z", "changed_date": "2025-09-09T14:29:05.22Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:19.381075"}, "740549": {"id": 740549, "title": "Pilots data dsicrepancy", "description": "<div>Please rectify the following data discrepancy and remove the Salary uplift that has happened. </div><div><br> </div><div><table width=463 style=\"width:347.05pt;border-collapse:collapse;\">\n <tbody><tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;background:#DFEAF3;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">PERSON_NUMBER</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border:solid #DDDDDD 1.0pt;border-left:none;background:#DFEAF3;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">Root Cause</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">495354</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">EBS incorrect salary , Fusion\n  correct as FTE</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">027395</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">Asg Hours 37.5 Contracted\n  hours 17.308</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">561282</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">Asg Hours 37.5 Contracted\n  hours 17.308</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">406720</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:yellow;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">Asg Hours 37.5 Contracted\n  hours 17.308</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">027083</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">Migrated Cap 13, EBS Cap 14</span> </p>\n  </td>\n </tr>\n <tr style=\"\">\n  <td width=226 style=\"width:169.85pt;border:solid #DDDDDD 1.0pt;border-top:none;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">018358</span> </p>\n  </td>\n  <td width=236 style=\"width:177.2pt;border-top:none;border-left:none;border-bottom:solid #DDDDDD 1.0pt;border-right:solid #DDDDDD 1.0pt;background:white;padding:.75pt 5.4pt .75pt 5.4pt;\">\n  <p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-size:9.0pt;font-family:&quot;Arial&quot;,sans-serif;color:black;\">On Cap 14 FT in Fusion but had\n  part time salary migrated.</span> </p>\n  </td>\n </tr></tbody></table><br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-08T11:54:46.47Z", "changed_date": "2025-09-08T16:20:24.547Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:19.541152"}, "740547": {"id": 740547, "title": "Unable to create Reward Bookings", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-08T11:51:42.58Z", "changed_date": "2025-09-10T11:59:17.383Z", "priority": 1, "tags": "Loops", "repro_steps": "<p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">We are unable to create the Reward Bookings. We are getting\nthe error as below. </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">Could you please check from your end and let us know? </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\">VSTERM: </p><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/d6a7fb5e-fda6-48a6-ba7b-58ba3e6b5d3d?fileName=image.png\" alt=Image><br> </div><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><br> </div><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><br> </div><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><br> </div><div style=\"margin:0px 0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">Sharing Some other FC account details and logs from Digital First\nperspective</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">FC account – **********</span></b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;</span></b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">Basket: 0df4d112-db58-4ec8-a4a3-d453431b0e7f </span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">Order Validation Failed:&nbsp; </span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;FC account – <b>**********</b></span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">Request:</span></b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">{&quot;pqId&quot;:&quot;bookOrderCreate&quot;,&quot;variables&quot;:{&quot;request&quot;:{&quot;basketId&quot;:&quot;0df4d112-db58-4ec8-a4a3-d453431b0e7f&quot;,&quot;paymentStatus&quot;:&quot;Authorized&quot;,&quot;deviceInfo&quot;:null,&quot;ipAddress&quot;:&quot;***********&quot;,&quot;browserInfo&quot;:&quot;PostmanRuntime/7.29.2&quot;,&quot;ccCheckConsent&quot;:false}}}\n</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">Response: </span></b> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">{</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp; &quot;errors&quot;: [</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp; {</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;message&quot;: &quot;Request\nfailed.&quot;,</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;extensions&quot;: {</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;code&quot;:\n&quot;500&quot;,</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;reason&quot;:\n&quot;Internal Server Error&quot;,</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;response&quot;:\n&quot;{\\&quot;IsFailure\\&quot;:true,\\&quot;IsSuccess\\&quot;:false,\\&quot;Error\\&quot;:\\&quot;[{\\\\\\&quot;code\\\\\\&quot;:\\\\\\&quot;G051\\\\\\&quot;,\\\\\\&quot;message\\\\\\&quot;:\\\\\\&quot;{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;ROR4013\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;Unable\nto create Fight\nOrder\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;developerMessage\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;OrderApiService.createOrder-channelId:DFWEB,applicationId:DC,OFFER-IDs:[3157d71c-7443-4b03-afd6-0c22c91616a5-FL-1],FlightOrderComposerWorkflowStepImpl.invokeFlightOrderComposer-channelId:DFWEB,applicationId:DC,transactionId:8b693cb3-7cc7-4900-9dae-ba4868332e91FE5230:Unable\nto process - No Response:<b>UNABLE TO PROCESS - NO RESPONSE TO GET AVAIL MLS\nMSG</b>\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;moreInfo\\\\\\\\\\\\\\&quot;:[{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;9002\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;UNABLE\nTO PROCESS - NO RESPONSE TO GET AVAIL MLS\nMSG\\\\\\\\\\\\\\&quot;},{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;9002\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;UNABLE\nTO PROCESS - NO RESPONSE TO GET AVAIL MLS\nMSG\\\\\\\\\\\\\\&quot;}]}\\\\\\&quot;,\\\\\\&quot;developerMessage\\\\\\&quot;:\\\\\\&quot;{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;ROR4013\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;Unable\nto create Fight\nOrder\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;developerMessage\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;OrderApiService.createOrder-channelId:DFWEB,applicationId:DC,OFFER-IDs:[3157d71c-7443-4b03-afd6-0c22c91616a5-FL-1],FlightOrderComposerWorkflowStepImpl.invokeFlightOrderComposer-channelId:DFWEB,applicationId:DC,transactionId:8b693cb3-7cc7-4900-9dae-ba4868332e91FE5230:Unable\nto process - No Response:UNABLE TO PROCESS - NO RESPONSE TO GET AVAIL MLS\nMSG\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;moreInfo\\\\\\\\\\\\\\&quot;:[{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;9002\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;UNABLE\nTO PROCESS - NO RESPONSE TO GET AVAIL MLS\nMSG\\\\\\\\\\\\\\&quot;},{\\\\\\\\\\\\\\&quot;code\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;9002\\\\\\\\\\\\\\&quot;,\\\\\\\\\\\\\\&quot;message\\\\\\\\\\\\\\&quot;:\\\\\\\\\\\\\\&quot;UNABLE\nTO PROCESS - NO RESPONSE TO GET AVAIL MLS MSG\\\\\\\\\\\\\\&quot;}]} | ErrorID:\n8b693cb3-7cc7-4900-9dae-ba4868332e91 | TimeStamp: 9/8/2025 8:22:26\nAM\\\\\\&quot;,\\\\\\&quot;errorSource\\\\\\&quot;:\\\\\\&quot;DeltaServices\\\\\\&quot;}]\\&quot;}&quot;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; }</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;&nbsp;&nbsp; }</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp; ],</span> </p><br> </div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"\"></span> </p>", "system_info": "", "updated_at": "2025-09-30T11:53:19.914888"}, "740503": {"id": 740503, "title": "Load HMRC failing due insufficient privilege", "description": "<div>Hello&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,20783165-d9b9-66ac-9e69-53970dd3bcfd\">@<PERSON></a>&nbsp; the Load HMRC Data process is&nbsp;<span>failing due insufficient privileges.</span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-08T11:23:05.563Z", "changed_date": "2025-09-10T15:04:01.833Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:20.043177"}, "740053": {"id": 740053, "title": "<PERSON><PERSON><PERSON> returned when trying to add override cert", "description": "<div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/19d27fab-af07-4c9a-9fb1-89ba3616acb3?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T08:00:38.547Z", "changed_date": "2025-09-22T13:45:34.55Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div>Connected with <a href=\"#\" data-vss-mention=\"version:2.0,ab30f37d-a6f7-6a5f-8e73-4152db06d551\">@<PERSON></a>&nbsp;- for a quick working session and we were able to submit the override 0 without issue. </div><div><br> </div><div>Please to report us if you face this issue again </div>", "updated_at": "2025-09-30T11:53:20.159263"}, "740046": {"id": 740046, "title": "When loading sickness a Annual leave error is returned", "description": "<div>User - 445371 </div><div><br> </div><div>Trying to load sickness but the error returned states Annual Leave </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/3e6cbd85-1dd9-4775-96a3-4c6087e52767?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Retest", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-08T07:55:24.897Z", "changed_date": "2025-09-09T09:28:15.7Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div style=\"box-sizing:border-box;\"><p>Terminated record /Enrollment end dated record would prorate the balance being front load plan, any recorded entries would be recalculated any over usage of balance may end in error. Once you delete the errored record it will allow to proceed any further updates to the person. </p><br> </div><br><br>", "updated_at": "2025-09-30T11:53:20.309525"}, "740036": {"id": 740036, "title": "INTHCM004-Getting so many Error Notifications", "description": "<div>We are getting so many error notification with Subject -&nbsp;<b>PROD - INTHCM004 - Error - Batch ID: nKIGaYwoEfC5IVsX_HWfNw , </b>please refer to the attachment. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-08T07:45:59.167Z", "changed_date": "2025-09-10T14:31:01.36Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:20.444909"}, "739984": {"id": 739984, "title": "INTHCM002/003/004 -Discrepancy Identified in Integration Notification – Header and Content Mismatch", "description": "<div style=\"margin-top:14px;margin-bottom:14px;\"><p>We have identified a <strong>discrepancy between the header and content</strong> of the integration notification. Specifically, the information presented in the header does not align with the corresponding data within the body of the notification. This misalignment may result in misinterpretation or potential processing errors. </p><p>Additionally, we have observed that <strong>an error file's location is being generated despite all integration stages completing successfully</strong>.&nbsp; </p><p>We kindly request you to review this matter at your earliest convenience. Please refer to the attached documentation for further details and supporting evidence. </p><p>FYI <a href=\"#\" data-vss-mention=\"version:2.0,f5510c8a-f7c7-6936-81c9-c133bb2948a9\">@Ra<PERSON><PERSON><PERSON></a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,2756ae4a-e494-6a36-92b1-11080478e130\">@<PERSON></a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,9ef6c22c-ef06-62e0-9c40-29da9f0a1e3a\">@<PERSON><PERSON><PERSON></a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,e7b44cd7-b62c-630c-82f1-db0350c50f43\">@Usha G S</a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,936a0d07-4b8a-67dc-ad21-8d38a734399c\">@Ridhi Sharma</a> </p><br> </div><br>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-08T06:51:37.357Z", "changed_date": "2025-09-15T10:01:06.977Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:20.646072"}, "739982": {"id": 739982, "title": "INTHCM003-Getting an Error Report", "description": "<div>Error Report for INTHCM003 – Please Refer to the Attachment<br> </div><div><br> </div><div>FYI- <a href=\"#\" data-vss-mention=\"version:2.0,f5510c8a-f7c7-6936-81c9-c133bb2948a9\">@<PERSON><PERSON><PERSON><PERSON> T<PERSON></a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,2756ae4a-e494-6a36-92b1-11080478e130\">@<PERSON></a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,9ef6c22c-ef06-62e0-9c40-29da9f0a1e3a\">@<PERSON><PERSON><PERSON></a> <a href=\"#\" data-vss-mention=\"version:2.0,936a0d07-4b8a-67dc-ad21-8d38a734399c\">@<PERSON><PERSON><PERSON></a> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-08T06:37:07.553Z", "changed_date": "2025-09-10T14:18:53.26Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:20.789541"}, "739964": {"id": 739964, "title": "INTFIN043 -Mapping change of  COA", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-07T18:03:59.703Z", "changed_date": "2025-09-17T10:42:33.99Z", "priority": 2, "tags": "HyperCare", "repro_steps": "<div>Some eMRO invoices have failed to transfer to Fusion due to 'Invalid Distribution'. Please can you check the COA mapping (eMRO to Fusion) to determine why segment 3 is missing for the below distribution code </div><div><br> </div><div>Example invoice: CONSOL140 - 01.34030..0.A339GEN.0.0.0.0<br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:20.993086"}, "739963": {"id": 739963, "title": "INTFIN034 - GB TBOX Rate missing", "description": "<div>Invoices with GB TBOX (Tax Code) calculating 20% instead of 0%. Tax team to fix the issue </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-07T17:49:03.06Z", "changed_date": "2025-09-08T13:01:02.21Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:21.131380"}, "739962": {"id": 739962, "title": "INTFIN043 - Invalid Precision on Invoices", "description": "<div>Additional decimal precision places are getting added to amount </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON> S Sandhya", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-07T17:43:39.313Z", "changed_date": "2025-09-08T13:01:38.377Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:21.288483"}, "739961": {"id": 739961, "title": "INTFIN043 -Invoice Line Numbering", "description": "<div><span>Multiple invoice lines coming with same order line number for eMRO and also invoice lines are referring to different PO numbers not referenced in the file. See sample invoices below. Config missed during design.</span><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/8467afb0-3bb3-4f07-b185-04fb859fd54d?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-07T15:57:51.88Z", "changed_date": "2025-09-18T12:43:23.143Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:21.431056"}, "739960": {"id": 739960, "title": "INTHCM004 - 4-0001044998 - Leave request failing with 404 error", "description": "<div>Leave request for person number&nbsp;<span>038662 failed with 404 error</span> </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "An<PERSON>ta <PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T14:46:30.537Z", "changed_date": "2025-09-08T10:46:46.9Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:21.590103"}, "739959": {"id": 739959, "title": "INTHCM004: Leave requests failed with Insufficient balance message", "description": "<div>Leave requests failed. </div><div><span><ul><li>Employee No. 543201 for 21-Aug - <strong>Response</strong> - You can't record this absence because your TOIL Plan - Hours plan balance will fall below the minimum limit set for this absence type. (ANC-3405002). </li><li>Employee No. 463787 for 15-Aug - <strong>Response</strong> - You can't record this absence because your TOIL Plan plan balance will fall below the minimum limit set for this absence type. (ANC-3405002). </li> </ul></span><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T13:58:08.727Z", "changed_date": "2025-09-23T09:43:58.787Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:21.811600"}, "739958": {"id": 739958, "title": "INTHCM054- no Sick has loaded from integration", "description": "<div>There doesn't seem to be any sick records loaded as part of INTHCM054 </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T13:48:10.66Z", "changed_date": "2025-09-15T10:05:31.397Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.050654"}, "739954": {"id": 739954, "title": "MAT loaded with \"wont return to work\" flagged and should not be", "description": "<div>All MAT leave has been loaded with &quot;<span>wont return to work&quot; flagged but this should not be the case.</span> </div><div><span><br></span> </div><div><span><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/3c83e447-65e4-4192-beda-d5aec960319e?fileName=image.png\" alt=Image><br></span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-07T12:16:14.95Z", "changed_date": "2025-09-23T09:39:53.08Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.313083"}, "739953": {"id": 739953, "title": "INTHCM004 -SR(4-0001044281) - Acru - HDL's not loaded", "description": "<div>A<PERSON>ru not loaded for some colleagues.&nbsp; <PERSON><PERSON><PERSON> has checked and the HDL's haven't loaded. </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T11:44:01.507Z", "changed_date": "2025-09-10T14:29:44.297Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.496063"}, "739952": {"id": 739952, "title": "INTHCM004 Acru not loaded due to incorrect TOIL plan", "description": "<div><PERSON><PERSON><PERSON> wasn't loaded in INTHCM004 for colleague&nbsp;<span>463787.</span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T11:30:01.16Z", "changed_date": "2025-09-10T14:24:54.537Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.659807"}, "739951": {"id": 739951, "title": "REPHCM016 - View employees training record", "description": "<div>REPHCM016 - View an employees' training record returns zero data in the Bi Publisher report. OBTI dashboard is working correctly.&nbsp;<div style=\"box-sizing:border-box;\"><br> </div><div style=\"box-sizing:border-box;\">My cutover task is to create multiple schedules of this report to replicate what is received by the business today by the legacy system. When i run the report it returns no data. This happened in UAT, and was a permissions issue. <PERSON> has check my profile and the only thing i don't have in PROD vs UAT is Hiring Manager.&nbsp; </div><div style=\"box-sizing:border-box;\">The OBTI dashboard is now working for me, however the Bi report is not and this is what i need to be able to schedule.&nbsp; </div><div style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/94d20bc9-39e6-46a4-b2c1-89ad15e0ddea?fileName=image.png\" alt=Image style=\"box-sizing:border-box;max-width:100%;align-self:center;\"> </div> </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme\\People Functional", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-07T10:49:46.777Z", "changed_date": "2025-09-09T14:44:03.587Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.848708"}, "739949": {"id": 739949, "title": "INTHCM004 Acru not showing in Oracle", "description": "<div>A<PERSON><PERSON> sent from Genesys isn't showing in Oracle for person number&nbsp;<span>463787 20/21 Aug.<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/56e67d09-2e34-4ec7-ae53-65290e563013?fileName=image.png\" alt=Image></span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-06T18:23:06.817Z", "changed_date": "2025-09-08T10:48:31.14Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:22.993529"}, "739948": {"id": 739948, "title": "INTHCM061-AIMS sickness hasn't been imported as full days prior to August", "description": "<div><PERSON> says&nbsp;<span>historic occasions of sickness have imported not as full days prior to Aug which will also impact sick pay entitlement.</span> </div><div><span><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/4436098b-48e5-41e9-b91b-3408eff00c5c?fileName=image.png\" alt=Image><br></span> </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-06T17:02:19.767Z", "changed_date": "2025-09-08T13:39:45.537Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:23.155914"}, "739947": {"id": 739947, "title": "INTHCM061 AIMS Absence - NS codes missing from data extract from AIMS", "description": "<div>NS (No Show) codes aren't in the AIMS extract file for person no.s 549378, 14/15 Aug and 574995 27 Aug.&nbsp; (Ignore the first highlighted person in the screenshot below.)&nbsp; </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/f43e5254-5600-450a-b7e4-466ab7ddc7e2?fileName=image.png\" alt=Image><br> </div><div>(Note:&nbsp;<span style=\"display:inline !important;\"><span>&nbsp;</span><PERSON> has manually added the NS in Fusion for 574995.)</span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-06T16:48:22.283Z", "changed_date": "2025-09-16T14:01:16.15Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:23.324028"}, "739946": {"id": 739946, "title": "INTHCM061 AIMS Absence MISS duty codes not coming in to Oracle", "description": "<div>MISS duty codes aren't coming in to Oracle. </div><div>e.g.&nbsp; </div><div><span><table><tbody><tr><td>438772 </td><td>Virgin Atlantic </td><td>CSS </td><td>MISS </td><td>04/08/2025 </td></tr><tr><td>562563 </td><td>Virgin Atlantic </td><td>CC </td><td>MISS </td><td>05/08/2025 </td></tr><tr><td>566343 </td><td>Virgin Atlantic </td><td>CC </td><td>MISS </td><td>03/08/2025 </td></tr><tr><td>572532 </td><td>Virgin Atlantic </td><td>CC </td><td>MISS </td><td>04/08/2025 </td></tr></tbody></table></span><br><br><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-06T16:35:46.187Z", "changed_date": "2025-09-08T20:28:40.27Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:23.555834"}, "739945": {"id": 739945, "title": "INTHCM061/2 AIMS Absence and Work Schedules incorrect data in extract if crew member calls fit", "description": "<div>From <PERSON>: </div><div>&quot;<span>we have an issue with the work schedules and the sickness from the Aims feed into fusion which means if a crew ember goes sick for a duty period say 5 days but calls fit after 3 days the data feed is not picking up the new work schedule to say they are now back at work it is taking the original trip pattern as sick entirely. this hugely impacts the accuracy of records and the calculation of sick pay &quot;</span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-06T15:37:16.577Z", "changed_date": "2025-09-23T09:47:20.75Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:23.729035"}, "739942": {"id": 739942, "title": "INTFIN019 - File naming convention is incorrect", "description": "<div><span><p>File naming convention is incorrect. It should in that pattern i6_FMS_AP*.csv.pgp </p></span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-06T07:56:20.19Z", "changed_date": "2025-09-23T10:10:38.567Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:23.936527"}, "739941": {"id": 739941, "title": "INTFIN001 - Some PO's are having special characters", "description": "<div><span><p>Some PO's are having special characters like &amp;, '', bullet, tab and enter </p></span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-06T07:43:19.047Z", "changed_date": "2025-09-08T16:24:32.733Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:24.136040"}, "739939": {"id": 739939, "title": "Payslip - Overpayment Oustanding balance is not getting fetched if it is <£1", "description": "<div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/15e5c313-835b-41de-ac84-f6b6e57914e4?fileName=image.png\" alt=Image><br><br><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/26ce0c94-1631-4262-9ccf-823a8ee409ac?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-05T18:36:00.76Z", "changed_date": "2025-09-06T12:01:43.693Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:24.285117"}, "739938": {"id": 739938, "title": "Payslip- has incorrect Pension SS Employee SS Contributions", "description": "<div>Generated payslip has incorrect pension ss employee ss contributions in YTD section.<br><br><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e854249a-2159-4b36-8c20-f452f3f0ee59?fileName=image.png\" alt=Image style=\"width:556px;height:528px;\" width=556 height=528><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-05T16:36:26.33Z", "changed_date": "2025-09-09T10:16:17Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:24.510503"}, "739900": {"id": 739900, "title": "Letter generated x8 copies - B<PERSON> filed on \"ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content", "description": "<div>as the user when loading the journey and viewing the letter - the letter had generated x8 copies </div>", "work_item_type": "Bug", "state": "Resolved", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-05T15:33:42.187Z", "changed_date": "2025-09-07T13:15:30.14Z", "priority": 2, "tags": "RC; Smoke testing; VA Letter", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">05/09/2025  16:31 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:24.698833"}, "739899": {"id": 739899, "title": "Journey wont submit - Bug filed on \"ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content\"", "description": "<div><span style=\"display:inline !important;\">error message attached when submitting the journey as the user</span><br> </div><div><span style=\"display:inline !important;\"><br></span> </div><div><span style=\"display:inline !important;\"><ol style=\"margin-bottom:0cm;margin-top:0cm;\" start=1 type=1>\n <li style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;color:black;\"><i><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">e-signature\n     integration is different to UAT. In UAT it is defined as 'XXVA_ESIGNQ' but\n     in Dev3 as 'XXVA_ESIGN', so journey was not able to find the e-signature.</span></i> </li>\n <li style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;color:black;\"><i><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">In\n     Integration setup - Display signature Pad is set to No, which should be\n     set to Yes.</span></i> </li> </ol><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;\"><i><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">After changing\n&nbsp;the e-signature int name in journey and display pad to Yes, the pdf is\nstoring to DOR.&nbsp;</span></i> </p><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;\"><i><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;</span></i> </p><p style=\"margin:0cm;font-size:11pt;\"><font face=\"Century Gothic, sans-serif\"><i>Josie to confirm the correct e-sig is&nbsp;<i style=\"box-sizing:border-box;font-family:Calibri, sans-serif;text-align:left;\"><span style=\"box-sizing:border-box;font-family:&quot;Century Gothic&quot;, sans-serif;\">'XXVA_ESIGN' this needs to be replicated across all journeys and display pad set to yes.</span></i></i></font> </p><br></span> </div><div> </div>", "work_item_type": "Bug", "state": "Resolved", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-05T15:31:54.67Z", "changed_date": "2025-09-07T13:15:56.263Z", "priority": 2, "tags": "RC; Smoke testing; VA Letter", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">05/09/2025  16:28 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:24.918907"}, "739861": {"id": 739861, "title": "RM Flight Offers - Unable to complete ancillary post purchase", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-05T12:51:02.597Z", "changed_date": "2025-09-18T07:19:18.737Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Creating a copy of bug <a href=\"https://virginatlantic.visualstudio.com/Air4%20Channels%20Testing/_workitems/edit/738729/\" data-vss-mention=\"version:1.0\" target=_blank rel=\"noopener noreferrer\">#738729</a>&nbsp;to track against the Flight Offers release. </div>", "system_info": "", "updated_at": "2025-09-30T11:53:25.068140"}, "739842": {"id": 739842, "title": "Production Baseline : INTHCM008 and INTHCM010 EASA Monthly and Expiry Report", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "An<PERSON>ta <PERSON>", "created_by": "An<PERSON>ta <PERSON>", "created_date": "2025-09-05T12:03:35.257Z", "changed_date": "2025-09-05T12:03:40.263Z", "priority": 2, "tags": "", "repro_steps": "<div>Baseline file did not fetch any data </div>", "system_info": "", "updated_at": "2025-09-30T11:53:25.225164"}, "739837": {"id": 739837, "title": "O365 Integration - Interviewer calendar shown as unknown", "description": "<div>Interview Availability doesn't give a view of busy or free slots in the create interview page (see screenshot 1) regardless O365 integration is set up properly and validated (see screenshot 2).&nbsp; </div><div><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c757399f-ffa6-4b44-a780-484544bd1010?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/670c7321-5928-48a1-8137-e6e619853eb9?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-05T11:26:56.537Z", "changed_date": "2025-09-17T09:22:27.31Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:25.391171"}, "739836": {"id": 739836, "title": "INTHCM070 and INTHCM003 - some activity codes missed on the data from Genesys", "description": "<div><span style=\"display:inline !important;\">Some activity codes are missing from these files.</span><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme\\Integrations and Reports", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-05T11:25:26.7Z", "changed_date": "2025-09-15T10:08:40.07Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div><br> </div><div><br> </div>", "updated_at": "2025-09-30T11:53:25.567289"}, "739827": {"id": 739827, "title": "INTHCM073- Duplicate entries in file", "description": "<p>Duplicate entries in the INTHCM073 file, some examples below.&nbsp; </p><p>&nbsp; </p><figure><table><tbody><tr><td>SourceSystemId </td><td>PersonNumber </td><td>Employer </td><td>AbsenceType </td><td>StartDate </td><td>StartTime </td><td>StartDateDuration </td><td>EndDate </td><td>EndTime </td><td>EndDateDuration </td><td>AbsenceStatus </td><td>ApprovalStatus </td></tr><tr><td>569717-Unpaid Leave - Days-2025/08/24 </td><td>569717 </td><td>Virgin Atlantic Airways Limited </td><td>Unpaid Leave - Days </td><td>24/08/2025 </td><td>00:00 </td><td>1 </td><td>24/08/2025 </td><td>01:00 </td><td>1 </td><td>SUBMITTED </td><td>APPROVED </td></tr><tr><td>569717-Unpaid Leave - Days-2025/08/24 </td><td>569717 </td><td>Virgin Atlantic Airways Limited </td><td>Unpaid Leave - Days </td><td>24/08/2025 </td><td>00:00 </td><td>1 </td><td>24/08/2025 </td><td>01:00 </td><td>1 </td><td>SUBMITTED </td><td>APPROVED </td></tr><tr><td>577281-Unpaid Leave - Days-2025/08/07 </td><td>577281 </td><td>Virgin Atlantic Airways Limited </td><td>Unpaid Leave - Days </td><td>07/08/2025 </td><td>00:00 </td><td>1 </td><td>07/08/2025 </td><td>01:00 </td><td>1 </td><td>SUBMITTED </td><td>APPROVED </td></tr><tr><td>577281-Unpaid Leave - Days-2025/08/07 </td><td>577281 </td><td>Virgin Atlantic Airways Limited </td><td>Unpaid Leave - Days </td><td>07/08/2025 </td><td>00:00 </td><td>1 </td><td>07/08/2025 </td><td>01:00 </td><td>1 </td><td>SUBMITTED </td><td>APPROVED </td></tr></tbody></table></figure><p>&nbsp; </p><br><br>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-05T10:36:39.18Z", "changed_date": "2025-09-22T14:30:06.11Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:25.790732"}, "739821": {"id": 739821, "title": "INTHCM073-Annual Leave - Day duration not set to 1 day", "description": "<div>INTHCM073- I notice Annual leave seems to have loaded as 0.09 instead of 1 day. </div><span><p>&nbsp; </p><p>User - 575343 </p><p><span><img alt=image src=\"data:image/jpeg;base64,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\"><br></span> </p></span>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-05T10:18:57.463Z", "changed_date": "2025-09-22T14:26:47.793Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div>System was showing Annual Leave and Annual Leave - Days, that's the duration was incorrect, which is now resolved for the reported employee.<br><br>The issue got fixed after sometime, no changes were made in the config. </div>", "updated_at": "2025-09-30T11:53:25.961646"}, "739745": {"id": 739745, "title": "INTHCM008 - EASA Outbound monthly - no data in prod baseline run", "description": "<div>Need to analyze why no data was pulled in Baseline Run. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "An<PERSON>ta <PERSON>", "created_by": "An<PERSON>ta <PERSON>", "created_date": "2025-09-05T08:20:26.41Z", "changed_date": "2025-09-05T11:07:31.593Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:26.132662"}, "739703": {"id": 739703, "title": "INTHCM098 - missing config in prod for probation pass letter", "description": "<div>Baseline run for INTHCM098 - Probation pass letter did not pick any data due to missing configuration for the existing records. </div><div><br> </div><div>On the onboarding journey, verifying probation pass status must be set as Yes. </div>", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "An<PERSON>ta <PERSON>", "created_date": "2025-09-05T07:17:40.56Z", "changed_date": "2025-09-08T10:44:41.003Z", "priority": 2, "tags": "Cutover; HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:26.451052"}, "739695": {"id": 739695, "title": "INTHCM062-Dates and People missing from  file", "description": "<div><span><p>I only have 461 people in this file when expecting 4,000+ as should be Cabin Crew and Pilots. And of the ones I have received I am missing dates, so have not received a full month, see example below.&nbsp; </p><p>&nbsp; </p><p>&nbsp; </p><table><tbody><tr><td><span>E005079</span> </td><td>20250801 </td></tr><tr><td><span>E005079</span> </td><td>20250802 </td></tr><tr><td><span>E005079</span> </td><td>20250807 </td></tr><tr><td><span>E005079</span> </td><td>20250808 </td></tr></tbody></table></span><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-05T06:44:39.513Z", "changed_date": "2025-09-09T15:23:40.553Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:26.645264"}, "739661": {"id": 739661, "title": "Same figure for YTD and Monthly Gross Earnings on payslip", "description": "<div>instead of Year to date balance in YTD section, gross earnings has the monthly balance </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/945e9117-cf0f-4027-bbc4-2be9b276dd15?fileName=image.png\" alt=Image style=\"width:385px;\" width=385 height=300><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T22:04:46.79Z", "changed_date": "2025-09-09T20:26:19.477Z", "priority": 2, "tags": "HyperCare; Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:26.809622"}, "739657": {"id": 739657, "title": "INTHCM070-The integration is timing out.", "description": "<div>The integration is timing out.<br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-04T19:07:17.753Z", "changed_date": "2025-09-23T10:05:51.317Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div>Action Plan: </div><div><ol><li>Deloitte to raise an SR - Done </li><li>Deloitte to implement based on oracle's reply&nbsp; </li> </ol> </div>", "updated_at": "2025-09-30T11:53:27.064088"}, "739633": {"id": 739633, "title": "Pension FF logic to be applied to family leave absence types", "description": "<div>There is a piece of code re pension missing in the family leave absence type FFs. Issue was raised in UAT for one absence type, but similar logic is needed for all family leaves. <br><br>-----<br>Migrated components as a part of bug fix:<br>Pension SS Calculator FF<br>XXVA_PY_ABS_CHILD_BIRTH_WEEK.&nbsp;XXVA_PY_ABS_CHILD_BIRTH_WEEK_ACTUAL ValueSet<br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-04T17:43:50.587Z", "changed_date": "2025-09-09T08:08:26.87Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "<div>Tanya to retest in dev3 07/09 </div><div><PERSON><PERSON><PERSON> to deploy to prod 08/09 </div>", "updated_at": "2025-09-30T11:53:27.265097"}, "739632": {"id": 739632, "title": "Payslip has blank line and -- under earnings section", "description": "<div>There is blank line in earnings section with -- under date column. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T17:12:30.74Z", "changed_date": "2025-09-06T11:59:49.617Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:27.458225"}, "739625": {"id": 739625, "title": "Images and Videos on EnhanceMedia Career Site Pages are not displayed in the right format", "description": "<div>All the career sites header URLs are working fine to take the users to the right page but some images and videos seem to be loaded or displayed in the wrong format as the screenshots below shows. <a href=\"#\" data-vss-mention=\"version:2.0,dee1d60a-d5b2-6003-917e-094ae8c730f9\">@<PERSON></a>&nbsp;probably need to check with EnhanceMedia as the pages are on EM career site i suppose. </div><div><br> </div><div><b>Our People</b> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e5ca501e-ab81-4c31-b1bb-4b181627cf05?fileName=Our%20people.png\" alt=\"Our people.png\"> </div><div><br> </div><div><b>Be yourself</b> </div><div><br><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/cb483952-c61a-468c-807b-83f8e63d1160?fileName=Be%20yourself%202.png\" alt=\"Be yourself 2.png\"><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/60210163-6433-4e90-a4c5-b78db42bdb8d?fileName=be%20yourself.png\" alt=\"be yourself.png\"><br> </div><div><br> </div> </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-04T16:48:58.36Z", "changed_date": "2025-09-05T12:45:10.51Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:27.670255"}, "739614": {"id": 739614, "title": "Bug filed on \"ST-HCM-Helpdesk Report-Access dahsboard report and tests filters and stats\"", "description": "<div>Cannot close helpdesk ticket as an agent to feed dashboard stats for smoke test </div>", "work_item_type": "Bug", "state": "Resolved", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T16:02:34.47Z", "changed_date": "2025-09-05T09:22:36.277Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">04/09/2025  17:00 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Helpdesk Report-Access dahsboard report and tests filters and stats&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:red;\">Failed </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Helpdesk Report-Access dahsboard report and tests filters and stats<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Helpdesk Report-Access dahsboard report and tests filters and stats </div><div style=\"padding-top:10px;\"><div>Comments: Can not close tickets as an agent to feed dashboard stats </div> </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:27.876703"}, "739586": {"id": 739586, "title": "Bug filed on \"ST-HCM-Letters-Run a recruitment letter to generate template and review content\"", "description": "<div><ul><li>no branding </li><li>no notice period </li><li>missing start date </li><li>format off compared to template loaded to library </li> </ul> </div>", "work_item_type": "Bug", "state": "Blocked", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T15:46:10.12Z", "changed_date": "2025-09-08T08:38:59.887Z", "priority": 2, "tags": "RC; Smoke testing; VA Letter", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">04/09/2025  16:44 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Letters-Run a recruitment letter to generate template and review content&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Letters-Run a recruitment letter to generate template and review content<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Letters-Run a recruitment letter to generate template and review content </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:28.079195"}, "739578": {"id": 739578, "title": "Bug filed on \"ST-HCM-Letters-Check DOR has stored document correctly\"", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T15:34:48.78Z", "changed_date": "2025-09-04T15:43:38.527Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">04/09/2025  16:31 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Letters-Check DOR has stored document correctly&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:red;\">Failed </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Letters-Check DOR has stored document correctly<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Letters-Check DOR has stored document correctly </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:28.270370"}, "739576": {"id": 739576, "title": "Bug - ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T15:31:16.06Z", "changed_date": "2025-09-05T15:28:11.54Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">04/09/2025  16:29 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:red;\">Failed </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Letters-Run a Journey letter to ensure tigger and generated template, review content </div><div style=\"padding-top:10px;\"><div>Comments: Letter content incorrect: </div><div>- no branding </div><div>- start date missing </div><div>- formatting is out from template loaded to library </div><div>- Notice period not pulling in </div> </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:28.483977"}, "739496": {"id": 739496, "title": "Basic Salary Fast formula Variable isn't Initialised", "description": "<div>Error occurred while processing basic salary fast formula. Line 410 a local variable used before&nbsp;Initialised. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-04T14:10:38.393Z", "changed_date": "2025-09-04T17:08:59.577Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:28.689017"}, "739493": {"id": 739493, "title": "Annual Leave Liability: High employer amount", "description": "<div><span style=\"display:inline !important;\">as per PCT discussion Annual Leave liability shouldn't have impact on employer cost. Please fix the config</span><br> </div><div><span style=\"display:inline !important;\"><br></span> </div><div><span style=\"display:inline !important;\"><br></span> </div><div><span style=\"display:inline !important;\"><br></span> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-04T14:03:52.137Z", "changed_date": "2025-09-04T17:09:57.02Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">9/4/2025  12:23 PM </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-HCM-Payroll-002-Run and roll back Payroll (non Prod environment)&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\">ST-HCM-Payroll-002-Run and roll back Payroll (non Prod environment)<div style=\"padding-top:10px;\">Expected Result </div><div>ST-HCM-Payroll-002-Run and roll back Payroll (non Prod environment) </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:28.916098"}, "739491": {"id": 739491, "title": "Object reference not set to an Object", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Engineering Transformation Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON> .", "created_date": "2025-09-04T14:02:52.243Z", "changed_date": "2025-09-10T12:20:54.42Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:29.085026"}, "739490": {"id": 739490, "title": "HMRC Retrieval Error", "description": "<div>Error was occurred during HMRC Data Retrieval Process. </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-04T14:02:23.83Z", "changed_date": "2025-09-04T17:09:14.343Z", "priority": 2, "tags": "Smoke testing", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:29.276052"}, "739483": {"id": 739483, "title": "P29_INC0864525_High_413899_Control Sheets - blank items when tasks are cancelled or busted Failed", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "Engineering Transformation Programme\\Product", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-04T13:48:34.14Z", "changed_date": "2025-09-04T13:48:53.23Z", "priority": 2, "tags": "", "repro_steps": "<span style=\"color:6d6d6d;\">Test</span>: <b>P29_INC0864525_High_413899_Control Sheets - blank items when tasks are cancelled or busted</b><br><span style=\"color:6d6d6d;\">Priority</span>: 2<br><span style=\"color:6d6d6d;\">Test file</span>: <span style=\"color:6d6d6d;\">not available</span><br><span style=\"color:6d6d6d;\">Machine</span>: <span style=\"color:6d6d6d;\">not available</span><br><span style=\"color:6d6d6d;\">Tested build</span>: <span style=\"color:6d6d6d;\">not available</span><br><span style=\"color:6d6d6d;\">Error message</span>: <span style=\"color:6d6d6d;\">not available</span><br><span style=\"color:6d6d6d;\">Stack trace</span>: <span style=\"color:6d6d6d;\">not available</span><br><br>", "system_info": "", "updated_at": "2025-09-30T11:53:29.496364"}, "739464": {"id": 739464, "title": "INTHCM072-Controlled Start - Confirmation on Work Schedule Names", "description": "<div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/63ebf688-dff4-4b86-8f7c-8a8733ed52bb?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-04T13:21:28.47Z", "changed_date": "2025-09-08T10:48:56.967Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:29.698493"}, "739459": {"id": 739459, "title": "RM Flight Offers - Unable to access Reshop", "description": "", "work_item_type": "Bug", "state": "Resolved", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-04T13:09:09.933Z", "changed_date": "2025-09-05T12:43:15.72Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Retrieve a reshop eligible booking on My Booking </div><div>Click on Change Flight </div><div><br> </div><div>Expected: User is able to navigate to the Reshop flow </div><div>Actual: User is not able to navigate to Reshop flow and is shown an error message </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/30c5940d-ef93-4f47-98e3-b143650bff92?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>PNR: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/561011bd-1efb-4173-a2b9-2af307dbc574?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:29.947276"}, "739329": {"id": 739329, "title": "BUG 739248 - ST-FIN-Lease Accounting-002-Run reports", "description": "<div>Able to run the following reports in lease accounting, but fails to generate the report, appears to be access issue, please see attached Log file </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/35e1e0c1-a47c-4f70-89da-dc43976adfe0?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/32c07812-f1c3-45a8-a89e-404a6c996db2?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Resolved", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON><PERSON><PERSON> <PERSON>", "created_date": "2025-09-04T09:00:12.917Z", "changed_date": "2025-09-05T11:03:34.497Z", "priority": 2, "tags": "Cutover", "repro_steps": "<div><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">04/09/2025  09:55 </td><td style=\"vertical-align:top;padding:2px 7px 2px 10px;\">Bug filed on &quot;ST-FIN-Lease Accounting-002-Run reports&quot; </td></tr></table><hr style=\"border-color:black;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Step no. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Result </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Title </td></tr><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">1. </td><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;color:rgb(29, 108, 255);\">None </td><td style=\"vertical-align:top;padding:2px 7px;\"><div><p><span>ST-FIN-Lease Accounting-002-Run reports</span> </p> </div><div style=\"padding-top:10px;\">Expected Result </div><div><p><span>ST-FIN-Lease Accounting-002-Run reports</span> </p> </div> </td></tr></table><hr style=\"border-color:white;\"><table><tr><td style=\"vertical-align:top;padding:2px 7px;font-weight:bold;\">Test Configuration: </td><td style=\"vertical-align:top;padding:2px 7px 2px 100px;\">Windows 10 </td></tr></table><hr style=\"border-color:white;\"><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:30.156229"}, "739015": {"id": 739015, "title": "Balance screen showing previous name not current name", "description": "<div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7bee5d2d-260d-4f73-8b4e-9fd5c542b2a9?fileName=image.png\" alt=Image> </div><div>Balance screen showing previous name not current name<br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme", "assigned_to": "Su<PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-03T16:14:17.273Z", "changed_date": "2025-09-18T09:40:22.953Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:30.354341"}, "738889": {"id": 738889, "title": "Duration does not get updated to reflect work schedule", "description": "<div>User - 020459 WS is 9.5hrs, but absence is showing as 7.5hrs </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e31fa0ca-7343-4e9f-83cd-d68b45372d6c?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/dcf61e3e-d020-4d11-a064-fbe67d238a63?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>I have run the &quot;evaluate absence&quot; process but it did not update as expected </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-03T14:02:36.99Z", "changed_date": "2025-09-22T14:15:11.037Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "<div>Absence entries need to be deleted from HDL and reload again same entries will resolve the issue, as it will pick the new WS attached to an employee. </div>", "updated_at": "2025-09-30T11:53:30.636302"}, "738801": {"id": 738801, "title": "********* : DF | Staging/ TVTH / HPP / HPP page- PayPal GBP issue", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-03T12:56:23.85Z", "changed_date": "2025-09-08T12:34:24.8Z", "priority": 3, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Team is experiencing an issue in the&nbsp;</span><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">non-production environment, </span><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">booking failed on HPP due to a&nbsp;PayPal error. Please find the\ndetails below for further investigation.</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Environment\n: &nbsp;Staging/ TVTH / HPP / HPP page- PayPal GBP issue </span><span style=\"font-size:12.0pt;font-family:&quot;Century Gothic&quot;,sans-serif;\"></span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Issue:\nPayPal issue on HPP<br>\nError Message: Bad Request<br>\nPRI: 7B668EF231174158A0F8B80248770653</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Channel\n: DC / WEB<br>\nPriority: <span style=\"color:black;\">Medium </span><br>\nImpacted: All GBP currency - &nbsp;PayPal testing only<br>\nBP: <span style=\"color:black;\">Medium </span><br>\nHow many times was it successful? Except for the&nbsp;GBP currency,\nthe&nbsp;others are working fine </span> </p><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/503834a7-fcbd-4521-9c56-746aeb77dcae?fileName=image.png\" alt=Image><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:30.951508"}, "738750": {"id": 738750, "title": "Unable to log into QT TRN with TRNUSER1", "description": "<div>I'm unable to log into the TRN UAT environment with the TRNUSER users using the LOGIN and PASSWORD information. This is required for me to be able to provide training to the production staff. </div>", "work_item_type": "Bug", "state": "New", "area_path": "Engineering Transformation Programme\\Product", "assigned_to": "", "created_by": "<PERSON>", "created_date": "2025-09-03T10:37:30.19Z", "changed_date": "2025-09-03T10:37:30.19Z", "priority": 1, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:31.187805"}, "738729": {"id": 738729, "title": "RM Retail/APP - iOS/Android Unable to complete ancillary post purchase for Economy Delight seats", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-03T09:53:52.273Z", "changed_date": "2025-09-15T10:53:49.903Z", "priority": 1, "tags": "UAT", "repro_steps": "<div>Retrieve a booking on DotCom/APP </div><div>Navigate to post purchase seatmap and purchase a seat/upgrade </div><div><br> </div><div>Expected: User is able to purchase the seat </div><div>Actual: Purchase fails and user is unable to complete seat purchase </div><div><br> </div><div>We're able to see a 500 response on the aLaCarteUpsellPurchase service call.&nbsp; </div><div><br> </div><div>paymentReferenceId: '6622B6254AD84C26B0BF8819670EFB75'} </div><div>&quot;paymentReferenceId&quot; : &quot;10c40a9a-b6fd-4975-bb85-b523ce2dcdf2&quot;, </div><div>paymentReferenceId: '90244F2C26CD4B7E9780D4BAE57353ED'}<br> </div><div> </div><div><br> </div><div> </div><div>Cards used: </div><div>**************** </div><div>*************** </div><div><span lang=EN-GB style=\"alignment-baseline:auto;backface-visibility:visible;baseline-shift:0px;border-width:0px;border-style:none;border-spacing:2px;border-radius:0px;box-shadow:none;box-sizing:content-box;clip:auto;clip-path:none;clip-rule:nonzero;color-interpolation:srgb;color-interpolation-filters:linearrgb;color-rendering:auto;columns:auto;column-fill:balance;column-rule:0px rgb(0, 0, 0);column-span:none;counter-increment:none;counter-reset:none;cursor:text;display:inline;dominant-baseline:auto;fill-opacity:1;fill-rule:nonzero;flex:0 1 auto;flex-flow:row;font-family:Calibri, sans-serif;font-size:14.6667px;font-size-adjust:none;font-variant:none;image-rendering:auto;margin:0px;mask:none;opacity:1;order:0;outline:rgb(0, 0, 0) none 0px;perspective:none;perspective-origin:0px 0px;pointer-events:none;ruby-align:space-around;ruby-position:over;shape-rendering:auto;stroke:none;stroke-dasharray:none;stroke-dashoffset:0px;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-width:1px;table-layout:auto;text-align:left;text-align-last:auto;text-anchor:start;text-emphasis-position:over;text-overflow:clip;text-rendering:auto;text-shadow:none;text-underline-position:auto;text-wrap:wrap;transform-origin:0px 0px;transform-style:flat;transition:all;width:auto;word-break:break-word;writing-mode:horizontal-tb;z-index:auto;zoom:1;\"><span>****************&nbsp;</span></span><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/e0cdc0f6-fd5b-4f2e-a29b-a3022ea35e8e?fileName=image.png\" alt=Image><br> </div><div><img style=\"font-size:14.666667px;\"><br> </div><div><img style=\"font-size:14.666667px;\"><br> </div><div><img style=\"font-size:14.666667px;\"><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/7eeac2b8-2cbf-4219-bd0e-be672510e870?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><br> </div><div>PNRs: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/f33a84c7-f9f8-40b2-8c0e-70ff00329461?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/0d126bc2-21cf-426c-9992-c4ee0e3d114e?fileName=image.png\" alt=Image><br> </div><div><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:31.481481"}, "738727": {"id": 738727, "title": "********* : DF - Unable to complete the reshop scenarios", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-03T09:44:54.23Z", "changed_date": "2025-09-17T11:01:03.063Z", "priority": 1, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">We are unable to complete the reshop\nscenarios - ADD collect and Even exchange from DF due to the below error.</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">Please find the below PNR’s and Could you\nplease investigate the issues.</span> </p><p style=\"margin:0cm;font-size:10pt;font-family:Aptos, sans-serif;\"><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">EFGNPE</span> </p><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\">EFGXCS</span><br> </div><div><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c25128af-917b-439a-b761-5614b64a7f1d?fileName=image.png\" alt=Image><br></span> </div><div><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/ca290783-dcfd-4a20-bb5f-3090129b4c75?fileName=image.png\" alt=Image><br></span> </div><div><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/1b0d7931-46ba-4c0d-b083-debd85034ea6?fileName=image.png\" alt=Image><br></span> </div><div><span lang=EN-IN style=\"font-size:11.0pt;font-family:&quot;Calibri&quot;,sans-serif;color:black;\"><br></span> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:31.714991"}, "738673": {"id": 738673, "title": "VAFIN480- XCD Republic Report", "description": "<div> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">Business answers for this new report requirement and criticality:</span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">1.<span>&nbsp;</span><b style=\"box-sizing:border-box;\">Details for the report requirement&nbsp;</b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">We require this report inorder to pay Cheques through Republic bank. These are an upload and we require the report, to then upload and pay these specific suppliers.</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><font style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"></font>2.<span>&nbsp;</span><b style=\"box-sizing:border-box;\">Why the ask for it e.g. regulatory etc?:<span style=\"box-sizing:border-box;\">&nbsp;</span></b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">This isnt critical for the 1st day of payments 11Sept, but we do require this for the wk starting the 15th Sep. Otherwise without it we are unable to pay these invoice, as there is no other option to pay them</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"><b style=\"box-sizing:border-box;\">3.Criticality of the report?&nbsp;</b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">We require these reports by the 15th Sep. We start making payment on the 11th Sep and can pay these a couple of days later, but we do need it by 15th Sep.</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\">4,<b style=\"box-sizing:border-box;\"><span>&nbsp;</span>What's the impact of not having change for Week 1?</b></span><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span dir=auto style=\"box-sizing:border-box;\"><p style=\"box-sizing:border-box;\">If we dont have these reports, we are unable to pay these suppliers, until we have the reports. </p></span></span><i style=\"box-sizing:border-box;width:1px;opacity:0.001;\"></i></span></span><span style=\"box-sizing:border-box;\">​</span> </div><div><br> </div>", "work_item_type": "User Story", "state": "Technical Build", "area_path": "People and Finance Programme\\Reporting", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-03T07:37:34.217Z", "changed_date": "2025-09-11T07:03:01.163Z", "priority": 2, "tags": "New Report; Post SIT1; UAT CR", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:32.094592"}, "738672": {"id": 738672, "title": "VAFIN481- INR Citi Report", "description": "<div> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">Business answers for this new report requirement and criticality:</span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">1.<span>&nbsp;</span><b style=\"box-sizing:border-box;\">Details for the report requirement&nbsp;</b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">We require this report inorder to pay INR Cheques through Citi. These are an upload and we require the report, to then upload and pay these specific suppliers.</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><font style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"></font>2.<span>&nbsp;</span><b style=\"box-sizing:border-box;\">Why the ask for it e.g. regulatory etc?:<span style=\"box-sizing:border-box;\">&nbsp;</span></b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">This isnt critical for the 1st day of payments 11Sept, but we do require this for the wk starting the 15th Sep. Otherwise without it we are unable to pay these invoice, as there is no other option to pay them</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\"><b style=\"box-sizing:border-box;\">3.Criticality of the report?&nbsp;</b></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\">We require these reports by the 15th Sep. We start making payment on the 11th Sep and can pay these a couple of days later, but we do need it by 15th Sep.</span></span> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><br style=\"box-sizing:border-box;\">4,<b style=\"box-sizing:border-box;\"><span>&nbsp;</span>What's the impact of not having change for Week 1?</b></span><br style=\"box-sizing:border-box;\"> </div><div style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span style=\"box-sizing:border-box;\"><span dir=auto style=\"box-sizing:border-box;\"><p style=\"box-sizing:border-box;\">If we dont have these reports, we are unable to pay these suppliers, until we have the reports. </p></span></span><i style=\"box-sizing:border-box;width:1px;opacity:0.001;\"></i></span></span><span style=\"box-sizing:border-box;\">​</span> </div><div><br> </div>", "work_item_type": "User Story", "state": "Ready for SIT2", "area_path": "People and Finance Programme\\Reporting", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-03T07:32:13.313Z", "changed_date": "2025-09-12T06:39:40.793Z", "priority": 2, "tags": "New Report; Post SIT1; UAT CR", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:32.346999"}, "738595": {"id": 738595, "title": "Fidelity - new joiner and changes outbound file template changes", "description": "<ol start=1 type=1 style=\"color:rgb(36, 36, 36);font-size:15px;margin-top:0px;margin-bottom:0px;\"><li style=\"color:black;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span>It was established today that Fidelity have raised the following issues:</span> </li><ol start=1 type=1 style=\"text-align:start;margin-top:0px;margin-bottom:0px;\"><li style=\"color:black !important;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span style=\"margin:0px;\">New joiner outbound file – there are three required columns (Enrolment Status Effective Date, Enrolment Status, Enrolment Sub Status) which exist in the file, however the requirement was that they were not applicable, therefore these fields are blank and there is no logic to populate them. Fidelity also raised that there are other columns mismatches between their file and the fusion file (however this is not go live critical)</span> </li><li style=\"color:black !important;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span style=\"margin:0px;\">Changes outbound file – the three columns above are missing in fusion file which fidelity would like to add as required</span> </li> </ol><p style=\"text-align:start;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span style=\"margin:0px;color:black !important;\">&nbsp;</span> </p><p style=\"text-align:start;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span style=\"margin:0px;color:black !important;\">We agreed that for go live, for both files above VA will get the original files from fusion, manually edit the files to add data and / or columns (changes file only) and then upload to Plan Viewer.</span> </p><li style=\"color:black;font-size:12pt;font-family:Aptos, sans-serif;margin:0px;\"><span style=\"margin:0px;\"></span> </li> </ol><br><br>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T20:38:55.007Z", "changed_date": "2025-09-23T09:51:18.267Z", "priority": 2, "tags": "Cutover", "repro_steps": "", "system_info": "<div>Workaround as below for go live. </div><div>CR to be raised for longer term integration fix 19/09 </div>", "updated_at": "2025-09-30T11:53:32.660144"}, "738593": {"id": 738593, "title": "CWK restricted role: no access to learning", "description": "<div>CWK restricted role cannot see any courses during their onboarding </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T20:33:40.087Z", "changed_date": "2025-09-03T14:58:18.67Z", "priority": 2, "tags": "Cutover", "repro_steps": "", "system_info": "<div><PERSON><PERSON> investigated and found the missing privileges. Fix was applied to dev2 and regression tested. </div><div><PERSON><PERSON> applied fix to prod with <PERSON>'s approval 02/09 </div>", "updated_at": "2025-09-30T11:53:32.921806"}, "738592": {"id": 738592, "title": "NMW logic does not include payments in future months when date worked in previous month", "description": "", "work_item_type": "Bug", "state": "Active", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T20:23:26.973Z", "changed_date": "2025-09-08T12:40:36Z", "priority": 2, "tags": "Cutover", "repro_steps": "", "system_info": "<div>Workaround to be applied for september payrun by <PERSON> </div><div>Full fix into prod targeting 3rd oct </div>", "updated_at": "2025-09-30T11:53:33.165669"}, "738590": {"id": 738590, "title": "INTHCM043 - Incremental file not picking up new hires (who aren't pending workers)", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T18:20:23.463Z", "changed_date": "2025-09-03T14:54:17.61Z", "priority": 2, "tags": "Cutover", "repro_steps": "<div>When adding a future data CWK or direct employee hire, records are not picked up in the INTHCM043 incremental file. </div>", "system_info": "<div>Regression testing complete in dev2 fusion, however further testing cannot be completed on Azure and SNOW as production work is ongoing. <PERSON><PERSON> comfortable with this and sees as low risk. </div><div><PERSON> to raise risk around level of regression testing 02/09 </div><div>Hayley F to confirm risk comfortability and approval into prod </div><div><PERSON><PERSON><PERSON> to migrate into prod </div>", "updated_at": "2025-09-30T11:53:33.370635"}, "738454": {"id": 738454, "title": "Change in XML Response Mapping in Defect Report Service", "description": "", "work_item_type": "Feature", "state": "Closed", "area_path": "Engineering Transformation Programme", "assigned_to": "", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-02T15:01:28.103Z", "changed_date": "2025-09-02T15:05:52.48Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:33.564979"}, "738383": {"id": 738383, "title": "********* - DF | Staging/ TVTH / Payment / Payment reference id generation issue", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-02T11:59:05.463Z", "changed_date": "2025-09-03T09:01:18.49Z", "priority": 1, "tags": "DF; Environment-DL", "repro_steps": "<div><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">We are experiencing an issue in the\nnon-production environment with Delta Payment reference ID generation is\nfailing today. Please refer to the details below for further investigation. </span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Environment : Staging/ TVTH / Payment /\nPayment reference id generation issue.</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Issue : Payment reference id generation issue<br>\nError Message: 404 - Not Found<br>\nURL:&nbsp;<a href=\"https://protect.checkpoint.com/v2/r02/___https:/stage-snsapigateway2.vs01.vs.air4.com/ufDrjsyRfsfljrjsydA6dufDrjsyWjkjwjshjNi___.YzJlOnZpcmdpbmF0bGFudGljYWlybGluZXM6YzpvOjFjODFlNzMxNmZiYmM4ZGU0N2Q2ZDA5N2E2NzQ0NzlmOjc6ZTljYToyOTE3NzY3YjE3Mzc5ODliZjU5Yjg4NjRjMzRhYjgxMTFmZWYyMmQ2NDM3ZmE5YWI3MWNhZjc3YjMwYzIyN2Q2Omg6VDpU\" target=_blank title=\"Protected by Check Point: https://stage-snsapigateway2.vs01.vs.air4.com/...\">https://stage-snsapigateway2.vs01.vs.air4.com/paymentManagement/v1/paymentReferenceId</a><br>\nChannel : DC / WEB<br>\nPriority : Critical<br>\nImpacted : All journey testing impacted<br>\nBP : Critical<br>\nHow many times was it successful? zero success &quot;&nbsp;(edited)&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/5ddf63c5-0a5a-4fc4-906f-d2a60c1ea07f?fileName=image.png\" alt=Image><br></span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\"><br></span> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:33.810226"}, "738382": {"id": 738382, "title": "DF | Staging/ TVTH / Seat / Seat offers not coming(APP/WEB/SNAPP)", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-02T11:57:22.353Z", "changed_date": "2025-09-11T06:00:55.55Z", "priority": 2, "tags": "DF; Environment-DL; seat", "repro_steps": "<div><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">We are experiencing an issue in the non-production\nenvironment where Delta Seat is failing today.&nbsp;Please refer to the details\nbelow and raise it with the&nbsp;DL team for further investigation.</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Environment : Staging/ TVTH / Seat / Seat offers not\ncoming / </span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Issue: Seat offers are&nbsp;not coming</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Error Message: Seat offers not coming</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Channel: DC / WEB</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Priority: High</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Impacted: All journey testing was impacted for Seats</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">BP: High</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">How many times was it successful? zero success </span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:12pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Please find the attached request and response.</span> </p><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:34.035900"}, "738381": {"id": 738381, "title": "INTHCM062/070/072/058 -<PERSON><PERSON><PERSON> returned when trying to update RE from OFF to WORK", "description": "<div>Person - 016013&nbsp; </div><div><br> </div><div>Trying to update RE to show as WORK period for 18th Feb but following error returned. </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/3353506a-1148-4a17-900b-456da9e7d3f3?fileName=image.png\" alt=Image><br> </div>", "work_item_type": "Bug", "state": "Retest", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T11:28:44.52Z", "changed_date": "2025-09-08T12:00:05.667Z", "priority": 2, "tags": "HyperCare", "repro_steps": "", "system_info": "<div>The resource exception's availability needs to be updated from the backend itself, which will not give any warning or error while submitting the changes.<br> </div><div><br>Emma to confirm if the issue is fixed or not for the reported employees. </div>", "updated_at": "2025-09-30T11:53:34.271995"}, "738376": {"id": 738376, "title": "Unable to add SHPL for birth partner", "description": "<div>When trying to add SH<PERSON> for birth partner, following error is returned </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/996b4549-50cc-4617-bdc4-ec72ef4af86e?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>IF I try to then add entitlement, the following error is returned. </div><div><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/dad2a1e6-cba5-41cc-b00a-803274357adf?fileName=image.png\" alt=Image><br> </div><div><br> </div><div><br> </div><div><br> </div><div><br> </div>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-02T10:57:57.407Z", "changed_date": "2025-09-22T14:08:16.993Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "<div>We have saved the agreement with all the required data successfully and didn't face any issues. We are assuming that, it is an privilege or user account issue which is why you are not able to save or submit the record.<br> </div>", "updated_at": "2025-09-30T11:53:34.543487"}, "738317": {"id": 738317, "title": "********* Unable to create the PNR's in Axis", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON>", "created_date": "2025-09-02T09:48:33.113Z", "changed_date": "2025-09-03T09:13:56.917Z", "priority": 1, "tags": "Axis; Environment-DL", "repro_steps": "<div><p style=\"box-sizing:border-box;margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"box-sizing:border-box;font-family:Calibri, sans-serif;\">We are unable to create the PNR’s in Axis and facing the “Unknown” error.</span><span lang=EN-US style=\"box-sizing:border-box;\"></span> </p><p style=\"box-sizing:border-box;margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"box-sizing:border-box;font-family:Calibri, sans-serif;\">Could you please investigate the issue</span> </p><p style=\"box-sizing:border-box;margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"box-sizing:border-box;font-family:Calibri, sans-serif;\"><br></span> </p><p style=\"box-sizing:border-box;margin:0cm;font-size:11pt;font-family:Aptos, sans-serif;\"><span lang=EN-US style=\"box-sizing:border-box;font-family:Calibri, sans-serif;\"><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/d59d2df1-cc5b-457a-b816-0610a05366a4?fileName=image.png\" alt=Image><br></span> </p> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:34.783380"}, "738200": {"id": 738200, "title": "RM Retail/APP - Seatmap does not load", "description": "", "work_item_type": "Bug", "state": "Closed", "area_path": "Air4 Channels Testing", "assigned_to": "<PERSON>, <PERSON><PERSON><PERSON>", "created_by": "<PERSON><PERSON><PERSON><PERSON>", "created_date": "2025-09-01T15:56:58.97Z", "changed_date": "2025-09-09T09:42:08.13Z", "priority": 1, "tags": "Seats; UAT", "repro_steps": "<div>Retrieve a booking&nbsp; </div><div>Navigate to the seatmap </div><div><br> </div><div>OR start a flight booking </div><div>Select flights </div><div>Select seats </div><div><br> </div><div>Expected: Seatmap loads and user is able to select/purchase seats </div><div>Actual: User is shown an error message and seatmap doesn't load </div><div><br> </div><div>Please note: This is replicable on both iOS, Android APPs and on DotCom and on both during and post purchase flows </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/dfd6124b-0bb5-4b45-807c-d2b36d0d86f0?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/f60dcc7a-ef4d-4778-9061-2ce3b8ab9e77?fileName=image.png\" alt=Image><br> </div><div><br> </div><div>PNRs: </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/66a3c244-309d-488a-a2aa-f0ea2fc85a0e?fileName=image.png\" alt=Image><br> </div><div><img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/c2443631-92e9-4cd4-9a4e-ae4526cf4f90?fileName=image.png\" alt=Image><br> </div><div><br> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:35.061477"}, "738188": {"id": 738188, "title": "Supplier Name search is not working for the update request", "description": "<div><h3><span style=\"font-weight:normal;\">Expected Result</span> </h3><ul><li><p>The supplier name search should return the correct supplier record if it exists and is active. </p> </li> </ul><h3><span style=\"font-weight:normal;\">&nbsp;Actual Result</span> </h3><ul><li><p>No results are displayed. The search appears to fail.<img src=\"https://dev.azure.com/virginatlantic/4bfd40e5-01a7-4f0a-8aea-312bc244c457/_apis/wit/attachments/25d02763-c6a9-4d47-b9a0-0744930df7b8?fileName=image.png\" alt=Image> </p> </li> </ul><br> </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme\\New Supplier Request Power App", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON><PERSON>", "created_date": "2025-09-01T15:30:23.633Z", "changed_date": "2025-09-01T15:30:23.633Z", "priority": 2, "tags": "", "repro_steps": "<div><ol><li>Navigate to New Supplier Request. </li><li>Navigate to Update Request.&nbsp; </li><li>Search using Supplier Name.&nbsp; </li> </ol> </div>", "system_info": "", "updated_at": "2025-09-30T11:53:35.316513"}, "738087": {"id": 738087, "title": "DMPROD Recruitment - Job title not showing in UI", "description": "<ul style=\"margin-bottom:0cm;margin-top:0cm;\">\n <li style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;color:black;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Employer</span></b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\"> is displaying correctly in the\n     <b>Previous Employment</b> section.</span> </li>\n <li style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;color:black;\"><b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">Position title</span></b><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;\">, however, is missing, despite\n     being present in the source file.</span> </li> </ul><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;margin-left:36.0pt;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">&nbsp;</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">I’ve checked column <b>AB</b> in the file\nPREVIOUS_EMPLOYMENT_DMPROD_20AUG2025_v3 under DMPROD\\Previous Employment\\Final\nand can confirm the position titles were part of the load.</span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\"><br></span> </p><p style=\"margin:0cm;font-size:11pt;font-family:Calibri, sans-serif;\"><span style=\"font-family:&quot;Century Gothic&quot;,sans-serif;color:black;\">When trying to add job title directly into Oracle error message is shown (see comments)&nbsp;</span> </p>", "work_item_type": "Bug", "state": "Closed", "area_path": "People and Finance Programme", "assigned_to": "<PERSON><PERSON>", "created_by": "<PERSON>", "created_date": "2025-09-01T11:04:37.253Z", "changed_date": "2025-09-02T08:18:03.623Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:35.581316"}, "737863": {"id": 737863, "title": "Observation - Payment Currency Difference between Invoice and Supplier Master", "description": "<div><a href=\"#\" data-vss-mention=\"version:2.0,82350c61-c25b-6986-a5f2-780af3c97713\">@Suvendu Chowdhury</a>&nbsp;<a href=\"#\" data-vss-mention=\"version:2.0,5996891c-c36a-6527-ac75-23e74b80c1b7\">@Richard St<PERSON>wick</a>&nbsp;, AP Invoice load completed , we have an observation for 74 invoice (with payment method as VA_ACH , VA_WIRE , VA_BACS) the invoice payment currency is not matching with supplier master payment currency </div><div>File -&nbsp;AP_Payment_Currency_Observation_30Aug </div>", "work_item_type": "Bug", "state": "New", "area_path": "People and Finance Programme\\Data Migration", "assigned_to": "<PERSON>", "created_by": "Kishore S", "created_date": "2025-08-31T10:46:12.113Z", "changed_date": "2025-09-01T09:54:40.67Z", "priority": 2, "tags": "", "repro_steps": "", "system_info": "", "updated_at": "2025-09-30T11:53:35.856304"}}