"""
Local Vector Storage Client
A simple file-based vector storage for testing and development.
"""

import json
import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import numpy as np
from datetime import datetime

from ..models.schemas import WorkItem

logger = logging.getLogger(__name__)


class LocalVectorClient:
    """
    Local file-based vector storage client for testing and development.
    Stores work items and embeddings in JSON files.
    """
    
    def __init__(self, config):
        """Initialize the local vector client."""
        self.config = config
        self.storage_dir = Path("data/vectors")
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        self.work_items_file = self.storage_dir / "work_items.json"
        self.embeddings_file = self.storage_dir / "embeddings.json"
        
        # Load existing data
        self.work_items = self._load_work_items()
        self.embeddings = self._load_embeddings()
        
        logger.info(f"Local vector storage initialized at {self.storage_dir}")
    
    def _load_work_items(self) -> Dict[str, Dict]:
        """Load work items from file."""
        if self.work_items_file.exists():
            try:
                with open(self.work_items_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load work items: {e}")
        return {}
    
    def _load_embeddings(self) -> Dict[str, List[float]]:
        """Load embeddings from file."""
        if self.embeddings_file.exists():
            try:
                with open(self.embeddings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load embeddings: {e}")
        return {}
    
    def _save_work_items(self):
        """Save work items to file."""
        try:
            with open(self.work_items_file, 'w', encoding='utf-8') as f:
                json.dump(self.work_items, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save work items: {e}")
    
    def _save_embeddings(self):
        """Save embeddings to file."""
        try:
            with open(self.embeddings_file, 'w', encoding='utf-8') as f:
                json.dump(self.embeddings, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save embeddings: {e}")
    
    async def ensure_tables_exist(self):
        """Ensure storage directories exist (no-op for file storage)."""
        self.storage_dir.mkdir(parents=True, exist_ok=True)
        logger.info("Local vector storage directories ensured")
    
    async def upsert_work_item_with_embedding(
        self, 
        work_item: WorkItem, 
        embedding: List[float]
    ) -> None:
        """
        Upsert a work item with its embedding to local storage.
        
        Args:
            work_item: The work item to store
            embedding: The vector embedding for the work item
        """
        try:
            work_item_id = str(work_item.id)
            
            # Store work item
            self.work_items[work_item_id] = {
                "id": work_item.id,
                "title": work_item.title,
                "description": work_item.description,
                "work_item_type": work_item.work_item_type,
                "state": work_item.state,
                "area_path": work_item.area_path,
                "assigned_to": work_item.assigned_to,
                "created_by": work_item.created_by,
                "created_date": work_item.created_date,
                "changed_date": work_item.changed_date,
                "priority": work_item.priority,
                "tags": work_item.tags,
                "repro_steps": work_item.repro_steps,
                "system_info": work_item.system_info,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            # Store embedding
            self.embeddings[work_item_id] = embedding
            
            # Save to files
            self._save_work_items()
            self._save_embeddings()
            
            logger.debug(f"Stored work item {work_item_id} with embedding")
            
        except Exception as e:
            logger.error(f"Failed to upsert work item {work_item.id}: {e}")
            raise
    
    async def vector_similarity_search(
        self,
        query_vector: List[float],
        filters: Optional[Dict[str, Any]] = None,
        top: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Perform vector similarity search using cosine similarity.
        
        Args:
            query_vector: The query vector
            filters: Optional filters to apply
            top: Number of results to return
            
        Returns:
            List of similar work items with similarity scores
        """
        try:
            if not self.embeddings:
                return []
            
            query_vector = np.array(query_vector)
            similarities = []
            
            for work_item_id, embedding in self.embeddings.items():
                if work_item_id not in self.work_items:
                    continue
                
                work_item = self.work_items[work_item_id]
                
                # Apply filters if provided
                if filters:
                    skip = False
                    for key, value in filters.items():
                        if key in work_item and work_item[key] != value:
                            skip = True
                            break
                    if skip:
                        continue
                
                # Calculate cosine similarity
                embedding_vector = np.array(embedding)
                similarity = np.dot(query_vector, embedding_vector) / (
                    np.linalg.norm(query_vector) * np.linalg.norm(embedding_vector)
                )
                
                similarities.append({
                    "work_item": work_item,
                    "similarity": float(similarity),
                    "score": float(similarity)  # For compatibility
                })
            
            # Sort by similarity and return top results
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            return similarities[:top]
            
        except Exception as e:
            logger.error(f"Failed to perform vector similarity search: {e}")
            return []
    
    async def get_work_item(self, work_item_id: str) -> Optional[Dict[str, Any]]:
        """Get a work item by ID."""
        return self.work_items.get(str(work_item_id))
    
    async def delete_work_item(self, work_item_id: str) -> bool:
        """Delete a work item and its embedding."""
        try:
            work_item_id = str(work_item_id)
            
            if work_item_id in self.work_items:
                del self.work_items[work_item_id]
                self._save_work_items()
            
            if work_item_id in self.embeddings:
                del self.embeddings[work_item_id]
                self._save_embeddings()
            
            logger.debug(f"Deleted work item {work_item_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete work item {work_item_id}: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        return {
            "total_work_items": len(self.work_items),
            "total_embeddings": len(self.embeddings),
            "storage_path": str(self.storage_dir),
            "work_items_file_size": self.work_items_file.stat().st_size if self.work_items_file.exists() else 0,
            "embeddings_file_size": self.embeddings_file.stat().st_size if self.embeddings_file.exists() else 0
        }
