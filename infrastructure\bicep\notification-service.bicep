@description('Main Bicep template for Intelligent Notifications & Reminders service infrastructure')

// Parameters
@description('Environment name (dev, staging, prod)')
param environment string = 'dev'

@description('Location for all resources')
param location string = resourceGroup().location

@description('Application name prefix')
param appName string = 'qa-ai-triage'

@description('Enable PagerDuty integration')
param enablePagerDuty bool = false

@description('PagerDuty API key (will be stored in Key Vault)')
@secure()
param pagerDutyApiKey string = ''

@description('Teams webhook URL (will be stored in Key Vault)')
@secure()
param teamsWebhookUrl string = ''

@description('Azure DevOps organization URL')
param adoOrganization string = ''

@description('Azure DevOps project name')
param adoProject string = ''

@description('Azure DevOps PAT (will be stored in Key Vault)')
@secure()
param adoPat string = ''

@description('OpenAI API key (will be stored in Key Vault)')
@secure()
param openAiApiKey string = ''

@description('Tags to apply to all resources')
param tags object = {
  Environment: environment
  Application: 'QA-AI-Triage'
  Component: 'Notifications'
  ManagedBy: 'Bicep'
}

// Variables
var resourcePrefix = '${appName}-notifications-${environment}'
var functionAppName = '${resourcePrefix}-func'
var appServicePlanName = '${resourcePrefix}-plan'
var storageAccountName = replace('${resourcePrefix}storage', '-', '')
var keyVaultName = '${resourcePrefix}-kv'
var appInsightsName = '${resourcePrefix}-ai'
var logAnalyticsName = '${resourcePrefix}-logs'

// Existing resources (assumed to be created by main infrastructure)
resource existingKeyVault 'Microsoft.KeyVault/vaults@2023-07-01' existing = {
  name: keyVaultName
}

resource existingAppInsights 'Microsoft.Insights/components@2020-02-02' existing = {
  name: appInsightsName
}

resource existingStorageAccount 'Microsoft.Storage/storageAccounts@2023-01-01' existing = {
  name: storageAccountName
}

// Log Analytics Workspace for monitoring
resource logAnalyticsWorkspace 'Microsoft.OperationalInsights/workspaces@2023-09-01' = {
  name: logAnalyticsName
  location: location
  tags: tags
  properties: {
    sku: {
      name: 'PerGB2018'
    }
    retentionInDays: 30
    features: {
      enableLogAccessUsingOnlyResourcePermissions: true
    }
  }
}

// Application Insights for notification service
resource notificationAppInsights 'Microsoft.Insights/components@2020-02-02' = {
  name: '${appInsightsName}-notifications'
  location: location
  tags: tags
  kind: 'web'
  properties: {
    Application_Type: 'web'
    WorkspaceResourceId: logAnalyticsWorkspace.id
    IngestionMode: 'LogAnalytics'
    publicNetworkAccessForIngestion: 'Enabled'
    publicNetworkAccessForQuery: 'Enabled'
  }
}

// App Service Plan for Function App
resource appServicePlan 'Microsoft.Web/serverfarms@2023-01-01' = {
  name: appServicePlanName
  location: location
  tags: tags
  sku: {
    name: 'Y1'
    tier: 'Dynamic'
    size: 'Y1'
    family: 'Y'
    capacity: 0
  }
  properties: {
    computeMode: 'Dynamic'
    reserved: true
  }
  kind: 'functionapp'
}

// Function App for notification service
resource functionApp 'Microsoft.Web/sites@2023-01-01' = {
  name: functionAppName
  location: location
  tags: tags
  kind: 'functionapp,linux'
  identity: {
    type: 'SystemAssigned'
  }
  properties: {
    serverFarmId: appServicePlan.id
    reserved: true
    httpsOnly: true
    siteConfig: {
      linuxFxVersion: 'Python|3.11'
      appSettings: [
        {
          name: 'AzureWebJobsStorage'
          value: 'DefaultEndpointsProtocol=https;AccountName=${existingStorageAccount.name};AccountKey=${existingStorageAccount.listKeys().keys[0].value};EndpointSuffix=${environment().suffixes.storage}'
        }
        {
          name: 'WEBSITE_CONTENTAZUREFILECONNECTIONSTRING'
          value: 'DefaultEndpointsProtocol=https;AccountName=${existingStorageAccount.name};AccountKey=${existingStorageAccount.listKeys().keys[0].value};EndpointSuffix=${environment().suffixes.storage}'
        }
        {
          name: 'WEBSITE_CONTENTSHARE'
          value: toLower(functionAppName)
        }
        {
          name: 'FUNCTIONS_EXTENSION_VERSION'
          value: '~4'
        }
        {
          name: 'FUNCTIONS_WORKER_RUNTIME'
          value: 'python'
        }
        {
          name: 'APPLICATIONINSIGHTS_CONNECTION_STRING'
          value: notificationAppInsights.properties.ConnectionString
        }
        {
          name: 'APPINSIGHTS_INSTRUMENTATIONKEY'
          value: notificationAppInsights.properties.InstrumentationKey
        }
        // Notification-specific settings
        {
          name: 'NOTIFICATIONS_ENABLED'
          value: 'true'
        }
        {
          name: 'PAGERDUTY_ENABLED'
          value: string(enablePagerDuty)
        }
        {
          name: 'NOTIFICATION_RATE_LIMIT_PER_USER_HOUR'
          value: '5'
        }
        {
          name: 'NOTIFICATION_RATE_LIMIT_PER_CHANNEL_HOUR'
          value: '20'
        }
        {
          name: 'NOTIFICATION_RATE_LIMIT_PER_PAGERDUTY_HOUR'
          value: '10'
        }
        {
          name: 'NOTIFICATION_DEDUP_WINDOW_MINUTES'
          value: '60'
        }
        {
          name: 'NOTIFICATION_QUIET_HOURS_START'
          value: '22:00'
        }
        {
          name: 'NOTIFICATION_QUIET_HOURS_END'
          value: '08:00'
        }
        {
          name: 'NOTIFICATION_QUIET_HOURS_TIMEZONE'
          value: 'UTC'
        }
        {
          name: 'NOTIFICATION_CRITICAL_BYPASS_QUIET_HOURS'
          value: 'true'
        }
        {
          name: 'NOTIFICATION_AGING_CHECK_INTERVAL_HOURS'
          value: '4'
        }
        {
          name: 'NOTIFICATION_AUDIT_RETENTION_DAYS'
          value: '30'
        }
        {
          name: 'NOTIFICATION_MAX_PROCESSING_TIME_MS'
          value: '5000'
        }
        // Key Vault references
        {
          name: 'PAGERDUTY_API_KEY'
          value: '@Microsoft.KeyVault(VaultName=${existingKeyVault.name};SecretName=pagerduty-api-key)'
        }
        {
          name: 'TEAMS_WEBHOOK_URL'
          value: '@Microsoft.KeyVault(VaultName=${existingKeyVault.name};SecretName=teams-webhook-url)'
        }
        {
          name: 'ADO_PAT'
          value: '@Microsoft.KeyVault(VaultName=${existingKeyVault.name};SecretName=ado-pat)'
        }
        {
          name: 'OPENAI_API_KEY'
          value: '@Microsoft.KeyVault(VaultName=${existingKeyVault.name};SecretName=openai-api-key)'
        }
        // ADO configuration
        {
          name: 'ADO_ORGANIZATION'
          value: adoOrganization
        }
        {
          name: 'ADO_PROJECT'
          value: adoProject
        }
      ]
      cors: {
        allowedOrigins: [
          'https://portal.azure.com'
        ]
      }
      use32BitWorkerProcess: false
      ftpsState: 'Disabled'
      minTlsVersion: '1.2'
    }
  }
}

// Grant Function App access to Key Vault
resource keyVaultAccessPolicy 'Microsoft.KeyVault/vaults/accessPolicies@2023-07-01' = {
  name: 'add'
  parent: existingKeyVault
  properties: {
    accessPolicies: [
      {
        tenantId: subscription().tenantId
        objectId: functionApp.identity.principalId
        permissions: {
          secrets: [
            'get'
            'list'
          ]
        }
      }
    ]
  }
}

// Store secrets in Key Vault
resource pagerDutyApiKeySecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = if (enablePagerDuty && !empty(pagerDutyApiKey)) {
  name: 'pagerduty-api-key'
  parent: existingKeyVault
  properties: {
    value: pagerDutyApiKey
    attributes: {
      enabled: true
    }
  }
}

resource teamsWebhookUrlSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = if (!empty(teamsWebhookUrl)) {
  name: 'teams-webhook-url'
  parent: existingKeyVault
  properties: {
    value: teamsWebhookUrl
    attributes: {
      enabled: true
    }
  }
}

resource adoPatSecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = if (!empty(adoPat)) {
  name: 'ado-pat'
  parent: existingKeyVault
  properties: {
    value: adoPat
    attributes: {
      enabled: true
    }
  }
}

resource openAiApiKeySecret 'Microsoft.KeyVault/vaults/secrets@2023-07-01' = if (!empty(openAiApiKey)) {
  name: 'openai-api-key'
  parent: existingKeyVault
  properties: {
    value: openAiApiKey
    attributes: {
      enabled: true
    }
  }
}

// Outputs
output functionAppName string = functionApp.name
output functionAppUrl string = 'https://${functionApp.properties.defaultHostName}'
output functionAppPrincipalId string = functionApp.identity.principalId
output appInsightsConnectionString string = notificationAppInsights.properties.ConnectionString
output logAnalyticsWorkspaceId string = logAnalyticsWorkspace.id
