"""
Embedding Service
Handles text embeddings using OpenAI or local models with routing logic.
"""

import logging
from typing import List, Optional, Union, Dict, Any
import asyncio
from enum import Enum

import openai
import numpy as np

# Lazy import for sentence_transformers to avoid startup delays
_sentence_transformers = None

def _get_sentence_transformers():
    """Lazy import of sentence_transformers to avoid startup delays."""
    global _sentence_transformers
    if _sentence_transformers is None:
        from sentence_transformers import SentenceTransformer
        _sentence_transformers = SentenceTransformer
    return _sentence_transformers

from ..models.schemas import WorkItem
from ..utils.config import Config
from ..utils.text import clean_html, normalize_text
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class EmbeddingProvider(Enum):
    """Supported embedding providers."""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    SENTENCE_TRANSFORMERS = "sentence_transformers"
    E5_LARGE = "e5_large"


class EmbeddingService:
    """Service for generating text embeddings with multiple provider support."""
    
    def __init__(self, config: Config):
        self.config = config
        self.provider = EmbeddingProvider(config.get('EMBEDDING_PROVIDER', 'sentence_transformers'))
        self.model_name = config.get('EMBEDDING_MODEL', 'all-MiniLM-L6-v2')
        self.max_tokens = config.get('EMBEDDING_MAX_TOKENS', 8192)

        # Initialize the appropriate embedding client lazily
        self._client = None
        self._model = None
        self._initialized = False

    def _ensure_initialized(self):
        """Ensure the provider is initialized before use."""
        if not self._initialized:
            self._initialize_provider()
            self._initialized = True

    def _initialize_provider(self):
        """Initialize the embedding provider based on configuration."""
        try:
            if self.provider == EmbeddingProvider.OPENAI:
                self._initialize_openai()
            elif self.provider == EmbeddingProvider.AZURE_OPENAI:
                self._initialize_azure_openai()
            elif self.provider in [EmbeddingProvider.SENTENCE_TRANSFORMERS, EmbeddingProvider.E5_LARGE]:
                self._initialize_sentence_transformers()
            else:
                raise ValueError(f"Unsupported embedding provider: {self.provider}")
                
            log_structured(
                logger,
                "info",
                "Embedding service initialized",
                extra={
                    "provider": self.provider.value,
                    "model": self.model_name
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to initialize embedding provider {self.provider}: {e}")
            raise
    
    def _initialize_openai(self):
        """Initialize OpenAI embedding client."""
        api_key = self.config.get('OPENAI_API_KEY')
        if not api_key:
            raise ValueError("OPENAI_API_KEY not configured")
        
        self._client = openai.AsyncOpenAI(api_key=api_key)
        self.model_name = self.config.get('OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small')
    
    def _initialize_azure_openai(self):
        """Initialize Azure OpenAI embedding client."""
        api_key = self.config.get('AZURE_OPENAI_API_KEY')
        endpoint = self.config.get('AZURE_OPENAI_ENDPOINT')
        api_version = self.config.get('AZURE_OPENAI_API_VERSION', '2024-02-01')
        
        if not all([api_key, endpoint]):
            raise ValueError("Azure OpenAI configuration incomplete")
        
        self._client = openai.AsyncAzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version
        )
        self.model_name = self.config.get('AZURE_OPENAI_EMBEDDING_MODEL', 'text-embedding-3-small')
    
    def _initialize_sentence_transformers(self):
        """Initialize Sentence Transformers model."""
        if self.provider == EmbeddingProvider.E5_LARGE:
            self.model_name = 'intfloat/e5-large-v2'
        
        try:
            SentenceTransformer = _get_sentence_transformers()
            self._model = SentenceTransformer(self.model_name)
            log_structured(
                logger,
                "info",
                "Loaded sentence transformer model",
                extra={"model_name": self.model_name}
            )
        except Exception as e:
            logger.error(f"Failed to load sentence transformer model {self.model_name}: {e}")
            # Fallback to a smaller model
            self.model_name = 'all-MiniLM-L6-v2'
            SentenceTransformer = _get_sentence_transformers()
            self._model = SentenceTransformer(self.model_name)
            logger.warning(f"Fell back to model: {self.model_name}")
    
    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for a single text.

        Args:
            text: Input text to embed

        Returns:
            List of embedding values
        """
        self._ensure_initialized()

        try:
            # Clean and normalize text
            cleaned_text = clean_html(text)
            normalized_text = normalize_text(cleaned_text)
            
            # Truncate if too long
            if len(normalized_text) > self.max_tokens:
                normalized_text = normalized_text[:self.max_tokens]
            
            if not normalized_text.strip():
                logger.warning("Empty text provided for embedding")
                return [0.0] * self._get_embedding_dimension()
            
            # Generate embedding based on provider
            if self.provider in [EmbeddingProvider.OPENAI, EmbeddingProvider.AZURE_OPENAI]:
                return await self._embed_with_openai(normalized_text)
            else:
                return await self._embed_with_sentence_transformers(normalized_text)
                
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            # Return zero vector as fallback
            return [0.0] * self._get_embedding_dimension()
    
    async def embed_texts(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts.

        Args:
            texts: List of input texts

        Returns:
            List of embedding vectors
        """
        self._ensure_initialized()

        try:
            if self.provider in [EmbeddingProvider.OPENAI, EmbeddingProvider.AZURE_OPENAI]:
                # Process in batches for API efficiency
                batch_size = 100
                all_embeddings = []
                
                for i in range(0, len(texts), batch_size):
                    batch = texts[i:i + batch_size]
                    batch_embeddings = await self._embed_batch_with_openai(batch)
                    all_embeddings.extend(batch_embeddings)
                
                return all_embeddings
            else:
                return await self._embed_batch_with_sentence_transformers(texts)
                
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {e}")
            # Return zero vectors as fallback
            dimension = self._get_embedding_dimension()
            return [[0.0] * dimension for _ in texts]
    
    async def embed_work_item(self, work_item: WorkItem) -> List[float]:
        """
        Generate embedding for a work item by combining title and description.
        
        Args:
            work_item: Work item to embed
        
        Returns:
            Embedding vector
        """
        try:
            # Combine relevant text fields
            text_parts = []
            
            if work_item.title:
                text_parts.append(work_item.title)
            
            if work_item.description:
                text_parts.append(work_item.description)
            
            if work_item.repro_steps:
                text_parts.append(work_item.repro_steps)
            
            # Join with newlines
            combined_text = "\n".join(text_parts)
            
            if not combined_text.strip():
                logger.warning(f"No text content found for work item {work_item.id}")
                return [0.0] * self._get_embedding_dimension()
            
            embedding = await self.embed_text(combined_text)
            
            log_structured(
                logger,
                "debug",
                "Generated work item embedding",
                extra={
                    "work_item_id": work_item.id,
                    "text_length": len(combined_text),
                    "embedding_dimension": len(embedding)
                }
            )
            
            return embedding
            
        except Exception as e:
            logger.error(f"Error embedding work item {work_item.id}: {e}")
            return [0.0] * self._get_embedding_dimension()
    
    async def _embed_with_openai(self, text: str) -> List[float]:
        """Generate embedding using OpenAI API."""
        try:
            response = await self._client.embeddings.create(
                model=self.model_name,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"OpenAI embedding error: {e}")
            raise
    
    async def _embed_batch_with_openai(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts using OpenAI API."""
        try:
            # Clean and normalize texts
            cleaned_texts = [normalize_text(clean_html(text)) for text in texts]
            
            response = await self._client.embeddings.create(
                model=self.model_name,
                input=cleaned_texts
            )
            
            return [item.embedding for item in response.data]
        except Exception as e:
            logger.error(f"OpenAI batch embedding error: {e}")
            raise
    
    async def _embed_with_sentence_transformers(self, text: str) -> List[float]:
        """Generate embedding using Sentence Transformers."""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            
            def _encode():
                if self.provider == EmbeddingProvider.E5_LARGE:
                    # E5 models expect a specific prefix
                    prefixed_text = f"query: {text}"
                    return self._model.encode(prefixed_text, normalize_embeddings=True)
                else:
                    return self._model.encode(text, normalize_embeddings=True)
            
            embedding = await loop.run_in_executor(None, _encode)
            return embedding.tolist()
            
        except Exception as e:
            logger.error(f"Sentence transformer embedding error: {e}")
            raise
    
    async def _embed_batch_with_sentence_transformers(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts using Sentence Transformers."""
        try:
            # Clean and normalize texts
            cleaned_texts = [normalize_text(clean_html(text)) for text in texts]
            
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            
            def _encode_batch():
                if self.provider == EmbeddingProvider.E5_LARGE:
                    # E5 models expect a specific prefix
                    prefixed_texts = [f"query: {text}" for text in cleaned_texts]
                    return self._model.encode(prefixed_texts, normalize_embeddings=True)
                else:
                    return self._model.encode(cleaned_texts, normalize_embeddings=True)
            
            embeddings = await loop.run_in_executor(None, _encode_batch)
            return embeddings.tolist()
            
        except Exception as e:
            logger.error(f"Sentence transformer batch embedding error: {e}")
            raise
    
    def _get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings for the current model."""
        if self.provider == EmbeddingProvider.OPENAI:
            if 'text-embedding-3-small' in self.model_name:
                return 1536
            elif 'text-embedding-3-large' in self.model_name:
                return 3072
            elif 'text-embedding-ada-002' in self.model_name:
                return 1536
        elif self.provider == EmbeddingProvider.AZURE_OPENAI:
            return 1536  # Most common Azure OpenAI embedding dimension
        elif self.provider == EmbeddingProvider.E5_LARGE:
            return 1024
        else:
            # Default for sentence transformers
            return 384
        
        return 384  # Safe default
    
    def get_embedding_dimension(self) -> int:
        """Public method to get embedding dimension."""
        return self._get_embedding_dimension()
