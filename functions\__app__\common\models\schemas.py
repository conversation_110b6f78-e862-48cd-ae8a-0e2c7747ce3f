"""
Pydantic models for WorkItem, RecResult, DuplicateHit and other data structures.
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from pydantic import BaseModel, Field, validator
from enum import Enum


class WorkItemType(str, Enum):
    """Supported work item types."""
    BUG = "Bug"
    TASK = "Task"
    USER_STORY = "User Story"
    FEATURE = "Feature"
    EPIC = "Epic"
    ISSUE = "Issue"
    DEFECT = "Defect"


class WorkItemState(str, Enum):
    """Common work item states."""
    NEW = "New"
    ACTIVE = "Active"
    RESOLVED = "Resolved"
    CLOSED = "Closed"
    REMOVED = "Removed"
    IN_PROGRESS = "In Progress"
    DONE = "Done"


class Priority(int, Enum):
    """Priority levels."""
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4


class WorkItem(BaseModel):
    """Represents a work item from Azure DevOps."""
    
    id: int = Field(..., description="Work item ID")
    title: str = Field(..., description="Work item title")
    description: Optional[str] = Field(None, description="Work item description")
    work_item_type: str = Field(..., description="Type of work item")
    state: str = Field(..., description="Current state")
    project: Optional[str] = Field(None, description="Project name")
    area_path: Optional[str] = Field(None, description="Area path")
    iteration_path: Optional[str] = Field(None, description="Iteration path")
    assigned_to: Optional[str] = Field(None, description="Assigned user")
    created_by: Optional[str] = Field(None, description="Created by user")
    created_date: Optional[str] = Field(None, description="Creation date")
    changed_date: Optional[str] = Field(None, description="Last changed date")
    priority: Optional[int] = Field(2, description="Priority level")
    severity: Optional[str] = Field(None, description="Severity level")
    tags: Optional[str] = Field(None, description="Tags")
    repro_steps: Optional[str] = Field(None, description="Reproduction steps")
    system_info: Optional[str] = Field(None, description="System information")
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and v not in [1, 2, 3, 4]:
            return 2  # Default to medium priority
        return v
    
    @validator('title')
    def validate_title(cls, v):
        if not v or not v.strip():
            raise ValueError('Title cannot be empty')
        return v.strip()
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class SearchResult(BaseModel):
    """Represents a search result from Azure AI Search."""
    
    work_item_id: int = Field(..., description="Work item ID")
    title: str = Field(..., description="Work item title")
    description: Optional[str] = Field(None, description="Work item description")
    work_item_type: str = Field(..., description="Type of work item")
    area_path: Optional[str] = Field(None, description="Area path")
    score: float = Field(..., description="Search relevance score")
    reranker_score: Optional[float] = Field(None, description="Reranker score")
    highlights: Optional[Dict[str, List[str]]] = Field(None, description="Search highlights")
    
    class Config:
        use_enum_values = True


class DuplicateHit(BaseModel):
    """Represents a potential duplicate work item."""
    
    work_item_id: int = Field(..., description="Duplicate work item ID")
    title: str = Field(..., description="Duplicate work item title")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    match_reasons: List[str] = Field(default_factory=list, description="Reasons for match")
    
    @validator('similarity_score')
    def validate_similarity_score(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class TriageResult(BaseModel):
    """Represents the result of automated triage."""
    
    work_item_id: int = Field(..., description="Work item ID")
    assigned_to: Optional[str] = Field(None, description="Recommended assignee")
    priority: Optional[int] = Field(None, description="Recommended priority")
    duplicates: Optional[List[DuplicateHit]] = Field(None, description="Potential duplicates")
    confidence_score: float = Field(..., description="Overall confidence (0-1)")
    reasoning: str = Field(..., description="Explanation of triage decisions")
    processing_time_ms: Optional[float] = Field(None, description="Processing time in milliseconds")
    
    @validator('confidence_score')
    def validate_confidence_score(cls, v):
        return max(0.0, min(1.0, v))
    
    @validator('priority')
    def validate_priority(cls, v):
        if v is not None and v not in [1, 2, 3, 4]:
            return None
        return v
    
    class Config:
        use_enum_values = True


class AssignmentRecommendation(BaseModel):
    """Represents an assignment recommendation."""
    
    assignee: str = Field(..., description="Recommended assignee")
    confidence: float = Field(..., description="Confidence score (0-1)")
    reasoning: List[str] = Field(default_factory=list, description="Reasons for recommendation")
    vote_count: int = Field(0, description="Number of similar items supporting this assignment")
    load_factor: float = Field(0.0, description="Current workload factor")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class PriorityRecommendation(BaseModel):
    """Represents a priority recommendation."""
    
    priority: int = Field(..., description="Recommended priority level")
    confidence: float = Field(..., description="Confidence score (0-1)")
    factors: Dict[str, Any] = Field(default_factory=dict, description="Factors influencing priority")
    reasoning: str = Field(..., description="Explanation of priority decision")
    
    @validator('priority')
    def validate_priority(cls, v):
        if v not in [1, 2, 3, 4]:
            raise ValueError('Priority must be 1, 2, 3, or 4')
        return v
    
    @validator('confidence')
    def validate_confidence(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class TeamMember(BaseModel):
    """Represents a team member."""
    
    email: str = Field(..., description="Email address")
    display_name: str = Field(..., description="Display name")
    team: Optional[str] = Field(None, description="Team name")
    expertise_areas: List[str] = Field(default_factory=list, description="Areas of expertise")
    current_load: int = Field(0, description="Current number of assigned items")
    availability: float = Field(1.0, description="Availability factor (0-1)")
    
    @validator('availability')
    def validate_availability(cls, v):
        return max(0.0, min(1.0, v))
    
    class Config:
        use_enum_values = True


class CodeOwnership(BaseModel):
    """Represents code ownership information."""
    
    path_pattern: str = Field(..., description="File/directory path pattern")
    owners: List[str] = Field(..., description="List of owners")
    team: Optional[str] = Field(None, description="Owning team")
    
    class Config:
        use_enum_values = True


class TriageMetrics(BaseModel):
    """Represents triage performance metrics."""
    
    total_items_processed: int = Field(0, description="Total items processed")
    auto_assigned_count: int = Field(0, description="Number of items auto-assigned")
    duplicates_found: int = Field(0, description="Number of duplicates found")
    avg_confidence_score: float = Field(0.0, description="Average confidence score")
    avg_processing_time_ms: float = Field(0.0, description="Average processing time")
    accuracy_rate: Optional[float] = Field(None, description="Assignment accuracy rate")
    
    @validator('avg_confidence_score')
    def validate_avg_confidence(cls, v):
        return max(0.0, min(1.0, v))
    
    @validator('accuracy_rate')
    def validate_accuracy_rate(cls, v):
        if v is not None:
            return max(0.0, min(1.0, v))
        return v
    
    class Config:
        use_enum_values = True


class WebhookPayload(BaseModel):
    """Represents an Azure DevOps webhook payload."""
    
    subscription_id: str = Field(..., description="Subscription ID")
    notification_id: int = Field(..., description="Notification ID")
    id: str = Field(..., description="Event ID")
    event_type: str = Field(..., description="Event type")
    publisher_id: str = Field(..., description="Publisher ID")
    message: Dict[str, Any] = Field(..., description="Message content")
    detailed_message: Dict[str, Any] = Field(..., description="Detailed message")
    resource: Dict[str, Any] = Field(..., description="Resource data")
    resource_version: str = Field(..., description="Resource version")
    resource_containers: Dict[str, Any] = Field(..., description="Resource containers")
    created_date: str = Field(..., description="Creation date")
    
    class Config:
        use_enum_values = True


class EmbeddingRequest(BaseModel):
    """Represents an embedding generation request."""
    
    text: str = Field(..., description="Text to embed")
    model: Optional[str] = Field(None, description="Embedding model to use")
    normalize: bool = Field(True, description="Whether to normalize embeddings")
    
    class Config:
        use_enum_values = True


class EmbeddingResponse(BaseModel):
    """Represents an embedding generation response."""
    
    embedding: List[float] = Field(..., description="Generated embedding vector")
    dimension: int = Field(..., description="Embedding dimension")
    model: str = Field(..., description="Model used for embedding")
    
    class Config:
        use_enum_values = True
