#!/usr/bin/env python3
"""
Complete Step-by-Step Workflow Execution
========================================

This script runs the complete workflow from work item creation to final notifications.
It demonstrates the entire process:

1. Work Item Creation/Retrieval
2. Historical Analysis 
3. AI Triage & Message Generation
4. Email Notification
5. Teams Message

Usage:
    python run_complete_workflow.py --work-item 12345
    python run_complete_workflow.py --recent-items 24
"""

import asyncio
import sys
import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.workflow_orchestrator import WorkflowOrchestrator, execute_workflow_for_work_item
from __app__.common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


class WorkflowRunner:
    """Complete workflow execution runner."""
    
    def __init__(self):
        self.orchestrator = None
        
    async def initialize(self):
        """Initialize the workflow orchestrator."""
        print("🔧 Initializing Workflow Orchestrator...")
        self.orchestrator = WorkflowOrchestrator()
        await self.orchestrator.initialize()
        print("✅ Orchestrator initialized successfully!")
        
    async def run_single_workflow(self, work_item_id: str) -> Dict[str, Any]:
        """
        Run the complete workflow for a single work item.
        
        Args:
            work_item_id: ID of the work item to process
            
        Returns:
            Complete workflow results
        """
        print(f"\n🚀 STARTING COMPLETE WORKFLOW FOR WORK ITEM: {work_item_id}")
        print("=" * 80)
        
        start_time = datetime.utcnow()
        
        try:
            # Execute the complete workflow
            context = await self.orchestrator.execute_workflow(work_item_id)
            
            # Print detailed results
            self._print_step_by_step_results(context)
            
            total_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "work_item_id": work_item_id,
                "success": len(context.errors) == 0,
                "total_execution_time": total_time,
                "step_timings": context.step_timings,
                "email_sent": context.email_sent,
                "teams_sent": context.teams_sent,
                "errors": context.errors,
                "triage_result": context.triage_result.dict() if context.triage_result else None
            }
            
        except Exception as e:
            print(f"❌ Workflow execution failed: {str(e)}")
            return {
                "work_item_id": work_item_id,
                "success": False,
                "error": str(e)
            }
    
    async def run_recent_items_workflow(self, hours_back: int = 24) -> Dict[str, Any]:
        """
        Run workflow for all recent work items.
        
        Args:
            hours_back: Number of hours to look back
            
        Returns:
            Batch processing results
        """
        print(f"\n🚀 STARTING BATCH WORKFLOW FOR LAST {hours_back} HOURS")
        print("=" * 80)
        
        start_time = datetime.utcnow()
        
        try:
            # Get recent work items
            since_date = datetime.utcnow() - timedelta(hours=hours_back)
            work_items = await self.orchestrator.clients['ado'].get_work_items_since(since_date)
            
            print(f"📋 Found {len(work_items)} work items from last {hours_back} hours")
            
            results = []
            for i, work_item_data in enumerate(work_items, 1):
                work_item_id = work_item_data.get('id')
                if work_item_id:
                    print(f"\n📝 Processing item {i}/{len(work_items)}: {work_item_id}")
                    context = await self.orchestrator.execute_workflow(str(work_item_id))
                    
                    results.append({
                        "work_item_id": work_item_id,
                        "success": len(context.errors) == 0,
                        "email_sent": context.email_sent,
                        "teams_sent": context.teams_sent,
                        "errors": context.errors
                    })
                    
                    # Print brief status
                    status = "✅" if len(context.errors) == 0 else "❌"
                    print(f"   {status} Email: {'✅' if context.email_sent else '❌'} | Teams: {'✅' if context.teams_sent else '❌'}")
            
            total_time = (datetime.utcnow() - start_time).total_seconds()
            successful = len([r for r in results if r["success"]])
            
            print(f"\n📊 BATCH PROCESSING SUMMARY")
            print("-" * 40)
            print(f"Total Items: {len(results)}")
            print(f"Successful: {successful}")
            print(f"Failed: {len(results) - successful}")
            print(f"Total Time: {total_time:.2f}s")
            
            return {
                "total_items": len(results),
                "successful_items": successful,
                "failed_items": len(results) - successful,
                "total_time": total_time,
                "results": results
            }
            
        except Exception as e:
            print(f"❌ Batch workflow failed: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _print_step_by_step_results(self, context):
        """Print detailed step-by-step results."""
        print(f"\n📊 STEP-BY-STEP WORKFLOW RESULTS")
        print("=" * 60)
        
        # Step 1: Work Item Creation
        print(f"\n🔍 STEP 1: WORK ITEM CREATION/RETRIEVAL")
        print("-" * 50)
        if context.work_item:
            print(f"✅ Work Item Retrieved Successfully")
            print(f"   ID: {context.work_item.id}")
            print(f"   Title: {context.work_item.title}")
            print(f"   Type: {context.work_item.work_item_type}")
            print(f"   State: {context.work_item.state}")
            print(f"   Priority: {context.work_item.priority}")
            print(f"   Assigned To: {context.work_item.assigned_to or 'Unassigned'}")
        else:
            print(f"❌ Failed to retrieve work item")
        
        step1_time = context.step_timings.get('step_1_work_item_creation', 0)
        print(f"   ⏱️  Execution Time: {step1_time:.2f}s")
        
        # Step 2: Historical Analysis
        print(f"\n📊 STEP 2: HISTORICAL ANALYSIS & PATTERN RECOGNITION")
        print("-" * 50)
        print(f"✅ Historical Analysis Completed")
        print(f"   Similar Items Found: {len(context.historical_items)}")
        
        if context.historical_items:
            print(f"   Top Similar Items:")
            for i, item in enumerate(context.historical_items[:3], 1):
                print(f"     {i}. ID: {item.get('id')} (Similarity: {item.get('similarity_score', 0):.2f})")
        
        step2_time = context.step_timings.get('step_2_historical_analysis', 0)
        print(f"   ⏱️  Execution Time: {step2_time:.2f}s")
        
        # Step 3: AI Triage & Message Generation
        print(f"\n🤖 STEP 3: AI TRIAGE & MESSAGE GENERATION")
        print("-" * 50)
        if context.triage_result:
            print(f"✅ AI Triage Completed")
            print(f"   Assigned To: {context.triage_result.assigned_to}")
            print(f"   Priority: {context.triage_result.priority}")
            print(f"   Confidence Score: {context.triage_result.confidence_score:.2f}")
            print(f"   Duplicates Found: {len(context.triage_result.duplicates)}")
            if context.triage_result.reasoning:
                print(f"   Reasoning: {context.triage_result.reasoning[:100]}...")
        else:
            print(f"❌ AI Triage Failed")
        
        if context.ai_message:
            print(f"   AI Message Generated: ✅")
            print(f"   Message Preview: {context.ai_message[:100]}...")
        
        step3_time = context.step_timings.get('step_3_ai_triage_and_message_generation', 0)
        print(f"   ⏱️  Execution Time: {step3_time:.2f}s")
        
        # Step 4: Email Notification
        print(f"\n📧 STEP 4: EMAIL NOTIFICATION DELIVERY")
        print("-" * 50)
        if context.email_sent:
            print(f"✅ Email Notification Sent Successfully")
            print(f"   Logic App Triggered: ✅")
            print(f"   Professional Formatting: ✅")
            print(f"   Stakeholder Delivery: ✅")
        else:
            print(f"❌ Email Notification Failed")
        
        step4_time = context.step_timings.get('step_4_email_notification', 0)
        print(f"   ⏱️  Execution Time: {step4_time:.2f}s")
        
        # Step 5: Teams Message
        print(f"\n💬 STEP 5: TEAMS MESSAGE BROADCASTING")
        print("-" * 50)
        if context.teams_sent:
            print(f"✅ Teams Message Sent Successfully")
            print(f"   Adaptive Card Created: ✅")
            print(f"   Channel Delivery: ✅")
            print(f"   Interactive Elements: ✅")
        else:
            print(f"❌ Teams Message Failed")
        
        step5_time = context.step_timings.get('step_5_teams_message', 0)
        print(f"   ⏱️  Execution Time: {step5_time:.2f}s")
        
        # Overall Summary
        print(f"\n📈 OVERALL WORKFLOW SUMMARY")
        print("-" * 50)
        total_time = sum(context.step_timings.values())
        success_rate = ((5 - len(context.errors)) / 5) * 100
        
        print(f"   Total Execution Time: {total_time:.2f}s")
        print(f"   Success Rate: {success_rate:.1f}%")
        print(f"   Email Delivered: {'✅' if context.email_sent else '❌'}")
        print(f"   Teams Delivered: {'✅' if context.teams_sent else '❌'}")
        print(f"   Errors: {len(context.errors)}")
        
        if context.errors:
            print(f"\n❌ ERRORS ENCOUNTERED:")
            for i, error in enumerate(context.errors, 1):
                print(f"   {i}. {error}")


async def main():
    """Main execution function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run Complete Step-by-Step Workflow")
    parser.add_argument("--work-item", type=str, help="Process specific work item ID")
    parser.add_argument("--recent-items", type=int, help="Process items from last N hours")
    parser.add_argument("--demo", action="store_true", help="Run demo with sample data")
    
    args = parser.parse_args()
    
    if not any([args.work_item, args.recent_items, args.demo]):
        parser.print_help()
        return
    
    runner = WorkflowRunner()
    
    try:
        await runner.initialize()
        
        if args.work_item:
            result = await runner.run_single_workflow(args.work_item)
            print(f"\n🎯 Final Result: {'SUCCESS' if result['success'] else 'FAILED'}")
            
        elif args.recent_items:
            result = await runner.run_recent_items_workflow(args.recent_items)
            print(f"\n🎯 Batch Result: {result['successful_items']}/{result['total_items']} successful")
            
        elif args.demo:
            print("🎭 Running Demo Workflow...")
            # Demo with a sample work item ID
            result = await runner.run_single_workflow("demo-12345")
            print(f"\n🎯 Demo Result: {'SUCCESS' if result['success'] else 'FAILED'}")
    
    except Exception as e:
        print(f"❌ Execution failed: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
